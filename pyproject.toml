[project]
name = "mcp"
dynamic = ["version"]
description = "Model Context Protocol SDK"
readme = "README.md"
requires-python = ">=3.10"
authors = [{ name = "Anthropic, PBC." }]
maintainers = [
    { name = "<PERSON>", email = "david<PERSON>@anthropic.com" },
    { name = "<PERSON>", email = "<EMAIL>" },
]
keywords = ["git", "mcp", "llm", "automation"]
license = { text = "MIT" }
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
]
dependencies = [
    "anyio>=4.5",
    "httpx>=0.27",
    "httpx-sse>=0.4",
    "pydantic>=2.7.2,<3.0.0",
    "starlette>=0.27",
    "python-multipart>=0.0.9",
    "sse-starlette>=1.6.1",
    "pydantic-settings>=2.5.2",
    "uvicorn>=0.23.1; sys_platform != 'emscripten'",
]

[project.optional-dependencies]
rich = ["rich>=13.9.4"]
cli = ["typer>=0.12.4", "python-dotenv>=1.0.0"]
ws = ["websockets>=15.0.1"]

[project.scripts]
mcp = "mcp.cli:app [cli]"

[tool.uv]
resolution = "lowest-direct"
default-groups = ["dev", "docs"]
required-version = ">=0.7.2"

[dependency-groups]
dev = [
    "pyright>=1.1.391",
    "pytest>=8.3.4",
    "ruff>=0.8.5",
    "trio>=0.26.2",
    "pytest-flakefinder>=1.1.0",
    "pytest-xdist>=3.6.1",
    "pytest-examples>=0.0.14",
    "pytest-pretty>=1.2.0",
    "inline-snapshot>=0.23.0",
]
docs = [
    "mkdocs>=1.6.1",
    "mkdocs-glightbox>=0.4.0",
    "mkdocs-material[imaging]>=9.5.45",
    "mkdocstrings-python>=1.12.2",
]

[build-system]
requires = ["hatchling", "uv-dynamic-versioning"]
build-backend = "hatchling.build"

[tool.hatch.version]
source = "uv-dynamic-versioning"

[tool.uv-dynamic-versioning]
vcs = "git"
style = "pep440"
bump = true

[project.urls]
Homepage = "https://modelcontextprotocol.io"
Repository = "https://github.com/modelcontextprotocol/python-sdk"
Issues = "https://github.com/modelcontextprotocol/python-sdk/issues"

[tool.hatch.build.targets.wheel]
packages = ["src/mcp"]

[tool.pyright]
include = ["src/mcp", "tests", "examples/servers"]
venvPath = "."
venv = ".venv"
strict = ["src/mcp/**/*.py"]

[tool.ruff.lint]
select = ["C4", "E", "F", "I", "PERF", "UP"]
ignore = ["PERF203"]

[tool.ruff]
line-length = 120
target-version = "py310"

[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["F401"]
"tests/server/fastmcp/test_func_metadata.py" = ["E501"]

[tool.uv.workspace]
members = ["examples/servers/*"]

[tool.uv.sources]
mcp = { workspace = true }

[tool.pytest.ini_options]
log_cli = true
xfail_strict = true
addopts = """
    --color=yes
    --capture=fd
    --numprocesses auto
"""
filterwarnings = [
    "error",
    # This should be fixed on Uvicorn's side.
    "ignore::DeprecationWarning:websockets",
    "ignore:websockets.server.WebSocketServerProtocol is deprecated:DeprecationWarning",
    "ignore:Returning str or bytes.*:DeprecationWarning:mcp.server.lowlevel"
]
