# from openai import OpenAI
# url = "https://yunwu.ai/v1"
# client = OpenAI(base_url=url, api_key="sk-6oJPGtsErJX67TyOTBAr5hJ3n334zaVk8n949svyJLBovDvG")
#
# data = client.chat.completions.create(
#     model="gpt-4.1-nano-2025-04-14",
#     messages=[{"role": "user", "content": "Hello world"}],
#     temperature=0.7,
#     max_tokens=4096,
#     top_p=1,
#     stream=False,
# )
# print(data.choices[0].message.content)


from google import genai

# The client gets the API key from the environment variable `GEMINI_API_KEY`.
client = genai.Client(api_key="AIzaSyCj0xZQ5BRNrdlulyfeZtR4mMIO9Z8YyPY")

response = client.models.generate_content(
    model="gemini-2.5-flash", contents="hello"
)
print(response.text)