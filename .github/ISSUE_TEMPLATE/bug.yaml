name: 🐛 MCP Python SDK Bug
description: Report a bug or unexpected behavior in the MCP Python SDK
labels: ["need confirmation"]

body:
  - type: markdown
    attributes:
      value: Thank you for contributing to the MCP Python SDK! ✊

  - type: checkboxes
    id: checks
    attributes:
      label: Initial Checks
      description: Just making sure you're using the latest version of MCP Python SDK.
      options:
        - label: I confirm that I'm using the latest version of MCP Python SDK
          required: true
        - label: I confirm that I searched for my issue in https://github.com/modelcontextprotocol/python-sdk/issues before opening this issue
          required: true

  - type: textarea
    id: description
    attributes:
      label: Description
      description: |
        Please explain what you're seeing and what you would expect to see.

        Please provide as much detail as possible to make understanding and solving your problem as quick as possible. 🙏
    validations:
      required: true

  - type: textarea
    id: example
    attributes:
      label: Example Code
      description: >
        If applicable, please add a self-contained,
        [minimal, reproducible, example](https://stackoverflow.com/help/minimal-reproducible-example)
        demonstrating the bug.

      placeholder: |
        from mcp.server.fastmcp import FastMCP

        ...
      render: Python

  - type: textarea
    id: version
    attributes:
      label: Python & MCP Python SDK
      description: |
        Which version of Python and MCP Python SDK are you using?
      render: Text
    validations:
      required: true
