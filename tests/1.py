def read_docx_file(file_path):
    """读取Word文档文件"""
    try:
        from docx import Document
        doc = Document(file_path)
        content = []
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():  # 只添加非空段落
                content.append(paragraph.text)
        return '\n'.join(content)
    except ImportError:
        print("请先安装python-docx库: pip install python-docx")
        return None
    except Exception as e:
        print(f"读取Word文档时出错: {e}")
        return None

# 使用示例
file_path = "/Users/<USER>/Desktop/project/1/1.docx"
content = read_docx_file(file_path)
if content:
    print("文档内容:")
    print(content)
