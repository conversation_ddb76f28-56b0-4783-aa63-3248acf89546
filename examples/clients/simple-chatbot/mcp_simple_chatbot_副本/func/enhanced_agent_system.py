"""
Enhanced Multi-Agent System for MCP Tool Retrieval
增强的多Agent系统用于MCP工具检索
"""

import asyncio
import logging
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json

from langchain.chat_models import init_chat_model
from langchain.embeddings import init_embeddings
from langgraph.graph import StateGraph, MessagesState
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('enhanced_agent_system.log', encoding='utf-8')
    ]
)

# 创建专用的logger
logger = logging.getLogger('EnhancedAgentSystem')


class AgentRole(Enum):
    """Agent角色定义"""
    INTENT_CLASSIFIER = "intent_classifier"  # 意图分类器
    TOOL_RETRIEVER = "tool_retriever"       # 工具检索器
    CONTEXT_ANALYZER = "context_analyzer"   # 上下文分析器
    RESULT_RANKER = "result_ranker"         # 结果排序器
    EXECUTION_PLANNER = "execution_planner" # 执行规划器


@dataclass
class RetrievalContext:
    """增强版检索上下文"""
    query: str
    user_intent: str
    domain: str
    complexity_level: int
    historical_tools: List[str]
    session_context: Dict[str, Any]
    # 新增字段
    predicted_tool_types: List[str] = None
    key_entities: List[str] = None
    time_constraints: List[str] = None
    filter_conditions: List[str] = None
    parameters: Dict[str, Any] = None
    task_dependencies: List[str] = None


@dataclass
class ToolCandidate:
    """工具候选"""
    tool_id: str
    tool_name: str
    description: str
    similarity_score: float
    confidence: float
    reasoning: str
    match_details: Dict[str, float]


class IntentClassifierAgent:
    """增强版意图分类Agent - 全面分析用户查询意图"""

    def __init__(self, llm_model: str = "openai:gpt-4o-mini"):
        self.llm = init_chat_model(llm_model)
        self.logger = logging.getLogger(f'{__name__}.IntentClassifierAgent')
        self.system_prompt = """
        你是一个专业的意图分类器。请仔细分析用户查询，识别所有意图、功能需求和细节要求。

        ## 主要意图类型：
        - information_retrieval: 信息获取（查询、搜索、获取数据）
        - content_creation: 内容创建（上传、生成、创建文档）
        - data_processing: 数据处理（转换、计算、分析、筛选、排序）
        - system_control: 系统控制（关机、截图、窗口管理）
        - communication: 通信交互（发送通知、用户交互、提醒设置）
        - file_management: 文件管理（压缩、解压、清理）
        - navigation_travel: 导航出行（路径规划、交通查询、出行建议）
        - entertainment_lifestyle: 娱乐生活（电影、菜谱、热点）
        - automation_workflow: 自动化工作流（多步骤任务编排、定时任务）

        ## 领域分类：
        - bilibili: B站相关（视频上传、用户数据、投稿管理）
        - books: 图书信息（ISBN查询、书籍搜索）
        - system: 系统信息（硬件状态、内存、磁盘、网络）
        - news_media: 新闻媒体（热点数据、新闻查询）
        - office: 办公文档（Word、Excel、PowerPoint）
        - business: 商务信息（企业查询、汇率转换）
        - location: 位置服务（定位、地图、导航）
        - image: 图像处理（格式转换、压缩、水印）
        - network: 网络服务（IP查询、网速测试）
        - math: 数学计算（表达式计算、数值处理）
        - entertainment: 娱乐信息（电影、菜谱推荐）
        - transport: 交通出行（火车票、航班、油价、限行）
        - weather: 天气服务（实时天气、预报、预警）
        - utilities: 实用工具（二维码、截图、通知、手机号查询）
        - file_ops: 文件操作（压缩、解压、清理）
        - audio_video: 音视频处理（语音合成、音频控制、视频处理）
        - translation: 翻译服务（文本翻译、语言转换）
        - notification: 通知提醒（定时提醒、事件通知）
        - general: 通用功能

        ## 工具类型映射（请根据查询内容识别所需的具体工具类型）：
        - transport_tools: 交通查询工具（高铁、航班、公交）
        - weather_tools: 天气查询工具（实时天气、预报）
        - translation_tools: 翻译工具（文本翻译、语言识别）
        - audio_tools: 音频工具（语音合成、音频播放）
        - audio_control_tools: 音频控制工具（音量调节、播放速度）
        - notification_tools: 通知工具（提醒设置、定时通知）
        - reminder_tools: 提醒工具（时间提醒、事件提醒）
        - data_filtering_tools: 数据筛选工具（条件筛选、排序）
        - data_analysis_tools: 数据分析工具（统计、比较）
        - scheduling_tools: 调度工具（时间安排、任务规划）
        - recommendation_tools: 推荐工具（智能建议、个性化推荐）
        - file_tools: 文件工具（文件操作、格式转换）
        - system_tools: 系统工具（系统控制、状态查询）
        - office_tools: 办公工具（文档处理、表格操作）
        - bilibili_tools: B站工具（视频管理、用户数据）
        - general_tools: 通用工具

        ## 复杂度等级（1-5级）：
        1: 简单查询（获取基础信息）
        2: 单步操作（简单的创建或设置）
        3: 中等复杂（需要参数配置或多步骤）
        4: 复杂操作（需要多个工具协作或复杂逻辑）
        5: 高度复杂（需要深度分析或复杂的工作流）

        ## 分析要求：
        1. **全面识别**：识别查询中的所有功能需求，不要遗漏任何细节
        2. **实体提取**：提取所有关键实体，包括：
           - 地点、时间、人名、数量
           - 技术参数（如音量、速度、时间间隔）
           - 条件约束（如"最快"、"2小时前"）
           - 操作对象（如文件、数据、设备）
        3. **工具预测**：根据功能需求预测所需的具体工具类型
        4. **依赖关系**：识别任务间的依赖关系和执行顺序

        返回JSON格式：
        {
            "intent": "主要意图类型",
            "domain": "领域分类",
            "complexity": 复杂度等级,
            "predicted_tool_types": ["具体工具类型1", "具体工具类型2"],
            "key_entities": ["关键实体1", "关键实体2"],
            "time_constraints": ["时间约束1", "时间约束2"],
            "filter_conditions": ["筛选条件1", "筛选条件2"],
            "parameters": {"参数名": "参数值"},
            "task_dependencies": ["任务依赖关系"],
            "confidence": 置信度(0.0-1.0),
            "reasoning": "详细的分类理由说明"
        }
        """

    async def classify_intent(self, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """增强版意图分类 - 全面分析用户查询"""
        start_time = time.time()
        self.logger.info(f"开始增强意图分类 - 查询: '{query[:50]}{'...' if len(query) > 50 else ''}'")
        self.logger.debug(f"输入上下文: {json.dumps(context, ensure_ascii=False, indent=2)}")

        try:
            # 构建更详细的分析提示
            analysis_prompt = f"""
            请仔细分析以下用户查询，识别所有功能需求和细节要求：

            用户查询: {query}

            上下文信息: {json.dumps(context, ensure_ascii=False)}

            请特别注意：
            1. 识别所有动作和操作（查询、设置、筛选、提醒等）
            2. 提取所有实体（地点、时间、参数、条件等）
            3. 识别时间约束和筛选条件
            4. 预测需要的具体工具类型
            5. 分析任务间的依赖关系
            """

            messages = [
                SystemMessage(content=self.system_prompt),
                HumanMessage(content=analysis_prompt)
            ]

            self.logger.debug("调用LLM进行增强意图分类...")
            response = await self.llm.ainvoke(messages)

            try:
                result = json.loads(response.content)

                # 验证和补充结果
                result = self._validate_and_enhance_result(result, query)

                processing_time = time.time() - start_time
                self.logger.info(f"增强意图分类完成 - 耗时: {processing_time:.2f}s")
                self.logger.info(f"分类结果: 意图={result.get('intent')}, 领域={result.get('domain')}, 复杂度={result.get('complexity')}")
                self.logger.info(f"预测工具: {result.get('predicted_tool_types', [])}")
                self.logger.debug(f"完整分类结果: {json.dumps(result, ensure_ascii=False, indent=2)}")

                return result

            except json.JSONDecodeError as e:
                self.logger.warning(f"LLM响应JSON解析失败: {e}")
                self.logger.warning(f"原始响应: {response.content}")

                # 如果解析失败，使用规则回退
                fallback_result = self._rule_based_fallback(query)
                processing_time = time.time() - start_time
                self.logger.info(f"使用规则回退分类结果 - 耗时: {processing_time:.2f}s")
                return fallback_result

        except Exception as e:
            processing_time = time.time() - start_time
            self.logger.error(f"意图分类过程中发生错误: {e} - 耗时: {processing_time:.2f}s")

            # 返回增强的默认分类
            return self._get_enhanced_default_result(query)

    def _validate_and_enhance_result(self, result: Dict[str, Any], query: str) -> Dict[str, Any]:
        """验证和增强分类结果"""
        # 确保所有必需字段存在
        enhanced_result = {
            "intent": result.get("intent", "information_retrieval"),
            "domain": result.get("domain", "general"),
            "complexity": result.get("complexity", 3),
            "predicted_tool_types": result.get("predicted_tool_types", []),
            "key_entities": result.get("key_entities", []),
            "time_constraints": result.get("time_constraints", []),
            "filter_conditions": result.get("filter_conditions", []),
            "parameters": result.get("parameters", {}),
            "task_dependencies": result.get("task_dependencies", []),
            "confidence": result.get("confidence", 0.8),
            "reasoning": result.get("reasoning", "基于LLM分析的结果")
        }

        # 基于规则增强结果
        enhanced_result = self._apply_rule_based_enhancement(enhanced_result, query)

        return enhanced_result

    def _apply_rule_based_enhancement(self, result: Dict[str, Any], query: str) -> Dict[str, Any]:
        """应用基于规则的增强"""
        query_lower = query.lower()

        # 检测遗漏的工具类型
        additional_tools = []

        # 提醒/通知相关
        if any(word in query for word in ["提醒", "通知", "小时前", "分钟前", "定时"]):
            if "notification_tools" not in result["predicted_tool_types"]:
                additional_tools.append("notification_tools")
            if "reminder_tools" not in result["predicted_tool_types"]:
                additional_tools.append("reminder_tools")

        # 筛选/排序相关
        if any(word in query for word in ["筛选", "最快", "最慢", "最便宜", "排序", "过滤"]):
            if "data_filtering_tools" not in result["predicted_tool_types"]:
                additional_tools.append("data_filtering_tools")

        # 音频控制相关
        if any(word in query for word in ["音量", "速度", "朗读", "播放", "调整"]):
            if "audio_control_tools" not in result["predicted_tool_types"]:
                additional_tools.append("audio_control_tools")

        # 语音合成相关
        if any(word in query for word in ["朗读", "语音", "合成", "播放"]):
            if "audio_tools" not in result["predicted_tool_types"]:
                additional_tools.append("audio_tools")

        # 翻译相关
        if any(word in query for word in ["翻译", "中文", "英文", "专业术语"]):
            if "translation_tools" not in result["predicted_tool_types"]:
                additional_tools.append("translation_tools")

        # 添加检测到的工具类型
        result["predicted_tool_types"].extend(additional_tools)
        result["predicted_tool_types"] = list(set(result["predicted_tool_types"]))  # 去重

        # 增强实体提取
        additional_entities = self._extract_additional_entities(query)
        result["key_entities"].extend(additional_entities)
        result["key_entities"] = list(set(result["key_entities"]))  # 去重

        # 增强时间约束提取
        time_constraints = self._extract_time_constraints(query)
        result["time_constraints"].extend(time_constraints)
        result["time_constraints"] = list(set(result["time_constraints"]))  # 去重

        # 增强筛选条件提取
        filter_conditions = self._extract_filter_conditions(query)
        result["filter_conditions"].extend(filter_conditions)
        result["filter_conditions"] = list(set(result["filter_conditions"]))  # 去重

        return result

    def _extract_additional_entities(self, query: str) -> List[str]:
        """提取额外的关键实体"""
        import re
        entities = []

        # 时间表达式
        time_patterns = [
            r'(\d+)\s*小时',
            r'(\d+)\s*分钟',
            r'明天|今天|后天',
            r'上午|下午|晚上',
            r'\d{1,2}:\d{2}'
        ]

        for pattern in time_patterns:
            matches = re.findall(pattern, query)
            entities.extend([match if isinstance(match, str) else match for match in matches])

        # 形容词修饰语
        adjectives = ["最快", "最慢", "最便宜", "最贵", "最好", "最新"]
        for adj in adjectives:
            if adj in query:
                entities.append(adj)

        # 技术参数
        tech_params = ["音量", "速度", "朗读速度", "播放速度", "专业术语"]
        for param in tech_params:
            if param in query:
                entities.append(param)

        return entities

    def _extract_time_constraints(self, query: str) -> List[str]:
        """提取时间约束"""
        import re
        constraints = []

        # 时间约束模式
        patterns = [
            r'(\d+)\s*小时前',
            r'(\d+)\s*分钟前',
            r'发车前\s*(\d+)\s*小时',
            r'出发前\s*(\d+)\s*小时',
            r'提前\s*(\d+)\s*小时'
        ]

        for pattern in patterns:
            matches = re.findall(pattern, query)
            for match in matches:
                constraints.append(f"{match}小时前" if "小时" in pattern else f"{match}分钟前")

        # 绝对时间约束
        if "明天" in query:
            constraints.append("明天")
        if "今天" in query:
            constraints.append("今天")

        return constraints

    def _extract_filter_conditions(self, query: str) -> List[str]:
        """提取筛选条件"""
        conditions = []

        # 速度相关
        if "最快" in query:
            conditions.append("按速度筛选-最快")
        if "最慢" in query:
            conditions.append("按速度筛选-最慢")

        # 价格相关
        if "最便宜" in query or "最低价" in query:
            conditions.append("按价格筛选-最低")
        if "最贵" in query or "最高价" in query:
            conditions.append("按价格筛选-最高")

        # 时间相关
        if "最早" in query:
            conditions.append("按时间筛选-最早")
        if "最晚" in query:
            conditions.append("按时间筛选-最晚")

        return conditions

    def _rule_based_fallback(self, query: str) -> Dict[str, Any]:
        """基于规则的回退分类"""
        query_lower = query.lower()

        # 基础分类
        intent = "information_retrieval"
        domain = "general"
        complexity = 3
        tool_types = ["general_tools"]

        # 交通相关
        if any(word in query for word in ["高铁", "火车", "航班", "机票", "班次"]):
            domain = "transport"
            tool_types = ["transport_tools"]

        # 天气相关
        if any(word in query for word in ["天气", "气温", "预报", "降雨"]):
            if domain == "transport":
                domain = "transport_weather"  # 复合领域
            else:
                domain = "weather"
            if "weather_tools" not in tool_types:
                tool_types.append("weather_tools")

        # 翻译相关
        if any(word in query for word in ["翻译", "中文", "英文"]):
            tool_types.append("translation_tools")

        # 语音相关
        if any(word in query for word in ["朗读", "语音", "合成"]):
            tool_types.append("audio_tools")

        # 提醒相关
        if any(word in query for word in ["提醒", "通知"]):
            tool_types.append("notification_tools")
            intent = "automation_workflow"

        # 复杂度评估
        if len(tool_types) > 3:
            complexity = 5
        elif len(tool_types) > 2:
            complexity = 4

        return {
            "intent": intent,
            "domain": domain,
            "complexity": complexity,
            "predicted_tool_types": tool_types,
            "key_entities": self._extract_additional_entities(query),
            "time_constraints": self._extract_time_constraints(query),
            "filter_conditions": self._extract_filter_conditions(query),
            "parameters": {},
            "task_dependencies": [],
            "confidence": 0.6,
            "reasoning": "基于规则的回退分类"
        }

    def _get_enhanced_default_result(self, query: str) -> Dict[str, Any]:
        """获取增强的默认结果"""
        return {
            "intent": "information_retrieval",
            "domain": "general",
            "complexity": 3,
            "predicted_tool_types": ["general_tools"],
            "key_entities": [],
            "time_constraints": [],
            "filter_conditions": [],
            "parameters": {},
            "task_dependencies": [],
            "confidence": 0.3,
            "reasoning": "意图分类过程中发生异常，使用增强默认分类"
        }


class ContextAnalyzerAgent:
    """上下文分析Agent - 分析会话历史和用户偏好"""

    def __init__(self, llm_model: str = "openai:gpt-4.1-mini-2025-04-14"):
        self.llm = init_chat_model(llm_model)
        self.conversation_history = []
        self.user_preferences = {}
        self.logger = logging.getLogger(f'{__name__}.ContextAnalyzerAgent')

    async def analyze_context(self, query: str, session_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析上下文信息"""
        start_time = time.time()
        self.logger.info(f"开始上下文分析 - 查询: '{query[:50]}{'...' if len(query) > 50 else ''}'")
        self.logger.debug(f"会话数据: {json.dumps(session_data, ensure_ascii=False, indent=2)}")

        try:
            # 分析历史工具使用模式
            self.logger.debug("分析历史工具使用模式...")
            tool_usage_pattern = self._analyze_tool_usage_pattern()
            self.logger.debug(f"工具使用模式: {tool_usage_pattern}")

            # 分析用户偏好
            self.logger.debug("提取用户偏好...")
            user_preferences = self._extract_user_preferences()
            self.logger.debug(f"用户偏好: {user_preferences}")

            # 分析查询上下文依赖
            self.logger.debug("分析查询上下文依赖...")
            context_dependencies = self._analyze_context_dependencies(query)
            self.logger.debug(f"上下文依赖: {context_dependencies}")

            # 检查会话连续性
            session_continuity = self._check_session_continuity(query)
            self.logger.debug(f"会话连续性: {session_continuity}")

            result = {
                "tool_usage_pattern": tool_usage_pattern,
                "user_preferences": user_preferences,
                "context_dependencies": context_dependencies,
                "session_continuity": session_continuity
            }

            processing_time = time.time() - start_time
            self.logger.info(f"上下文分析完成 - 耗时: {processing_time:.2f}s")
            self.logger.info(f"分析结果: 工具模式数量={len(tool_usage_pattern)}, 依赖项数量={len(context_dependencies)}, 会话连续性={session_continuity}")

            return result

        except Exception as e:
            processing_time = time.time() - start_time
            self.logger.error(f"上下文分析过程中发生错误: {e} - 耗时: {processing_time:.2f}s")

            # 返回默认结果
            return {
                "tool_usage_pattern": {},
                "user_preferences": {},
                "context_dependencies": [],
                "session_continuity": False
            }
    
    def _analyze_tool_usage_pattern(self) -> Dict[str, float]:
        """分析工具使用模式"""
        self.logger.debug(f"分析最近{min(10, len(self.conversation_history))}次交互的工具使用模式")

        # 统计历史工具使用频率
        tool_frequency = {}
        recent_history = self.conversation_history[-10:]  # 最近10次交互

        for i, entry in enumerate(recent_history):
            if 'used_tools' in entry:
                self.logger.debug(f"历史记录 {i+1}: 使用工具 {entry['used_tools']}")
                for tool in entry['used_tools']:
                    tool_frequency[tool] = tool_frequency.get(tool, 0) + 1

        # 计算偏好权重
        total_usage = sum(tool_frequency.values())
        if total_usage > 0:
            pattern = {tool: count/total_usage for tool, count in tool_frequency.items()}
            self.logger.debug(f"工具使用模式计算完成: {pattern}")
            return pattern

        self.logger.debug("无历史工具使用记录")
        return {}
    
    def _extract_user_preferences(self) -> Dict[str, Any]:
        """提取用户偏好"""
        return {
            "preferred_domains": ["weather", "file_operations"],  # 示例
            "complexity_preference": "medium",
            "response_style": "detailed"
        }
    
    def _analyze_context_dependencies(self, query: str) -> List[str]:
        """分析上下文依赖"""
        dependencies = []
        
        # 检查代词引用
        pronouns = ["它", "这个", "那个", "上面的", "之前的"]
        if any(pronoun in query for pronoun in pronouns):
            dependencies.append("previous_reference")
        
        # 检查连续性指示词
        continuity_words = ["继续", "然后", "接下来", "再"]
        if any(word in query for word in continuity_words):
            dependencies.append("sequential_action")
        
        return dependencies
    
    def _check_session_continuity(self, query: str) -> bool:
        """检查会话连续性"""
        return len(self.conversation_history) > 0


class EnhancedToolRetrieverAgent:
    """增强的工具检索Agent"""

    def __init__(self, cached_store, embeddings_model):
        self.cached_store = cached_store
        self.embeddings_model = embeddings_model
        self.logger = logging.getLogger(f'{__name__}.EnhancedToolRetrieverAgent')
        self.retrieval_strategies = {
            "semantic": self._semantic_retrieval,
            "keyword": self._keyword_retrieval,
            "hybrid": self._hybrid_retrieval,
            "contextual": self._contextual_retrieval
        }

    async def retrieve_tools(self,
                           retrieval_context: RetrievalContext,
                           strategy: str = "hybrid",
                           top_k: int = 10) -> List[ToolCandidate]:
        """根据上下文检索工具"""
        start_time = time.time()
        self.logger.info(f"开始工具检索 - 策略: {strategy}, top_k: {top_k}")
        self.logger.info(f"检索查询: '{retrieval_context.query[:50]}{'...' if len(retrieval_context.query) > 50 else ''}'")
        self.logger.debug(f"检索上下文: 意图={retrieval_context.user_intent}, 领域={retrieval_context.domain}, 复杂度={retrieval_context.complexity_level}")

        try:
            if strategy not in self.retrieval_strategies:
                self.logger.warning(f"未知的检索策略 '{strategy}', 使用默认策略 'hybrid'")
                strategy = "hybrid"

            # 使用指定策略检索
            self.logger.debug(f"执行 {strategy} 检索策略...")
            candidates = await self.retrieval_strategies[strategy](retrieval_context, top_k)
            self.logger.info(f"初始检索完成 - 获得 {len(candidates)} 个候选工具")

            # 应用上下文增强
            self.logger.debug("应用上下文增强...")
            enhanced_candidates = await self._apply_context_enhancement(candidates, retrieval_context)

            processing_time = time.time() - start_time
            self.logger.info(f"工具检索完成 - 耗时: {processing_time:.2f}s, 最终候选数量: {len(enhanced_candidates)}")

            # 记录前几个候选工具的详细信息
            for i, candidate in enumerate(enhanced_candidates[:3]):
                self.logger.debug(f"候选工具 {i+1}: {candidate.tool_name} (置信度: {candidate.confidence:.3f}, 相似度: {candidate.similarity_score:.3f})")

            return enhanced_candidates

        except Exception as e:
            processing_time = time.time() - start_time
            self.logger.error(f"工具检索过程中发生错误: {e} - 耗时: {processing_time:.2f}s")
            return []
    
    async def _semantic_retrieval(self, context: RetrievalContext, top_k: int) -> List[ToolCandidate]:
        """语义检索"""
        self.logger.debug(f"执行语义检索 - 查询: '{context.query}', top_k: {top_k}")

        try:
            results = await self.cached_store.search_similar_tools(context.query, top_k)
            self.logger.debug(f"语义检索返回 {len(results)} 个结果")

            candidates = []
            for i, result in enumerate(results):
                candidate = ToolCandidate(
                    tool_id=result["tool_id"],
                    tool_name=result["tool_data"]["name"],
                    description=result["tool_data"]["description"],
                    similarity_score=result["similarity"],
                    confidence=result["similarity"],
                    reasoning="语义相似度匹配",
                    match_details=result.get("match_details", {})
                )
                candidates.append(candidate)
                self.logger.debug(f"语义检索结果 {i+1}: {candidate.tool_name} (相似度: {candidate.similarity_score:.3f})")

            self.logger.debug(f"语义检索完成 - 生成 {len(candidates)} 个候选工具")
            return candidates

        except Exception as e:
            self.logger.error(f"语义检索过程中发生错误: {e}")
            return []
    
    async def _keyword_retrieval(self, context: RetrievalContext, top_k: int) -> List[ToolCandidate]:
        """关键词检索"""
        # 实现基于关键词的检索逻辑
        # 这里简化为调用现有的混合检索
        return await self._hybrid_retrieval(context, top_k)
    
    async def _hybrid_retrieval(self, context: RetrievalContext, top_k: int) -> List[ToolCandidate]:
        """混合检索（当前实现）"""
        return await self._semantic_retrieval(context, top_k)
    
    async def _contextual_retrieval(self, context: RetrievalContext, top_k: int) -> List[ToolCandidate]:
        """上下文感知检索"""
        self.logger.debug(f"执行上下文感知检索 - top_k: {top_k}")

        try:
            # 基础检索
            self.logger.debug(f"执行基础检索，获取 {top_k * 2} 个候选...")
            base_candidates = await self._hybrid_retrieval(context, top_k * 2)
            self.logger.debug(f"基础检索完成 - 获得 {len(base_candidates)} 个基础候选")

            # 应用上下文过滤和重排序
            contextual_candidates = []
            for i, candidate in enumerate(base_candidates):
                # 计算上下文相关性
                context_relevance = self._calculate_context_relevance(candidate, context)

                # 调整置信度
                original_confidence = candidate.confidence
                candidate.confidence = candidate.similarity_score * context_relevance
                candidate.reasoning += f" | 上下文相关性: {context_relevance:.2f}"

                self.logger.debug(f"候选 {i+1} 上下文调整: {candidate.tool_name} - 原置信度: {original_confidence:.3f} -> 新置信度: {candidate.confidence:.3f}")

                contextual_candidates.append(candidate)

            # 按调整后的置信度排序
            contextual_candidates.sort(key=lambda x: x.confidence, reverse=True)
            final_candidates = contextual_candidates[:top_k]

            self.logger.debug(f"上下文感知检索完成 - 最终返回 {len(final_candidates)} 个候选")
            return final_candidates

        except Exception as e:
            self.logger.error(f"上下文感知检索过程中发生错误: {e}")
            return []
    
    def _calculate_context_relevance(self, candidate: ToolCandidate, context: RetrievalContext) -> float:
        """计算增强版上下文相关性"""
        relevance = 1.0

        # 历史工具使用偏好
        if candidate.tool_name in context.historical_tools:
            relevance *= 1.2

        # 领域匹配
        if context.domain in candidate.description.lower():
            relevance *= 1.1

        # 复杂度匹配
        if context.complexity_level <= 3 and "simple" in candidate.description.lower():
            relevance *= 1.05
        elif context.complexity_level > 3 and "advanced" in candidate.description.lower():
            relevance *= 1.05

        # 新增：预测工具类型匹配
        if context.predicted_tool_types:
            for tool_type in context.predicted_tool_types:
                # 检查工具名称或描述中是否包含预测的工具类型关键词
                tool_type_keywords = self._get_tool_type_keywords(tool_type)
                for keyword in tool_type_keywords:
                    if keyword in candidate.tool_name.lower() or keyword in candidate.description.lower():
                        relevance *= 1.15
                        break

        # 新增：关键实体匹配
        if context.key_entities:
            entity_matches = 0
            for entity in context.key_entities:
                if entity.lower() in candidate.description.lower() or entity.lower() in candidate.tool_name.lower():
                    entity_matches += 1
            if entity_matches > 0:
                relevance *= (1.0 + 0.05 * entity_matches)  # 每个匹配实体增加5%

        # 新增：功能特征匹配
        if context.time_constraints and any(word in candidate.description.lower() for word in ["time", "schedule", "remind", "notification"]):
            relevance *= 1.1

        if context.filter_conditions and any(word in candidate.description.lower() for word in ["filter", "sort", "search", "query"]):
            relevance *= 1.1

        return min(relevance, 2.5)  # 提高最大增强倍数

    def _get_tool_type_keywords(self, tool_type: str) -> List[str]:
        """获取工具类型的关键词"""
        keyword_mapping = {
            "transport_tools": ["transport", "train", "flight", "ticket", "route"],
            "weather_tools": ["weather", "forecast", "temperature", "climate"],
            "translation_tools": ["translate", "translation", "language"],
            "audio_tools": ["audio", "voice", "speech", "sound"],
            "audio_control_tools": ["volume", "speed", "control", "adjust"],
            "notification_tools": ["notification", "notify", "alert", "remind"],
            "reminder_tools": ["reminder", "remind", "schedule", "time"],
            "data_filtering_tools": ["filter", "sort", "search", "query"],
            "data_analysis_tools": ["analysis", "analyze", "statistics", "compare"],
            "scheduling_tools": ["schedule", "plan", "calendar", "time"],
            "recommendation_tools": ["recommend", "suggest", "advice"],
            "file_tools": ["file", "document", "folder"],
            "system_tools": ["system", "hardware", "memory", "disk"],
            "office_tools": ["office", "word", "excel", "document"],
            "bilibili_tools": ["bilibili", "video", "upload"],
            "general_tools": ["general", "utility", "tool"]
        }
        return keyword_mapping.get(tool_type, [tool_type.replace("_tools", "").replace("_", " ")])
    
    async def _apply_context_enhancement(self, 
                                       candidates: List[ToolCandidate], 
                                       context: RetrievalContext) -> List[ToolCandidate]:
        """应用上下文增强"""
        enhanced = []
        
        for candidate in candidates:
            # 应用历史偏好权重
            if candidate.tool_name in context.historical_tools:
                candidate.confidence *= 1.15
                candidate.reasoning += " | 历史使用偏好"
            
            # 应用领域匹配权重
            if context.domain.lower() in candidate.description.lower():
                candidate.confidence *= 1.1
                candidate.reasoning += " | 领域匹配"
            
            enhanced.append(candidate)
        
        return enhanced


class ResultRankerAgent:
    """结果排序Agent - 智能排序和过滤"""

    def __init__(self, llm_model: str = "openai:gpt-4o-mini"):
        self.llm = init_chat_model(llm_model)
        self.logger = logging.getLogger(f'{__name__}.ResultRankerAgent')

    async def rank_candidates(self,
                            candidates: List[ToolCandidate],
                            context: RetrievalContext,
                            ranking_strategy: str = "comprehensive") -> List[ToolCandidate]:
        """智能排序候选工具"""
        start_time = time.time()
        self.logger.info(f"开始结果排序 - 策略: {ranking_strategy}, 候选数量: {len(candidates)}")

        try:
            if ranking_strategy == "comprehensive":
                result = await self._comprehensive_ranking(candidates, context)
            elif ranking_strategy == "confidence_based":
                result = self._confidence_ranking(candidates)
            elif ranking_strategy == "diversity_aware":
                result = await self._diversity_aware_ranking(candidates, context)
            else:
                self.logger.warning(f"未知的排序策略 '{ranking_strategy}', 返回原始候选列表")
                result = candidates

            processing_time = time.time() - start_time
            self.logger.info(f"结果排序完成 - 耗时: {processing_time:.2f}s, 最终排序数量: {len(result)}")

            # 记录排序后的前几个结果
            for i, candidate in enumerate(result[:3]):
                self.logger.debug(f"排序结果 {i+1}: {candidate.tool_name} (最终置信度: {candidate.confidence:.3f})")

            return result

        except Exception as e:
            processing_time = time.time() - start_time
            self.logger.error(f"结果排序过程中发生错误: {e} - 耗时: {processing_time:.2f}s")
            return candidates
    
    async def _comprehensive_ranking(self,
                                   candidates: List[ToolCandidate],
                                   context: RetrievalContext) -> List[ToolCandidate]:
        """综合排序"""
        self.logger.debug(f"执行综合排序 - 候选数量: {len(candidates)}")

        try:
            # 计算综合得分
            for i, candidate in enumerate(candidates):
                original_confidence = candidate.confidence
                comprehensive_score = self._calculate_comprehensive_score(candidate, context)
                candidate.confidence = comprehensive_score

                self.logger.debug(f"候选 {i+1} 综合评分: {candidate.tool_name} - 原置信度: {original_confidence:.3f} -> 综合得分: {comprehensive_score:.3f}")

            # 排序
            candidates.sort(key=lambda x: x.confidence, reverse=True)
            self.logger.debug("综合排序完成")

            return candidates

        except Exception as e:
            self.logger.error(f"综合排序过程中发生错误: {e}")
            return candidates
    
    def _calculate_comprehensive_score(self, 
                                     candidate: ToolCandidate, 
                                     context: RetrievalContext) -> float:
        """计算综合得分"""
        # 基础相似度权重 (40%)
        base_score = candidate.similarity_score * 0.4
        
        # 上下文相关性权重 (30%)
        context_score = self._calculate_context_score(candidate, context) * 0.3
        
        # 工具质量权重 (20%)
        quality_score = self._calculate_quality_score(candidate) * 0.2
        
        # 用户偏好权重 (10%)
        preference_score = self._calculate_preference_score(candidate, context) * 0.1
        
        return base_score + context_score + quality_score + preference_score
    
    def _calculate_context_score(self, candidate: ToolCandidate, context: RetrievalContext) -> float:
        """计算上下文得分"""
        score = 0.5  # 基础分
        
        # 领域匹配
        if context.domain.lower() in candidate.description.lower():
            score += 0.3
        
        # 复杂度匹配
        if context.complexity_level <= 3:
            if any(word in candidate.description.lower() for word in ["simple", "basic", "easy"]):
                score += 0.2
        else:
            if any(word in candidate.description.lower() for word in ["advanced", "complex", "detailed"]):
                score += 0.2
        
        return min(score, 1.0)
    
    def _calculate_quality_score(self, candidate: ToolCandidate) -> float:
        """计算工具质量得分"""
        score = 0.5  # 基础分
        
        # 描述完整性
        if len(candidate.description) > 50:
            score += 0.2
        
        # 工具名称清晰度
        if "_" in candidate.tool_name or "-" in candidate.tool_name:
            score += 0.1
        
        # 功能明确性
        action_words = ["get", "set", "create", "delete", "update", "search", "send", "receive"]
        if any(word in candidate.tool_name.lower() for word in action_words):
            score += 0.2
        
        return min(score, 1.0)
    
    def _calculate_preference_score(self, candidate: ToolCandidate, context: RetrievalContext) -> float:
        """计算用户偏好得分"""
        if candidate.tool_name in context.historical_tools:
            return 1.0
        return 0.5
    
    def _confidence_ranking(self, candidates: List[ToolCandidate]) -> List[ToolCandidate]:
        """基于置信度排序"""
        return sorted(candidates, key=lambda x: x.confidence, reverse=True)
    
    async def _diversity_aware_ranking(self, 
                                     candidates: List[ToolCandidate],
                                     context: RetrievalContext) -> List[ToolCandidate]:
        """多样性感知排序"""
        if not candidates:
            return candidates
        
        # 按功能类型分组
        function_groups = {}
        for candidate in candidates:
            func_type = self._extract_function_type(candidate.tool_name)
            if func_type not in function_groups:
                function_groups[func_type] = []
            function_groups[func_type].append(candidate)
        
        # 从每个组中选择最佳候选
        diverse_candidates = []
        max_per_group = max(1, len(candidates) // len(function_groups))
        
        for group_candidates in function_groups.values():
            group_candidates.sort(key=lambda x: x.confidence, reverse=True)
            diverse_candidates.extend(group_candidates[:max_per_group])
        
        # 按置信度最终排序
        diverse_candidates.sort(key=lambda x: x.confidence, reverse=True)
        return diverse_candidates
    
    def _extract_function_type(self, tool_name: str) -> str:
        """提取工具功能类型"""
        name_lower = tool_name.lower()
        
        if any(word in name_lower for word in ["get", "fetch", "retrieve", "read"]):
            return "retrieval"
        elif any(word in name_lower for word in ["set", "create", "add", "insert"]):
            return "creation"
        elif any(word in name_lower for word in ["update", "modify", "edit", "change"]):
            return "modification"
        elif any(word in name_lower for word in ["delete", "remove", "clear"]):
            return "deletion"
        elif any(word in name_lower for word in ["search", "find", "query"]):
            return "search"
        elif any(word in name_lower for word in ["send", "post", "submit"]):
            return "communication"
        else:
            return "general"


class MultiAgentRetrievalSystem:
    """多Agent检索系统协调器"""

    def __init__(self, cached_store, embeddings_model):
        self.intent_classifier = IntentClassifierAgent()
        self.context_analyzer = ContextAnalyzerAgent()
        self.tool_retriever = EnhancedToolRetrieverAgent(cached_store, embeddings_model)
        self.result_ranker = ResultRankerAgent()

        self.session_data = {}
        self.logger = logging.getLogger(f'{__name__}.MultiAgentRetrievalSystem')
    
    async def retrieve_tools_with_agents(self,
                                       query: str,
                                       session_id: str = "default",
                                       top_k: int = 20) -> List[ToolCandidate]:
        """使用多Agent系统检索工具"""
        start_time = time.time()
        self.logger.info("="*80)
        self.logger.info(f"开始多Agent工具检索流程")
        self.logger.info(f"查询: '{query}'")
        self.logger.info(f"会话ID: {session_id}, top_k: {top_k}")
        self.logger.info("="*80)

        try:
            # 1. 意图分类
            self.logger.info("步骤 1/6: 执行意图分类...")
            intent_result = await self.intent_classifier.classify_intent(
                query, self.session_data.get(session_id, {})
            )
            self.logger.info(f"意图分类结果: {intent_result}")

            # 2. 上下文分析
            self.logger.info("步骤 2/6: 执行上下文分析...")
            context_result = await self.context_analyzer.analyze_context(
                query, self.session_data.get(session_id, {})
            )
            self.logger.info(f"上下文分析结果: 工具模式={len(context_result.get('tool_usage_pattern', {}))}, 依赖项={len(context_result.get('context_dependencies', []))}")

            # 3. 构建增强检索上下文
            self.logger.info("步骤 3/6: 构建增强检索上下文...")
            historical_tools = list(context_result.get("tool_usage_pattern", {}).keys())
            retrieval_context = RetrievalContext(
                query=query,
                user_intent=intent_result["intent"],
                domain=intent_result["domain"],
                complexity_level=intent_result["complexity"],
                historical_tools=historical_tools,
                session_context=self.session_data.get(session_id, {}),
                # 新增的增强字段
                predicted_tool_types=intent_result.get("predicted_tool_types", []),
                key_entities=intent_result.get("key_entities", []),
                time_constraints=intent_result.get("time_constraints", []),
                filter_conditions=intent_result.get("filter_conditions", []),
                parameters=intent_result.get("parameters", {}),
                task_dependencies=intent_result.get("task_dependencies", [])
            )
            self.logger.info(f"增强检索上下文构建完成: 意图={retrieval_context.user_intent}, 领域={retrieval_context.domain}, 复杂度={retrieval_context.complexity_level}")
            self.logger.info(f"预测工具类型: {retrieval_context.predicted_tool_types}")
            self.logger.info(f"关键实体: {retrieval_context.key_entities}")
            self.logger.info(f"时间约束: {retrieval_context.time_constraints}")
            self.logger.info(f"筛选条件: {retrieval_context.filter_conditions}")
            self.logger.debug(f"历史工具: {historical_tools}")
            self.logger.debug(f"任务依赖: {retrieval_context.task_dependencies}")

            # 4. 工具检索
            self.logger.info("步骤 4/6: 执行工具检索...")
            candidates = await self.tool_retriever.retrieve_tools(
                retrieval_context, strategy="contextual", top_k=top_k * 2
            )
            self.logger.info(f"工具检索完成 - 获得 {len(candidates)} 个候选工具")

            # 5. 结果排序
            self.logger.info("步骤 5/6: 执行结果排序...")
            ranked_candidates = await self.result_ranker.rank_candidates(
                candidates, retrieval_context, ranking_strategy="comprehensive"
            )
            self.logger.info(f"结果排序完成 - 排序后候选数量: {len(ranked_candidates)}")

            # 6. 更新会话数据
            self.logger.info("步骤 6/6: 更新会话数据...")
            final_candidates = ranked_candidates[:top_k]
            self._update_session_data(session_id, query, final_candidates)

            total_time = time.time() - start_time
            self.logger.info("="*80)
            self.logger.info(f"多Agent工具检索流程完成!")
            self.logger.info(f"总耗时: {total_time:.2f}s")
            self.logger.info(f"最终返回 {len(final_candidates)} 个工具:")
            for i, candidate in enumerate(final_candidates):
                self.logger.info(f"  {i+1}. {candidate.tool_name} (置信度: {candidate.confidence:.3f})")
            self.logger.info("="*80)

            return final_candidates

        except Exception as e:
            total_time = time.time() - start_time
            self.logger.error("="*80)
            self.logger.error(f"多Agent工具检索流程发生错误: {e}")
            self.logger.error(f"错误发生时间: {total_time:.2f}s")
            self.logger.error("="*80)
            return []
    
    def _update_session_data(self, session_id: str, query: str, selected_tools: List[ToolCandidate]):
        """更新会话数据"""
        self.logger.debug(f"更新会话数据 - 会话ID: {session_id}")

        try:
            if session_id not in self.session_data:
                self.session_data[session_id] = {"history": [], "tool_usage": {}}
                self.logger.debug(f"创建新会话数据: {session_id}")

            # 记录查询历史
            history_entry = {
                "query": query,
                "selected_tools": [tool.tool_name for tool in selected_tools],
                "timestamp": asyncio.get_event_loop().time()
            }
            self.session_data[session_id]["history"].append(history_entry)
            self.logger.debug(f"添加历史记录: {history_entry}")

            # 更新工具使用统计
            for tool in selected_tools:
                tool_name = tool.tool_name
                if tool_name not in self.session_data[session_id]["tool_usage"]:
                    self.session_data[session_id]["tool_usage"][tool_name] = 0
                self.session_data[session_id]["tool_usage"][tool_name] += 1
                self.logger.debug(f"更新工具使用统计: {tool_name} -> {self.session_data[session_id]['tool_usage'][tool_name]}")

            # 保持历史记录在合理范围内
            history_count = len(self.session_data[session_id]["history"])
            if history_count > 50:
                self.session_data[session_id]["history"] = self.session_data[session_id]["history"][-50:]
                self.logger.debug(f"历史记录超过50条，截取最近50条 (原有{history_count}条)")

            self.logger.debug(f"会话数据更新完成 - 历史记录数: {len(self.session_data[session_id]['history'])}, 工具使用统计数: {len(self.session_data[session_id]['tool_usage'])}")

        except Exception as e:
            self.logger.error(f"更新会话数据时发生错误: {e}")
