#!/usr/bin/env python3
"""
具体检索策略实现
Concrete Retrieval Strategy Implementations
"""

import asyncio
import logging
import time
from typing import List, Dict, Any, Optional
import concurrent.futures

from .unified_retrieval_framework import (
    BaseRetrievalStrategy, RetrievalContext, RetrievalResult,
    with_retry, with_timeout, RetryConfig, RetrievalError, ErrorType
)

logger = logging.getLogger('ConcreteRetrievalStrategies')


class HybridRetrievalStrategy(BaseRetrievalStrategy):
    """混合检索策略"""
    
    def __init__(self, cached_store):
        super().__init__("hybrid")
        self.cached_store = cached_store
        self.retry_config = RetryConfig(max_retries=2, base_delay=0.5)
    
    @with_retry(RetryConfig(max_retries=2, base_delay=0.5))
    @with_timeout(10.0)
    async def retrieve(self, context: RetrievalContext) -> List[RetrievalResult]:
        """执行混合检索"""
        try:
            logger.info(f"Executing hybrid retrieval for: {context.query[:50]}...")
            
            # 使用现有的混合相似度搜索
            similar_tools = await self.cached_store.search_similar_tools(
                context.query, context.top_k
            )
            
            results = []
            for tool_info in similar_tools:
                result = RetrievalResult(
                    tool_id=tool_info["tool_id"],
                    tool_name=tool_info["tool_data"]["name"],
                    description=tool_info["tool_data"]["description"],
                    confidence=tool_info["similarity"],
                    strategy_used="hybrid",
                    reasoning=f"Hybrid similarity: {tool_info['similarity']:.3f}",
                    metadata={
                        "match_details": tool_info.get("match_details", {}),
                        "semantic_score": tool_info.get("match_details", {}).get("semantic_score", 0),
                        "keyword_score": tool_info.get("match_details", {}).get("keyword_score", 0),
                        "name_match_score": tool_info.get("match_details", {}).get("name_match_score", 0)
                    }
                )
                results.append(result)
            
            logger.info(f"Hybrid retrieval completed: {len(results)} results")
            return results
            
        except Exception as e:
            logger.error(f"Hybrid retrieval failed: {e}")
            raise RetrievalError(f"Hybrid retrieval failed: {e}", ErrorType.UNKNOWN_ERROR, e)


class AgentBasedRetrievalStrategy(BaseRetrievalStrategy):
    """基于Agent的检索策略"""
    
    def __init__(self, multi_agent_system):
        super().__init__("agent_based")
        self.multi_agent_system = multi_agent_system
        self.retry_config = RetryConfig(max_retries=1, base_delay=1.0)
    
    @with_retry(RetryConfig(max_retries=1, base_delay=1.0))
    @with_timeout(15.0)
    async def retrieve(self, context: RetrievalContext) -> List[RetrievalResult]:
        """执行基于Agent的检索"""
        if not self.multi_agent_system:
            raise RetrievalError("Multi-agent system not available", ErrorType.RESOURCE_ERROR)
        
        try:
            logger.info(f"Executing agent-based retrieval for: {context.query[:50]}...")
            
            candidates = await self.multi_agent_system.retrieve_tools_with_agents(
                context.query, context.user_id, top_k=context.top_k
            )
            
            results = []
            for candidate in candidates:
                result = RetrievalResult(
                    tool_id=candidate.tool_id,
                    tool_name=candidate.tool_name,
                    description=candidate.description,
                    confidence=candidate.confidence,
                    strategy_used="agent_based",
                    reasoning=candidate.reasoning,
                    metadata={
                        "agent_analysis": candidate.match_details,
                        "similarity_score": candidate.similarity_score,
                        "context_score": getattr(candidate, 'context_score', 0)
                    }
                )
                results.append(result)
            
            logger.info(f"Agent-based retrieval completed: {len(results)} results")
            return results
            
        except Exception as e:
            logger.error(f"Agent-based retrieval failed: {e}")
            raise RetrievalError(f"Agent-based retrieval failed: {e}", ErrorType.UNKNOWN_ERROR, e)


class AliyunEnhancedRetrievalStrategy(BaseRetrievalStrategy):
    """阿里云增强检索策略"""
    
    def __init__(self, aliyun_enhanced_retrieval):
        super().__init__("aliyun_enhanced")
        self.aliyun_enhanced_retrieval = aliyun_enhanced_retrieval
        self.retry_config = RetryConfig(max_retries=2, base_delay=1.0)
    
    @with_retry(RetryConfig(max_retries=2, base_delay=1.0))
    @with_timeout(20.0)
    async def retrieve(self, context: RetrievalContext) -> List[RetrievalResult]:
        """执行阿里云增强检索"""
        if not self.aliyun_enhanced_retrieval:
            raise RetrievalError("Aliyun enhanced retrieval not available", ErrorType.RESOURCE_ERROR)
        
        try:
            logger.info(f"Executing Aliyun enhanced retrieval for: {context.query[:50]}...")
            
            enhanced_results = await self.aliyun_enhanced_retrieval.enhanced_search(
                query=context.query,
                top_k=context.top_k,
                use_intent=True,
                use_rerank=True
            )
            
            results = []
            for enhanced_result in enhanced_results:
                result = RetrievalResult(
                    tool_id=enhanced_result.tool_id,
                    tool_name=enhanced_result.tool_name,
                    description=enhanced_result.description,
                    confidence=enhanced_result.final_score,
                    strategy_used="aliyun_enhanced",
                    reasoning=enhanced_result.reasoning,
                    metadata={
                        "original_similarity": enhanced_result.original_similarity,
                        "rerank_score": enhanced_result.rerank_score,
                        "intent_match": enhanced_result.intent_match,
                        "final_score": enhanced_result.final_score
                    }
                )
                results.append(result)
            
            logger.info(f"Aliyun enhanced retrieval completed: {len(results)} results")
            return results
            
        except Exception as e:
            logger.error(f"Aliyun enhanced retrieval failed: {e}")
            raise RetrievalError(f"Aliyun enhanced retrieval failed: {e}", ErrorType.API_ERROR, e)


class EnsembleRetrievalStrategy(BaseRetrievalStrategy):
    """集成检索策略"""
    
    def __init__(self, strategies: List[BaseRetrievalStrategy], weights: Optional[List[float]] = None):
        super().__init__("ensemble")
        self.strategies = strategies
        self.weights = weights or [1.0] * len(strategies)
        self.parallel_executor = concurrent.futures.ThreadPoolExecutor(max_workers=len(strategies))
    
    @with_timeout(25.0)
    async def retrieve(self, context: RetrievalContext) -> List[RetrievalResult]:
        """执行集成检索"""
        if not self.strategies:
            raise RetrievalError("No strategies available for ensemble", ErrorType.RESOURCE_ERROR)
        
        try:
            logger.info(f"Executing ensemble retrieval with {len(self.strategies)} strategies")
            
            # 并行执行所有策略
            tasks = []
            for strategy in self.strategies:
                task = asyncio.create_task(self._safe_retrieve(strategy, context))
                tasks.append(task)
            
            # 等待所有任务完成
            strategy_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 合并结果
            all_results = []
            successful_strategies = 0
            
            for i, result in enumerate(strategy_results):
                if isinstance(result, Exception):
                    logger.warning(f"Strategy {self.strategies[i].name} failed: {result}")
                    continue
                
                successful_strategies += 1
                # 应用权重
                weight = self.weights[i]
                for res in result:
                    res.confidence *= weight
                    res.metadata["ensemble_weight"] = weight
                    res.metadata["source_strategy"] = self.strategies[i].name
                
                all_results.extend(result)
            
            if successful_strategies == 0:
                raise RetrievalError("All ensemble strategies failed", ErrorType.UNKNOWN_ERROR)
            
            # 去重和排序
            unique_results = self._deduplicate_results(all_results)
            sorted_results = sorted(unique_results, key=lambda x: x.confidence, reverse=True)
            
            # 返回top_k结果
            final_results = sorted_results[:context.top_k]
            
            logger.info(f"Ensemble retrieval completed: {len(final_results)} results from {successful_strategies} strategies")
            return final_results
            
        except Exception as e:
            logger.error(f"Ensemble retrieval failed: {e}")
            raise RetrievalError(f"Ensemble retrieval failed: {e}", ErrorType.UNKNOWN_ERROR, e)
    
    async def _safe_retrieve(self, strategy: BaseRetrievalStrategy, context: RetrievalContext) -> List[RetrievalResult]:
        """安全执行单个策略"""
        try:
            return await strategy.retrieve(context)
        except Exception as e:
            logger.warning(f"Strategy {strategy.name} failed in ensemble: {e}")
            return []
    
    def _deduplicate_results(self, results: List[RetrievalResult]) -> List[RetrievalResult]:
        """去重结果"""
        seen_tools = {}
        unique_results = []
        
        for result in results:
            if result.tool_id not in seen_tools:
                seen_tools[result.tool_id] = result
                unique_results.append(result)
            else:
                # 如果已存在，选择置信度更高的
                existing = seen_tools[result.tool_id]
                if result.confidence > existing.confidence:
                    # 替换现有结果
                    unique_results.remove(existing)
                    unique_results.append(result)
                    seen_tools[result.tool_id] = result
        
        return unique_results


class AdaptiveRetrievalStrategy(BaseRetrievalStrategy):
    """自适应检索策略"""
    
    def __init__(self, primary_strategies: List[BaseRetrievalStrategy]):
        super().__init__("adaptive")
        self.primary_strategies = primary_strategies
        self.performance_history = {}
        self.adaptation_threshold = 0.1
    
    async def retrieve(self, context: RetrievalContext) -> List[RetrievalResult]:
        """执行自适应检索"""
        try:
            # 选择当前最佳策略
            best_strategy = self._select_best_strategy(context)
            
            logger.info(f"Adaptive strategy selected: {best_strategy.name}")
            
            # 执行选中的策略
            start_time = time.time()
            results = await best_strategy.retrieve(context)
            execution_time = time.time() - start_time
            
            # 计算平均置信度
            avg_confidence = sum(r.confidence for r in results) / max(len(results), 1) if results else 0.0

            # 更新性能历史
            self._update_performance_history(best_strategy.name, execution_time, len(results) > 0, avg_confidence)
            
            # 标记结果来源
            for result in results:
                result.metadata["adaptive_selected_strategy"] = best_strategy.name
                result.metadata["adaptive_execution_time"] = execution_time
            
            return results
            
        except Exception as e:
            logger.error(f"Adaptive retrieval failed: {e}")
            raise RetrievalError(f"Adaptive retrieval failed: {e}", ErrorType.UNKNOWN_ERROR, e)
    
    def _select_best_strategy(self, context: RetrievalContext) -> BaseRetrievalStrategy:
        """选择最佳策略"""
        if not self.performance_history:
            # 如果没有历史数据，返回第一个策略
            return self.primary_strategies[0]
        
        best_strategy = None
        best_score = -1
        
        for strategy in self.primary_strategies:
            if strategy.name in self.performance_history:
                history = self.performance_history[strategy.name]
                # 计算综合得分
                score = (
                    history["success_rate"] * 0.6 +
                    (1 / max(history["avg_time"], 0.001)) * 0.4
                )
                
                if score > best_score:
                    best_score = score
                    best_strategy = strategy
        
        return best_strategy or self.primary_strategies[0]
    
    def _update_performance_history(self, strategy_name: str, execution_time: float, success: bool, confidence_score: float = 0.0):
        """更新性能历史"""
        if strategy_name not in self.performance_history:
            self.performance_history[strategy_name] = {
                "total_calls": 0,
                "successful_calls": 0,
                "total_time": 0.0,
                "total_confidence": 0.0,
                "success_rate": 0.0,
                "avg_time": 0.0,
                "avg_confidence": 0.0
            }

        history = self.performance_history[strategy_name]
        history["total_calls"] += 1
        history["total_time"] += execution_time
        history["total_confidence"] += confidence_score

        if success:
            history["successful_calls"] += 1

        history["success_rate"] = history["successful_calls"] / history["total_calls"]
        history["avg_time"] = history["total_time"] / history["total_calls"]
        history["avg_confidence"] = history["total_confidence"] / history["total_calls"]
