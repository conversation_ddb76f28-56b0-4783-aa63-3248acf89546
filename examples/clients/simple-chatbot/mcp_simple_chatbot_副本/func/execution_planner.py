"""
智能工具执行规划器
Intelligent Tool Execution Planner
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json

from langchain.chat_models import init_chat_model
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage


class ExecutionStrategy(Enum):
    """执行策略"""
    SEQUENTIAL = "sequential"      # 顺序执行
    PARALLEL = "parallel"         # 并行执行
    CONDITIONAL = "conditional"   # 条件执行
    PIPELINE = "pipeline"         # 流水线执行


@dataclass
class ExecutionStep:
    """执行步骤"""
    step_id: str
    tool_name: str
    parameters: Dict[str, Any]
    dependencies: List[str]
    expected_output_type: str
    timeout: int = 30
    retry_count: int = 3


@dataclass
class ExecutionPlan:
    """执行计划"""
    plan_id: str
    query: str
    strategy: ExecutionStrategy
    steps: List[ExecutionStep]
    estimated_duration: int
    confidence: float
    reasoning: str


class ExecutionPlannerAgent:
    """执行规划Agent"""
    
    def __init__(self, llm_model: str = "openai:gpt-4o-mini"):
        self.llm = init_chat_model(llm_model)
        self.system_prompt = """
        你是一个智能的工具执行规划器。根据用户查询和可用工具，制定最优的执行计划。

        分析要点：
        1. 工具之间的依赖关系
        2. 数据流向和转换需求
        3. 执行效率和并行化可能性
        4. 错误处理和回退策略
        5. 资源使用优化

        返回JSON格式的执行计划：
        {
            "strategy": "执行策略",
            "steps": [
                {
                    "step_id": "步骤ID",
                    "tool_name": "工具名称",
                    "parameters": {"参数": "值"},
                    "dependencies": ["依赖步骤ID"],
                    "expected_output_type": "预期输出类型"
                }
            ],
            "estimated_duration": 预估时长秒,
            "confidence": 置信度0-1,
            "reasoning": "规划理由"
        }
        """
    
    async def create_execution_plan(self, 
                                  query: str, 
                                  selected_tools: List[Dict[str, Any]],
                                  context: Dict[str, Any] = None) -> ExecutionPlan:
        """创建执行计划"""
        
        # 构建工具信息
        tools_info = []
        for tool in selected_tools:
            tools_info.append({
                "name": tool.get("tool_name", ""),
                "description": tool.get("description", ""),
                "parameters": tool.get("parameters", {}),
                "confidence": tool.get("confidence", 0.0)
            })
        
        # 构建提示
        prompt = f"""
        用户查询: {query}
        
        可用工具:
        {json.dumps(tools_info, ensure_ascii=False, indent=2)}
        
        上下文信息:
        {json.dumps(context or {}, ensure_ascii=False, indent=2)}
        
        请制定最优的执行计划。
        """
        
        messages = [
            SystemMessage(content=self.system_prompt),
            HumanMessage(content=prompt)
        ]
        
        try:
            response = await self.llm.ainvoke(messages)
            plan_data = json.loads(response.content)
            
            # 构建执行步骤
            steps = []
            for i, step_data in enumerate(plan_data.get("steps", [])):
                step = ExecutionStep(
                    step_id=step_data.get("step_id", f"step_{i}"),
                    tool_name=step_data.get("tool_name", ""),
                    parameters=step_data.get("parameters", {}),
                    dependencies=step_data.get("dependencies", []),
                    expected_output_type=step_data.get("expected_output_type", "string")
                )
                steps.append(step)
            
            # 创建执行计划
            plan = ExecutionPlan(
                plan_id=f"plan_{asyncio.get_event_loop().time()}",
                query=query,
                strategy=ExecutionStrategy(plan_data.get("strategy", "sequential")),
                steps=steps,
                estimated_duration=plan_data.get("estimated_duration", 30),
                confidence=plan_data.get("confidence", 0.5),
                reasoning=plan_data.get("reasoning", "自动生成的执行计划")
            )
            
            return plan
            
        except Exception as e:
            logging.error(f"Failed to create execution plan: {e}")
            # 返回默认的顺序执行计划
            return self._create_default_plan(query, selected_tools)
    
    def _create_default_plan(self, query: str, selected_tools: List[Dict[str, Any]]) -> ExecutionPlan:
        """创建默认执行计划"""
        steps = []
        for i, tool in enumerate(selected_tools[:3]):  # 最多3个工具
            step = ExecutionStep(
                step_id=f"step_{i}",
                tool_name=tool.get("tool_name", ""),
                parameters={},
                dependencies=[f"step_{i-1}"] if i > 0 else [],
                expected_output_type="string"
            )
            steps.append(step)
        
        return ExecutionPlan(
            plan_id=f"default_plan_{asyncio.get_event_loop().time()}",
            query=query,
            strategy=ExecutionStrategy.SEQUENTIAL,
            steps=steps,
            estimated_duration=len(steps) * 10,
            confidence=0.6,
            reasoning="默认顺序执行计划"
        )


class ToolExecutor:
    """工具执行器"""
    
    def __init__(self, tool_registry: Dict[str, Any]):
        self.tool_registry = tool_registry
        self.execution_results = {}
    
    async def execute_plan(self, plan: ExecutionPlan) -> Dict[str, Any]:
        """执行计划"""
        logging.info(f"开始执行计划: {plan.plan_id}")
        logging.info(f"执行策略: {plan.strategy.value}")
        
        if plan.strategy == ExecutionStrategy.SEQUENTIAL:
            return await self._execute_sequential(plan)
        elif plan.strategy == ExecutionStrategy.PARALLEL:
            return await self._execute_parallel(plan)
        elif plan.strategy == ExecutionStrategy.PIPELINE:
            return await self._execute_pipeline(plan)
        else:
            return await self._execute_sequential(plan)
    
    async def _execute_sequential(self, plan: ExecutionPlan) -> Dict[str, Any]:
        """顺序执行"""
        results = {}
        
        for step in plan.steps:
            try:
                logging.info(f"执行步骤: {step.step_id} - {step.tool_name}")
                
                # 检查依赖
                if not self._check_dependencies(step, results):
                    logging.warning(f"步骤 {step.step_id} 依赖未满足，跳过")
                    continue
                
                # 准备参数
                parameters = self._prepare_parameters(step, results)
                
                # 执行工具
                result = await self._execute_tool(step.tool_name, parameters)
                results[step.step_id] = {
                    "tool_name": step.tool_name,
                    "result": result,
                    "status": "success"
                }
                
                logging.info(f"步骤 {step.step_id} 执行成功")
                
            except Exception as e:
                logging.error(f"步骤 {step.step_id} 执行失败: {e}")
                results[step.step_id] = {
                    "tool_name": step.tool_name,
                    "error": str(e),
                    "status": "failed"
                }
        
        return {
            "plan_id": plan.plan_id,
            "strategy": plan.strategy.value,
            "results": results,
            "overall_status": "completed"
        }
    
    async def _execute_parallel(self, plan: ExecutionPlan) -> Dict[str, Any]:
        """并行执行"""
        # 分析依赖关系，创建执行批次
        batches = self._create_execution_batches(plan.steps)
        results = {}
        
        for batch_idx, batch in enumerate(batches):
            logging.info(f"执行批次 {batch_idx + 1}: {len(batch)} 个步骤")
            
            # 并行执行当前批次
            tasks = []
            for step in batch:
                if self._check_dependencies(step, results):
                    parameters = self._prepare_parameters(step, results)
                    task = self._execute_tool_with_step_info(step, parameters)
                    tasks.append(task)
            
            # 等待批次完成
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理批次结果
            for i, result in enumerate(batch_results):
                step = batch[i]
                if isinstance(result, Exception):
                    results[step.step_id] = {
                        "tool_name": step.tool_name,
                        "error": str(result),
                        "status": "failed"
                    }
                else:
                    results[step.step_id] = {
                        "tool_name": step.tool_name,
                        "result": result,
                        "status": "success"
                    }
        
        return {
            "plan_id": plan.plan_id,
            "strategy": plan.strategy.value,
            "results": results,
            "overall_status": "completed"
        }
    
    async def _execute_pipeline(self, plan: ExecutionPlan) -> Dict[str, Any]:
        """流水线执行"""
        # 流水线执行类似于顺序执行，但会传递中间结果
        return await self._execute_sequential(plan)
    
    def _create_execution_batches(self, steps: List[ExecutionStep]) -> List[List[ExecutionStep]]:
        """创建执行批次（基于依赖关系）"""
        batches = []
        remaining_steps = steps.copy()
        completed_steps = set()
        
        while remaining_steps:
            current_batch = []
            
            # 找到可以执行的步骤（依赖已满足）
            for step in remaining_steps.copy():
                if all(dep in completed_steps for dep in step.dependencies):
                    current_batch.append(step)
                    remaining_steps.remove(step)
            
            if not current_batch:
                # 如果没有可执行的步骤，说明存在循环依赖，强制执行剩余步骤
                current_batch = remaining_steps.copy()
                remaining_steps.clear()
            
            batches.append(current_batch)
            completed_steps.update(step.step_id for step in current_batch)
        
        return batches
    
    def _check_dependencies(self, step: ExecutionStep, results: Dict[str, Any]) -> bool:
        """检查步骤依赖是否满足"""
        for dep in step.dependencies:
            if dep not in results or results[dep].get("status") != "success":
                return False
        return True
    
    def _prepare_parameters(self, step: ExecutionStep, results: Dict[str, Any]) -> Dict[str, Any]:
        """准备执行参数"""
        parameters = step.parameters.copy()
        
        # 从依赖步骤的结果中提取参数
        for dep in step.dependencies:
            if dep in results and results[dep].get("status") == "success":
                dep_result = results[dep].get("result")
                # 这里可以添加更复杂的参数映射逻辑
                if isinstance(dep_result, dict):
                    parameters.update(dep_result)
                elif isinstance(dep_result, str):
                    parameters["input"] = dep_result
        
        return parameters
    
    async def _execute_tool(self, tool_name: str, parameters: Dict[str, Any]) -> Any:
        """执行单个工具"""
        if tool_name not in self.tool_registry:
            raise ValueError(f"工具 {tool_name} 未找到")
        
        tool = self.tool_registry[tool_name]
        
        # 这里需要根据实际的工具接口调用
        # 示例实现
        try:
            if hasattr(tool, 'run'):
                result = await tool.run(parameters)
            elif callable(tool):
                result = await tool(**parameters)
            else:
                result = f"模拟执行工具 {tool_name}，参数: {parameters}"
            
            return result
        except Exception as e:
            logging.error(f"工具 {tool_name} 执行失败: {e}")
            raise
    
    async def _execute_tool_with_step_info(self, step: ExecutionStep, parameters: Dict[str, Any]) -> Any:
        """带步骤信息的工具执行"""
        return await self._execute_tool(step.tool_name, parameters)


class AdaptivePlannerAgent:
    """自适应规划Agent - 根据执行结果动态调整计划"""
    
    def __init__(self, llm_model: str = "openai:gpt-4o-mini"):
        self.llm = init_chat_model(llm_model)
        self.execution_history = []
    
    async def adapt_plan(self, 
                        original_plan: ExecutionPlan,
                        execution_results: Dict[str, Any],
                        remaining_query: str = None) -> Optional[ExecutionPlan]:
        """根据执行结果自适应调整计划"""
        
        # 分析执行结果
        failed_steps = []
        successful_steps = []
        
        for step_id, result in execution_results.get("results", {}).items():
            if result.get("status") == "failed":
                failed_steps.append(step_id)
            else:
                successful_steps.append(step_id)
        
        # 如果没有失败步骤，不需要调整
        if not failed_steps:
            return None
        
        # 分析失败原因并生成新计划
        failure_analysis = self._analyze_failures(failed_steps, execution_results)
        
        # 生成适应性计划
        adapted_plan = await self._generate_adapted_plan(
            original_plan, failure_analysis, remaining_query
        )
        
        return adapted_plan
    
    def _analyze_failures(self, failed_steps: List[str], execution_results: Dict[str, Any]) -> Dict[str, Any]:
        """分析失败原因"""
        analysis = {
            "failed_count": len(failed_steps),
            "failure_types": [],
            "common_issues": []
        }
        
        for step_id in failed_steps:
            result = execution_results["results"][step_id]
            error_msg = result.get("error", "")
            
            # 分类错误类型
            if "timeout" in error_msg.lower():
                analysis["failure_types"].append("timeout")
            elif "parameter" in error_msg.lower():
                analysis["failure_types"].append("parameter_error")
            elif "not found" in error_msg.lower():
                analysis["failure_types"].append("tool_not_found")
            else:
                analysis["failure_types"].append("unknown")
        
        return analysis
    
    async def _generate_adapted_plan(self, 
                                   original_plan: ExecutionPlan,
                                   failure_analysis: Dict[str, Any],
                                   remaining_query: str = None) -> ExecutionPlan:
        """生成适应性计划"""
        
        # 简化实现：创建重试计划
        adapted_steps = []
        
        for step in original_plan.steps:
            # 为失败的步骤创建重试版本
            if any(step.step_id in failure_analysis.get("failed_steps", [])):
                # 调整参数或策略
                adapted_step = ExecutionStep(
                    step_id=f"{step.step_id}_retry",
                    tool_name=step.tool_name,
                    parameters=step.parameters,
                    dependencies=step.dependencies,
                    expected_output_type=step.expected_output_type,
                    timeout=step.timeout * 2,  # 增加超时时间
                    retry_count=step.retry_count - 1
                )
                adapted_steps.append(adapted_step)
            else:
                adapted_steps.append(step)
        
        return ExecutionPlan(
            plan_id=f"{original_plan.plan_id}_adapted",
            query=remaining_query or original_plan.query,
            strategy=original_plan.strategy,
            steps=adapted_steps,
            estimated_duration=original_plan.estimated_duration,
            confidence=original_plan.confidence * 0.8,  # 降低置信度
            reasoning=f"基于失败分析的适应性计划: {failure_analysis}"
        )
