"""
智能缓存和学习系统
Intelligent Cache and Learning System
"""

import asyncio
import json
import logging
import pickle
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
import hashlib
import numpy as np
from collections import defaultdict, deque


@dataclass
class QueryPattern:
    """查询模式"""
    pattern_id: str
    query_template: str
    frequency: int
    success_rate: float
    avg_response_time: float
    preferred_tools: List[str]
    context_features: Dict[str, Any]
    last_used: float


@dataclass
class ToolPerformance:
    """工具性能指标"""
    tool_name: str
    success_count: int
    failure_count: int
    avg_execution_time: float
    user_satisfaction: float
    context_effectiveness: Dict[str, float]
    last_updated: float


@dataclass
class UserProfile:
    """用户画像"""
    user_id: str
    preferred_domains: List[str]
    tool_usage_patterns: Dict[str, int]
    query_complexity_preference: float
    response_style_preference: str
    learning_rate: float
    created_at: float
    updated_at: float


class IntelligentCacheManager:
    """智能缓存管理器"""
    
    def __init__(self, cache_dir: str = ".intelligent_cache"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        
        # 缓存文件路径
        self.query_patterns_file = self.cache_dir / "query_patterns.pkl"
        self.tool_performance_file = self.cache_dir / "tool_performance.pkl"
        self.user_profiles_file = self.cache_dir / "user_profiles.pkl"
        self.vector_cache_file = self.cache_dir / "enhanced_vectors.pkl"
        
        # 内存缓存
        self.query_patterns: Dict[str, QueryPattern] = {}
        self.tool_performance: Dict[str, ToolPerformance] = {}
        self.user_profiles: Dict[str, UserProfile] = {}
        self.vector_cache: Dict[str, Dict[str, Any]] = {}
        
        # 学习参数
        self.learning_rate = 0.1
        self.pattern_threshold = 0.8
        self.max_patterns = 1000
        
        # 加载缓存
        self._load_all_caches()
    
    def _load_all_caches(self):
        """加载所有缓存"""
        try:
            if self.query_patterns_file.exists():
                with open(self.query_patterns_file, 'rb') as f:
                    self.query_patterns = pickle.load(f)
                logging.info(f"加载了 {len(self.query_patterns)} 个查询模式")
            
            if self.tool_performance_file.exists():
                with open(self.tool_performance_file, 'rb') as f:
                    self.tool_performance = pickle.load(f)
                logging.info(f"加载了 {len(self.tool_performance)} 个工具性能记录")
            
            if self.user_profiles_file.exists():
                with open(self.user_profiles_file, 'rb') as f:
                    self.user_profiles = pickle.load(f)
                logging.info(f"加载了 {len(self.user_profiles)} 个用户画像")
            
            if self.vector_cache_file.exists():
                with open(self.vector_cache_file, 'rb') as f:
                    self.vector_cache = pickle.load(f)
                logging.info(f"加载了 {len(self.vector_cache)} 个增强向量")
                
        except Exception as e:
            logging.error(f"加载缓存失败: {e}")
    
    def _save_all_caches(self):
        """保存所有缓存"""
        try:
            with open(self.query_patterns_file, 'wb') as f:
                pickle.dump(self.query_patterns, f)
            
            with open(self.tool_performance_file, 'wb') as f:
                pickle.dump(self.tool_performance, f)
            
            with open(self.user_profiles_file, 'wb') as f:
                pickle.dump(self.user_profiles, f)
            
            with open(self.vector_cache_file, 'wb') as f:
                pickle.dump(self.vector_cache, f)
                
            logging.info("所有缓存已保存")
        except Exception as e:
            logging.error(f"保存缓存失败: {e}")
    
    def get_query_pattern(self, query: str) -> Optional[QueryPattern]:
        """获取查询模式"""
        query_hash = self._hash_query(query)
        
        # 精确匹配
        if query_hash in self.query_patterns:
            pattern = self.query_patterns[query_hash]
            pattern.last_used = time.time()
            return pattern
        
        # 模糊匹配
        best_match = None
        best_similarity = 0
        
        for pattern in self.query_patterns.values():
            similarity = self._calculate_query_similarity(query, pattern.query_template)
            if similarity > self.pattern_threshold and similarity > best_similarity:
                best_similarity = similarity
                best_match = pattern
        
        if best_match:
            best_match.last_used = time.time()
        
        return best_match
    
    def update_query_pattern(self, 
                           query: str, 
                           tools_used: List[str],
                           success: bool,
                           response_time: float,
                           context: Dict[str, Any] = None):
        """更新查询模式"""
        query_hash = self._hash_query(query)
        current_time = time.time()
        
        if query_hash in self.query_patterns:
            # 更新现有模式
            pattern = self.query_patterns[query_hash]
            pattern.frequency += 1
            
            # 更新成功率
            total_attempts = pattern.frequency
            current_success_rate = pattern.success_rate
            new_success_rate = (current_success_rate * (total_attempts - 1) + (1 if success else 0)) / total_attempts
            pattern.success_rate = new_success_rate
            
            # 更新响应时间
            pattern.avg_response_time = (pattern.avg_response_time * (total_attempts - 1) + response_time) / total_attempts
            
            # 更新偏好工具
            for tool in tools_used:
                if tool not in pattern.preferred_tools:
                    pattern.preferred_tools.append(tool)
            
            pattern.last_used = current_time
        else:
            # 创建新模式
            pattern = QueryPattern(
                pattern_id=query_hash,
                query_template=query,
                frequency=1,
                success_rate=1.0 if success else 0.0,
                avg_response_time=response_time,
                preferred_tools=tools_used.copy(),
                context_features=context or {},
                last_used=current_time
            )
            self.query_patterns[query_hash] = pattern
        
        # 清理过期模式
        self._cleanup_old_patterns()
    
    def get_tool_performance(self, tool_name: str) -> Optional[ToolPerformance]:
        """获取工具性能"""
        return self.tool_performance.get(tool_name)
    
    def update_tool_performance(self,
                              tool_name: str,
                              success: bool,
                              execution_time: float,
                              user_satisfaction: float = None,
                              context: str = None):
        """更新工具性能"""
        current_time = time.time()
        
        if tool_name in self.tool_performance:
            perf = self.tool_performance[tool_name]
            
            if success:
                perf.success_count += 1
            else:
                perf.failure_count += 1
            
            # 更新平均执行时间
            total_executions = perf.success_count + perf.failure_count
            perf.avg_execution_time = (perf.avg_execution_time * (total_executions - 1) + execution_time) / total_executions
            
            # 更新用户满意度
            if user_satisfaction is not None:
                perf.user_satisfaction = (perf.user_satisfaction + user_satisfaction) / 2
            
            # 更新上下文有效性
            if context:
                if context not in perf.context_effectiveness:
                    perf.context_effectiveness[context] = 0.5
                
                # 根据成功率调整上下文有效性
                adjustment = 0.1 if success else -0.1
                perf.context_effectiveness[context] = max(0, min(1, 
                    perf.context_effectiveness[context] + adjustment))
            
            perf.last_updated = current_time
        else:
            # 创建新的性能记录
            perf = ToolPerformance(
                tool_name=tool_name,
                success_count=1 if success else 0,
                failure_count=0 if success else 1,
                avg_execution_time=execution_time,
                user_satisfaction=user_satisfaction or 0.5,
                context_effectiveness={context: 0.7} if context else {},
                last_updated=current_time
            )
            self.tool_performance[tool_name] = perf
    
    def get_user_profile(self, user_id: str) -> Optional[UserProfile]:
        """获取用户画像"""
        return self.user_profiles.get(user_id)
    
    def update_user_profile(self,
                          user_id: str,
                          query_domain: str = None,
                          tools_used: List[str] = None,
                          query_complexity: float = None,
                          response_preference: str = None):
        """更新用户画像"""
        current_time = time.time()
        
        if user_id in self.user_profiles:
            profile = self.user_profiles[user_id]
            
            # 更新偏好领域
            if query_domain and query_domain not in profile.preferred_domains:
                profile.preferred_domains.append(query_domain)
                # 保持最多5个偏好领域
                if len(profile.preferred_domains) > 5:
                    profile.preferred_domains.pop(0)
            
            # 更新工具使用模式
            if tools_used:
                for tool in tools_used:
                    profile.tool_usage_patterns[tool] = profile.tool_usage_patterns.get(tool, 0) + 1
            
            # 更新复杂度偏好
            if query_complexity is not None:
                profile.query_complexity_preference = (
                    profile.query_complexity_preference * 0.8 + query_complexity * 0.2
                )
            
            # 更新响应风格偏好
            if response_preference:
                profile.response_style_preference = response_preference
            
            profile.updated_at = current_time
        else:
            # 创建新用户画像
            profile = UserProfile(
                user_id=user_id,
                preferred_domains=[query_domain] if query_domain else [],
                tool_usage_patterns={tool: 1 for tool in (tools_used or [])},
                query_complexity_preference=query_complexity or 3.0,
                response_style_preference=response_preference or "balanced",
                learning_rate=0.1,
                created_at=current_time,
                updated_at=current_time
            )
            self.user_profiles[user_id] = profile
    
    def get_enhanced_vector(self, tool_name: str, context: str = None) -> Optional[List[float]]:
        """获取增强向量"""
        key = f"{tool_name}_{context}" if context else tool_name
        cache_entry = self.vector_cache.get(key)
        
        if cache_entry:
            # 检查缓存是否过期（24小时）
            if time.time() - cache_entry.get("timestamp", 0) < 86400:
                return cache_entry.get("vector")
        
        return None
    
    def set_enhanced_vector(self, 
                          tool_name: str, 
                          vector: List[float],
                          context: str = None,
                          enhancement_factors: Dict[str, float] = None):
        """设置增强向量"""
        key = f"{tool_name}_{context}" if context else tool_name
        
        self.vector_cache[key] = {
            "vector": vector,
            "timestamp": time.time(),
            "enhancement_factors": enhancement_factors or {},
            "usage_count": self.vector_cache.get(key, {}).get("usage_count", 0) + 1
        }
    
    def get_personalized_recommendations(self, user_id: str, query_domain: str = None) -> List[str]:
        """获取个性化推荐"""
        profile = self.get_user_profile(user_id)
        if not profile:
            return []
        
        recommendations = []
        
        # 基于工具使用模式推荐
        sorted_tools = sorted(profile.tool_usage_patterns.items(), 
                            key=lambda x: x[1], reverse=True)
        
        for tool_name, usage_count in sorted_tools[:5]:
            # 检查工具性能
            perf = self.get_tool_performance(tool_name)
            if perf and perf.success_count > perf.failure_count:
                recommendations.append(tool_name)
        
        # 基于领域偏好推荐
        if query_domain and query_domain in profile.preferred_domains:
            # 查找该领域的高性能工具
            domain_tools = []
            for tool_name, perf in self.tool_performance.items():
                if query_domain in perf.context_effectiveness:
                    effectiveness = perf.context_effectiveness[query_domain]
                    if effectiveness > 0.6:
                        domain_tools.append((tool_name, effectiveness))
            
            # 按有效性排序
            domain_tools.sort(key=lambda x: x[1], reverse=True)
            for tool_name, _ in domain_tools[:3]:
                if tool_name not in recommendations:
                    recommendations.append(tool_name)
        
        return recommendations
    
    def _hash_query(self, query: str) -> str:
        """生成查询哈希"""
        # 标准化查询
        normalized = query.lower().strip()
        return hashlib.md5(normalized.encode()).hexdigest()
    
    def _calculate_query_similarity(self, query1: str, query2: str) -> float:
        """计算查询相似度"""
        # 简单的词汇重叠相似度
        words1 = set(query1.lower().split())
        words2 = set(query2.lower().split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union)
    
    def _cleanup_old_patterns(self):
        """清理过期模式"""
        if len(self.query_patterns) <= self.max_patterns:
            return
        
        current_time = time.time()
        
        # 按最后使用时间和频率排序
        patterns_by_score = []
        for pattern in self.query_patterns.values():
            # 计算模式得分（频率 + 最近使用时间权重）
            time_weight = max(0, 1 - (current_time - pattern.last_used) / 86400)  # 24小时衰减
            score = pattern.frequency * 0.7 + time_weight * 0.3
            patterns_by_score.append((pattern.pattern_id, score))
        
        # 排序并保留前N个
        patterns_by_score.sort(key=lambda x: x[1], reverse=True)
        keep_patterns = set(pid for pid, _ in patterns_by_score[:self.max_patterns])
        
        # 删除低分模式
        to_remove = []
        for pattern_id in self.query_patterns:
            if pattern_id not in keep_patterns:
                to_remove.append(pattern_id)
        
        for pattern_id in to_remove:
            del self.query_patterns[pattern_id]
        
        logging.info(f"清理了 {len(to_remove)} 个过期查询模式")
    
    def get_cache_statistics(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        current_time = time.time()
        
        # 查询模式统计
        pattern_stats = {
            "total_patterns": len(self.query_patterns),
            "avg_frequency": np.mean([p.frequency for p in self.query_patterns.values()]) if self.query_patterns else 0,
            "avg_success_rate": np.mean([p.success_rate for p in self.query_patterns.values()]) if self.query_patterns else 0
        }
        
        # 工具性能统计
        tool_stats = {
            "total_tools": len(self.tool_performance),
            "avg_success_rate": 0,
            "avg_execution_time": 0
        }
        
        if self.tool_performance:
            success_rates = []
            exec_times = []
            for perf in self.tool_performance.values():
                total = perf.success_count + perf.failure_count
                if total > 0:
                    success_rates.append(perf.success_count / total)
                    exec_times.append(perf.avg_execution_time)
            
            if success_rates:
                tool_stats["avg_success_rate"] = np.mean(success_rates)
            if exec_times:
                tool_stats["avg_execution_time"] = np.mean(exec_times)
        
        # 用户画像统计
        user_stats = {
            "total_users": len(self.user_profiles),
            "avg_tool_usage": np.mean([len(p.tool_usage_patterns) for p in self.user_profiles.values()]) if self.user_profiles else 0
        }
        
        # 向量缓存统计
        vector_stats = {
            "total_vectors": len(self.vector_cache),
            "cache_hit_rate": 0  # 需要在使用时统计
        }
        
        return {
            "query_patterns": pattern_stats,
            "tool_performance": tool_stats,
            "user_profiles": user_stats,
            "vector_cache": vector_stats,
            "last_updated": current_time
        }
    
    def save_cache(self):
        """保存缓存"""
        self._save_all_caches()
    
    def clear_cache(self, cache_type: str = "all"):
        """清理缓存"""
        if cache_type == "all" or cache_type == "patterns":
            self.query_patterns.clear()
            if self.query_patterns_file.exists():
                self.query_patterns_file.unlink()
        
        if cache_type == "all" or cache_type == "performance":
            self.tool_performance.clear()
            if self.tool_performance_file.exists():
                self.tool_performance_file.unlink()
        
        if cache_type == "all" or cache_type == "profiles":
            self.user_profiles.clear()
            if self.user_profiles_file.exists():
                self.user_profiles_file.unlink()
        
        if cache_type == "all" or cache_type == "vectors":
            self.vector_cache.clear()
            if self.vector_cache_file.exists():
                self.vector_cache_file.unlink()
        
        logging.info(f"已清理 {cache_type} 缓存")


class LearningEngine:
    """学习引擎"""
    
    def __init__(self, cache_manager: IntelligentCacheManager):
        self.cache_manager = cache_manager
        self.learning_sessions = deque(maxlen=1000)  # 保留最近1000次学习会话
    
    async def learn_from_interaction(self,
                                   user_id: str,
                                   query: str,
                                   selected_tools: List[str],
                                   execution_results: Dict[str, Any],
                                   user_feedback: Dict[str, Any] = None):
        """从交互中学习"""
        
        # 分析执行结果
        success = self._analyze_execution_success(execution_results)
        response_time = execution_results.get("total_time", 0)
        
        # 更新查询模式
        self.cache_manager.update_query_pattern(
            query=query,
            tools_used=selected_tools,
            success=success,
            response_time=response_time,
            context={"user_id": user_id}
        )
        
        # 更新工具性能
        for tool_name in selected_tools:
            tool_success = self._get_tool_success(tool_name, execution_results)
            tool_time = self._get_tool_execution_time(tool_name, execution_results)
            user_satisfaction = user_feedback.get("satisfaction", 0.5) if user_feedback else 0.5
            
            self.cache_manager.update_tool_performance(
                tool_name=tool_name,
                success=tool_success,
                execution_time=tool_time,
                user_satisfaction=user_satisfaction,
                context=self._extract_context(query)
            )
        
        # 更新用户画像
        self.cache_manager.update_user_profile(
            user_id=user_id,
            query_domain=self._extract_domain(query),
            tools_used=selected_tools,
            query_complexity=self._estimate_query_complexity(query),
            response_preference=user_feedback.get("style_preference") if user_feedback else None
        )
        
        # 记录学习会话
        session = {
            "timestamp": time.time(),
            "user_id": user_id,
            "query": query,
            "tools": selected_tools,
            "success": success,
            "feedback": user_feedback
        }
        self.learning_sessions.append(session)
    
    def _analyze_execution_success(self, execution_results: Dict[str, Any]) -> bool:
        """分析执行是否成功"""
        if "results" not in execution_results:
            return False
        
        results = execution_results["results"]
        success_count = sum(1 for r in results.values() if r.get("status") == "success")
        total_count = len(results)
        
        return success_count / total_count > 0.5 if total_count > 0 else False
    
    def _get_tool_success(self, tool_name: str, execution_results: Dict[str, Any]) -> bool:
        """获取特定工具的执行成功状态"""
        results = execution_results.get("results", {})
        for result in results.values():
            if result.get("tool_name") == tool_name:
                return result.get("status") == "success"
        return False
    
    def _get_tool_execution_time(self, tool_name: str, execution_results: Dict[str, Any]) -> float:
        """获取特定工具的执行时间"""
        # 简化实现，返回平均时间
        return execution_results.get("total_time", 10) / len(execution_results.get("results", {}))
    
    def _extract_context(self, query: str) -> str:
        """提取查询上下文"""
        # 简单的关键词匹配
        contexts = {
            "weather": ["天气", "weather", "温度", "temperature"],
            "file": ["文件", "file", "目录", "directory"],
            "web": ["网页", "web", "搜索", "search"],
            "email": ["邮件", "email", "发送", "send"],
            "database": ["数据库", "database", "查询", "query"]
        }
        
        query_lower = query.lower()
        for context, keywords in contexts.items():
            if any(keyword in query_lower for keyword in keywords):
                return context
        
        return "general"
    
    def _extract_domain(self, query: str) -> str:
        """提取查询领域"""
        return self._extract_context(query)  # 简化实现
    
    def _estimate_query_complexity(self, query: str) -> float:
        """估算查询复杂度"""
        # 基于查询长度和关键词复杂度
        word_count = len(query.split())
        
        complex_indicators = ["分析", "比较", "综合", "详细", "复杂", "多步骤"]
        complexity_bonus = sum(1 for indicator in complex_indicators if indicator in query)
        
        # 1-5的复杂度评分
        base_complexity = min(5, max(1, word_count / 5))
        final_complexity = min(5, base_complexity + complexity_bonus * 0.5)
        
        return final_complexity
