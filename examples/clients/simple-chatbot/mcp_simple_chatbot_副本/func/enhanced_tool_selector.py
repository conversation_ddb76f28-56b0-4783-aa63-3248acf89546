"""
增强的工具选择器
集成多工具查询处理功能，解决复杂查询中多个工具需求识别不全的问题
"""

import asyncio
import logging
import time
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

from .multi_tool_query_processor import (
    MultiToolRetriever, 
    MultiToolResult, 
    QueryComplexity,
    ToolMatch
)

logger = logging.getLogger(__name__)


@dataclass
class EnhancedToolResult:
    """增强的工具选择结果"""
    tool_id: str
    tool_name: str
    description: str
    confidence: float
    selection_method: str
    reasoning: str
    match_details: Dict[str, Any]
    execution_time: float
    subtask_info: Optional[Dict[str, Any]] = None


class EnhancedToolSelector:
    """增强的工具选择器"""
    
    def __init__(self, 
                 cached_store,
                 tool_registry: Dict[str, Any],
                 multi_agent_system=None,
                 enable_multi_tool_processing: bool = True):
        """
        初始化增强工具选择器
        
        Args:
            cached_store: 缓存存储系统
            tool_registry: 工具注册表
            multi_agent_system: 多Agent系统（可选）
            enable_multi_tool_processing: 是否启用多工具处理
        """
        self.cached_store = cached_store
        self.tool_registry = tool_registry
        self.multi_agent_system = multi_agent_system
        self.enable_multi_tool_processing = enable_multi_tool_processing
        
        # 初始化多工具检索器
        if self.enable_multi_tool_processing:
            self.multi_tool_retriever = MultiToolRetriever(
                cached_store, 
                cached_store.tool_vector_manager
            )
        
        # 性能统计
        self.stats = {
            "total_queries": 0,
            "multi_tool_queries": 0,
            "single_tool_queries": 0,
            "avg_processing_time": 0.0,
            "avg_tools_returned": 0.0
        }
    
    async def select_tools(self, 
                          query: str, 
                          max_tools: int = 10,
                          user_id: str = None,
                          context: Dict[str, Any] = None,
                          force_multi_tool: bool = False) -> List[EnhancedToolResult]:
        """
        智能选择相关工具
        
        Args:
            query: 用户查询
            max_tools: 最大返回工具数
            user_id: 用户ID
            context: 上下文信息
            force_multi_tool: 强制使用多工具处理
            
        Returns:
            增强的工具选择结果列表
        """
        start_time = time.time()
        self.stats["total_queries"] += 1
        
        logger.info(f"开始增强工具选择: '{query[:50]}{'...' if len(query) > 50 else ''}'")
        
        try:
            # 1. 判断是否需要多工具处理
            if self.enable_multi_tool_processing and (force_multi_tool or self._should_use_multi_tool(query)):
                logger.info("使用多工具处理模式")
                results = await self._select_with_multi_tool(query, max_tools, user_id, context)
                self.stats["multi_tool_queries"] += 1
            else:
                logger.info("使用单工具处理模式")
                results = await self._select_with_single_tool(query, max_tools, user_id, context)
                self.stats["single_tool_queries"] += 1
            
            # 2. 更新统计信息
            processing_time = time.time() - start_time
            self._update_stats(processing_time, len(results))
            
            # 3. 设置执行时间
            for result in results:
                result.execution_time = processing_time / len(results) if results else processing_time
            
            logger.info(f"工具选择完成 - 耗时: {processing_time:.2f}s, 返回 {len(results)} 个工具")
            return results
            
        except Exception as e:
            logger.error(f"工具选择失败: {e}")
            return []
    
    def _should_use_multi_tool(self, query: str) -> bool:
        """判断是否应该使用多工具处理"""
        # 检测多工具指示词
        multi_tool_indicators = [
            '，', ',', '；', ';',  # 分隔符
            '然后', '接着', '之后', '再',  # 顺序词
            '同时', '并且', '以及', '和',  # 并行词
            '对.*?进行', '执行.*?的',  # 动作模式
        ]
        
        indicator_count = 0
        for indicator in multi_tool_indicators:
            if indicator in query:
                indicator_count += 1
        
        # 检测动作词数量
        action_words = [
            '查询', '查找', '搜索', '获取', '检索',
            '创建', '新建', '添加', '生成',
            '更新', '修改', '编辑',
            '删除', '移除',
            '截图', '抓取', '捕获',
            '播放', '执行', '运行',
            '分析', '统计', '计算',
            'search', 'find', 'get', 'create', 'update', 'delete', 'capture', 'play'
        ]
        
        action_count = sum(1 for word in action_words if word in query)
        
        # 判断逻辑：有分隔符且动作词多于1个，或者指示词多于2个
        return (indicator_count > 0 and action_count > 1) or indicator_count > 2
    
    async def _select_with_multi_tool(self, 
                                     query: str, 
                                     max_tools: int,
                                     user_id: str,
                                     context: Dict[str, Any]) -> List[EnhancedToolResult]:
        """使用多工具处理模式"""
        logger.debug("执行多工具检索...")
        
        # 使用多工具检索器
        multi_result = await self.multi_tool_retriever.retrieve_multi_tools(
            query=query,
            max_tools_per_task=3,
            total_max_tools=max_tools
        )
        
        # 转换为增强结果格式
        enhanced_results = []
        for tool_match in multi_result.tool_matches:
            # 获取对应的子任务信息
            subtask_info = None
            for subtask in multi_result.subtasks:
                if subtask.task_id == tool_match.subtask_id:
                    subtask_info = {
                        "task_id": subtask.task_id,
                        "description": subtask.description,
                        "action_type": subtask.action_type,
                        "keywords": subtask.keywords,
                        "entities": subtask.entities
                    }
                    break
            
            enhanced_result = EnhancedToolResult(
                tool_id=tool_match.tool_id,
                tool_name=tool_match.tool_name,
                description=tool_match.description,
                confidence=tool_match.confidence,
                selection_method="multi_tool_processing",
                reasoning=f"多工具处理: {tool_match.match_reason}",
                match_details={
                    "similarity_score": tool_match.similarity_score,
                    "keyword_matches": tool_match.keyword_matches,
                    "query_complexity": multi_result.complexity.value,
                    "execution_strategy": multi_result.execution_strategy
                },
                execution_time=0.0,  # 将在外部设置
                subtask_info=subtask_info
            )
            enhanced_results.append(enhanced_result)
        
        logger.info(f"多工具处理完成 - 查询复杂度: {multi_result.complexity.value}, "
                   f"子任务数: {len(multi_result.subtasks)}, 工具数: {len(enhanced_results)}")
        
        return enhanced_results
    
    async def _select_with_single_tool(self, 
                                      query: str, 
                                      max_tools: int,
                                      user_id: str,
                                      context: Dict[str, Any]) -> List[EnhancedToolResult]:
        """使用单工具处理模式"""
        logger.debug("执行单工具检索...")
        
        # 使用传统的相似度检索
        similar_tools = await self.cached_store.search_similar_tools(query, max_tools)
        
        enhanced_results = []
        for tool_info in similar_tools:
            enhanced_result = EnhancedToolResult(
                tool_id=tool_info["tool_id"],
                tool_name=tool_info["tool_data"]["name"],
                description=tool_info["tool_data"]["description"],
                confidence=tool_info["similarity"],
                selection_method="single_tool_similarity",
                reasoning=f"相似度匹配: {tool_info['similarity']:.3f}",
                match_details=tool_info.get("match_details", {}),
                execution_time=0.0  # 将在外部设置
            )
            enhanced_results.append(enhanced_result)
        
        return enhanced_results
    
    def _update_stats(self, processing_time: float, tool_count: int):
        """更新统计信息"""
        # 更新平均处理时间
        total_queries = self.stats["total_queries"]
        old_avg_time = self.stats["avg_processing_time"]
        self.stats["avg_processing_time"] = (old_avg_time * (total_queries - 1) + processing_time) / total_queries
        
        # 更新平均工具数
        old_avg_tools = self.stats["avg_tools_returned"]
        self.stats["avg_tools_returned"] = (old_avg_tools * (total_queries - 1) + tool_count) / total_queries
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "performance_stats": self.stats.copy(),
            "multi_tool_ratio": self.stats["multi_tool_queries"] / max(self.stats["total_queries"], 1),
            "single_tool_ratio": self.stats["single_tool_queries"] / max(self.stats["total_queries"], 1),
            "multi_tool_enabled": self.enable_multi_tool_processing
        }
    
    def enable_multi_tool_mode(self):
        """启用多工具模式"""
        self.enable_multi_tool_processing = True
        if not hasattr(self, 'multi_tool_retriever'):
            self.multi_tool_retriever = MultiToolRetriever(
                self.cached_store, 
                self.cached_store.tool_vector_manager
            )
        logger.info("多工具处理模式已启用")
    
    def disable_multi_tool_mode(self):
        """禁用多工具模式"""
        self.enable_multi_tool_processing = False
        logger.info("多工具处理模式已禁用")
    
    async def analyze_query(self, query: str) -> Dict[str, Any]:
        """分析查询特征（用于调试和优化）"""
        analysis = {
            "query": query,
            "length": len(query),
            "should_use_multi_tool": self._should_use_multi_tool(query),
            "indicators": []
        }
        
        # 检测各种指示词
        multi_tool_indicators = {
            "separators": ['，', ',', '；', ';'],
            "sequential_words": ['然后', '接着', '之后', '再'],
            "parallel_words": ['同时', '并且', '以及', '和'],
            "action_patterns": ['对.*?进行', '执行.*?的']
        }
        
        for category, indicators in multi_tool_indicators.items():
            found = [ind for ind in indicators if ind in query]
            if found:
                analysis["indicators"].append({
                    "category": category,
                    "found": found
                })
        
        # 如果启用了多工具处理，进行详细分析
        if self.enable_multi_tool_processing:
            try:
                complexity, subtasks = await self.multi_tool_retriever.query_decomposer.decompose_query(query)
                analysis["decomposition"] = {
                    "complexity": complexity.value,
                    "subtask_count": len(subtasks),
                    "subtasks": [
                        {
                            "id": task.task_id,
                            "description": task.description,
                            "action_type": task.action_type,
                            "keywords": task.keywords[:5],  # 限制显示数量
                            "entities": task.entities
                        }
                        for task in subtasks
                    ]
                }
            except Exception as e:
                analysis["decomposition_error"] = str(e)
        
        return analysis


# 便捷函数
async def create_enhanced_tool_selector(cached_store, 
                                       tool_registry: Dict[str, Any],
                                       multi_agent_system=None,
                                       enable_multi_tool: bool = True) -> EnhancedToolSelector:
    """创建增强工具选择器的便捷函数"""
    return EnhancedToolSelector(
        cached_store=cached_store,
        tool_registry=tool_registry,
        multi_agent_system=multi_agent_system,
        enable_multi_tool_processing=enable_multi_tool
    )
