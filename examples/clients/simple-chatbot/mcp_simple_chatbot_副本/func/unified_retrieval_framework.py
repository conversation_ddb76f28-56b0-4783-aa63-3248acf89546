#!/usr/bin/env python3
"""
统一检索策略框架
Unified Retrieval Strategy Framework

解决检索策略选择逻辑分散、错误处理简单、性能优化空间等问题
"""

import asyncio
import logging
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from enum import Enum
from typing import Dict, List, Any, Optional, Callable, Union
import traceback
from contextlib import asynccontextmanager
import concurrent.futures
from functools import wraps

# 创建专用logger
logger = logging.getLogger('UnifiedRetrievalFramework')


class RetrievalStrategy(Enum):
    """检索策略枚举"""
    SEMANTIC = "semantic"
    KEYWORD = "keyword"
    HYBRID = "hybrid"
    AGENT_BASED = "agent_based"
    CONTEXTUAL = "contextual"
    ENSEMBLE = "ensemble"


class ErrorType(Enum):
    """错误类型枚举"""
    TIMEOUT_ERROR = "timeout_error"
    API_ERROR = "api_error"
    VALIDATION_ERROR = "validation_error"
    RESOURCE_ERROR = "resource_error"
    NETWORK_ERROR = "network_error"
    UNKNOWN_ERROR = "unknown_error"


@dataclass
class RetrievalMetrics:
    """检索指标"""
    strategy: str
    execution_time: float
    success: bool
    result_count: int
    confidence_score: float
    error_type: Optional[str] = None
    error_message: Optional[str] = None
    retry_count: int = 0
    cache_hit: bool = False


@dataclass
class RetrievalContext:
    """统一检索上下文"""
    query: str
    user_id: str
    session_id: str
    top_k: int = 10
    timeout: float = 30.0
    retry_config: Dict[str, Any] = field(default_factory=dict)
    cache_enabled: bool = True
    parallel_enabled: bool = True
    fallback_strategies: List[str] = field(default_factory=list)
    quality_threshold: float = 0.1
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class RetrievalResult:
    """统一检索结果"""
    tool_id: str
    tool_name: str
    description: str
    confidence: float
    strategy_used: str
    reasoning: str
    metadata: Dict[str, Any] = field(default_factory=dict)
    execution_time: float = 0.0


class RetrievalError(Exception):
    """检索错误基类"""
    def __init__(self, message: str, error_type: ErrorType, original_error: Exception = None):
        super().__init__(message)
        self.error_type = error_type
        self.original_error = original_error


class TimeoutError(RetrievalError):
    """超时错误"""
    def __init__(self, message: str, timeout: float):
        super().__init__(message, ErrorType.TIMEOUT_ERROR)
        self.timeout = timeout


class APIError(RetrievalError):
    """API错误"""
    def __init__(self, message: str, status_code: int = None):
        super().__init__(message, ErrorType.API_ERROR)
        self.status_code = status_code


class ValidationError(RetrievalError):
    """验证错误"""
    def __init__(self, message: str, field: str = None):
        super().__init__(message, ErrorType.VALIDATION_ERROR)
        self.field = field


class RetryConfig:
    """重试配置"""
    def __init__(self,
                 max_retries: int = 3,
                 base_delay: float = 1.0,
                 max_delay: float = 60.0,
                 exponential_base: float = 2.0,
                 jitter: bool = True):
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.exponential_base = exponential_base
        self.jitter = jitter


class CircuitBreaker:
    """熔断器"""
    def __init__(self,
                 failure_threshold: int = 5,
                 recovery_timeout: float = 60.0,
                 expected_exception: type = Exception):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        self.failure_count = 0
        self.last_failure_time = None
        self.state = 'CLOSED'  # CLOSED, OPEN, HALF_OPEN

    def call(self, func):
        """调用函数并应用熔断逻辑"""
        if self.state == 'OPEN':
            if time.time() - self.last_failure_time > self.recovery_timeout:
                self.state = 'HALF_OPEN'
            else:
                raise RetrievalError("Circuit breaker is OPEN", ErrorType.RESOURCE_ERROR)

        try:
            result = func()
            if self.state == 'HALF_OPEN':
                self.state = 'CLOSED'
                self.failure_count = 0
            return result
        except self.expected_exception as e:
            self.failure_count += 1
            self.last_failure_time = time.time()
            
            if self.failure_count >= self.failure_threshold:
                self.state = 'OPEN'
            
            raise e


def with_retry(retry_config: RetryConfig):
    """重试装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(retry_config.max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    
                    if attempt == retry_config.max_retries:
                        break
                    
                    # 计算延迟时间
                    delay = min(
                        retry_config.base_delay * (retry_config.exponential_base ** attempt),
                        retry_config.max_delay
                    )
                    
                    if retry_config.jitter:
                        import random
                        delay *= (0.5 + random.random() * 0.5)
                    
                    logger.warning(f"Attempt {attempt + 1} failed: {e}, retrying in {delay:.2f}s")
                    await asyncio.sleep(delay)
            
            raise last_exception
        return wrapper
    return decorator


def with_timeout(timeout: float):
    """超时装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                return await asyncio.wait_for(func(*args, **kwargs), timeout=timeout)
            except asyncio.TimeoutError:
                raise TimeoutError(f"Operation timed out after {timeout}s", timeout)
        return wrapper
    return decorator


class BaseRetrievalStrategy(ABC):
    """检索策略基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.circuit_breaker = CircuitBreaker()
        self.metrics_history: List[RetrievalMetrics] = []
        
    @abstractmethod
    async def retrieve(self, context: RetrievalContext) -> List[RetrievalResult]:
        """执行检索"""
        pass
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        if not self.metrics_history:
            return {"avg_time": 0, "success_rate": 0, "total_calls": 0}
        
        total_calls = len(self.metrics_history)
        successful_calls = sum(1 for m in self.metrics_history if m.success)
        avg_time = sum(m.execution_time for m in self.metrics_history) / total_calls
        
        return {
            "avg_time": avg_time,
            "success_rate": successful_calls / total_calls,
            "total_calls": total_calls,
            "avg_confidence": sum(m.confidence_score for m in self.metrics_history if m.success) / max(successful_calls, 1)
        }
    
    def record_metrics(self, metrics: RetrievalMetrics):
        """记录指标"""
        self.metrics_history.append(metrics)
        # 保持最近1000条记录
        if len(self.metrics_history) > 1000:
            self.metrics_history = self.metrics_history[-1000:]


class StrategySelector:
    """策略选择器"""
    
    def __init__(self):
        self.strategies: Dict[str, BaseRetrievalStrategy] = {}
        self.strategy_weights: Dict[str, float] = {}
        self.selection_history: List[Dict[str, Any]] = []
        
    def register_strategy(self, strategy: BaseRetrievalStrategy, weight: float = 1.0):
        """注册策略"""
        self.strategies[strategy.name] = strategy
        self.strategy_weights[strategy.name] = weight
        
    def select_strategy(self, context: RetrievalContext) -> str:
        """智能选择策略"""
        # 基于历史性能和当前上下文选择最佳策略
        best_strategy = None
        best_score = -1
        
        for strategy_name, strategy in self.strategies.items():
            metrics = strategy.get_performance_metrics()

            # 安全获取指标值，提供默认值
            success_rate = metrics.get("success_rate", 0.0)
            avg_time = metrics.get("avg_time", 1.0)
            avg_confidence = metrics.get("avg_confidence", 0.0)
            strategy_weight = self.strategy_weights.get(strategy_name, 0.1)

            # 计算策略得分
            score = (
                success_rate * 0.4 +
                (1 / max(avg_time, 0.001)) * 0.3 +
                avg_confidence * 0.2 +
                strategy_weight * 0.1
            )
            
            if score > best_score:
                best_score = score
                best_strategy = strategy_name
        
        return best_strategy or list(self.strategies.keys())[0]


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics_buffer: List[RetrievalMetrics] = []
        self.alerts: List[Dict[str, Any]] = []
        
    def record_metrics(self, metrics: RetrievalMetrics):
        """记录指标"""
        self.metrics_buffer.append(metrics)
        self._check_alerts(metrics)
        
        # 保持缓冲区大小
        if len(self.metrics_buffer) > 10000:
            self.metrics_buffer = self.metrics_buffer[-10000:]
    
    def _check_alerts(self, metrics: RetrievalMetrics):
        """检查告警条件"""
        # 检查执行时间过长
        if metrics.execution_time > 30.0:
            self.alerts.append({
                "type": "slow_execution",
                "strategy": metrics.strategy,
                "execution_time": metrics.execution_time,
                "timestamp": time.time()
            })
        
        # 检查成功率过低
        recent_metrics = [m for m in self.metrics_buffer[-100:] if m.strategy == metrics.strategy]
        if len(recent_metrics) >= 10:
            success_rate = sum(1 for m in recent_metrics if m.success) / len(recent_metrics)
            if success_rate < 0.7:
                self.alerts.append({
                    "type": "low_success_rate",
                    "strategy": metrics.strategy,
                    "success_rate": success_rate,
                    "timestamp": time.time()
                })
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        if not self.metrics_buffer:
            return {}
        
        # 按策略分组统计
        strategy_stats = {}
        for metrics in self.metrics_buffer:
            if metrics.strategy not in strategy_stats:
                strategy_stats[metrics.strategy] = []
            strategy_stats[metrics.strategy].append(metrics)
        
        report = {}
        for strategy, metrics_list in strategy_stats.items():
            successful = [m for m in metrics_list if m.success]
            report[strategy] = {
                "total_calls": len(metrics_list),
                "success_rate": len(successful) / len(metrics_list),
                "avg_execution_time": sum(m.execution_time for m in metrics_list) / len(metrics_list),
                "avg_confidence": sum(m.confidence_score for m in successful) / max(len(successful), 1)
            }
        
        return {
            "strategy_performance": report,
            "recent_alerts": self.alerts[-10:],
            "total_calls": len(self.metrics_buffer)
        }


class UnifiedRetrievalManager:
    """统一检索管理器"""

    def __init__(self):
        self.strategy_selector = StrategySelector()
        self.performance_monitor = PerformanceMonitor()
        self.cache: Dict[str, Any] = {}
        self.parallel_executor = concurrent.futures.ThreadPoolExecutor(max_workers=4)

    def register_strategy(self, strategy: BaseRetrievalStrategy, weight: float = 1.0):
        """注册检索策略"""
        self.strategy_selector.register_strategy(strategy, weight)
        logger.info(f"Registered strategy: {strategy.name} with weight {weight}")

    async def retrieve(self, context: RetrievalContext) -> List[RetrievalResult]:
        """统一检索入口"""
        start_time = time.time()

        try:
            # 1. 验证输入
            self._validate_context(context)

            # 2. 检查缓存
            if context.cache_enabled:
                cached_result = self._get_cached_result(context)
                if cached_result:
                    logger.info(f"Cache hit for query: {context.query[:50]}...")
                    return cached_result

            # 3. 选择策略
            strategy_name = self.strategy_selector.select_strategy(context)
            strategy = self.strategy_selector.strategies[strategy_name]

            logger.info(f"Selected strategy: {strategy_name} for query: {context.query[:50]}...")

            # 4. 执行检索
            results = await self._execute_with_fallback(strategy, context)

            # 5. 质量检查
            filtered_results = self._filter_by_quality(results, context.quality_threshold)

            # 6. 缓存结果
            if context.cache_enabled and filtered_results:
                self._cache_result(context, filtered_results)

            # 7. 记录指标
            execution_time = time.time() - start_time
            metrics = RetrievalMetrics(
                strategy=strategy_name,
                execution_time=execution_time,
                success=len(filtered_results) > 0,
                result_count=len(filtered_results),
                confidence_score=sum(r.confidence for r in filtered_results) / max(len(filtered_results), 1),
                cache_hit=False
            )

            strategy.record_metrics(metrics)
            self.performance_monitor.record_metrics(metrics)

            logger.info(f"Retrieval completed: {len(filtered_results)} results in {execution_time:.2f}s")
            return filtered_results

        except Exception as e:
            execution_time = time.time() - start_time
            error_type = self._classify_error(e)

            metrics = RetrievalMetrics(
                strategy=getattr(context, 'last_strategy', 'unknown'),
                execution_time=execution_time,
                success=False,
                result_count=0,
                confidence_score=0.0,
                error_type=error_type.value,
                error_message=str(e)
            )

            self.performance_monitor.record_metrics(metrics)
            logger.error(f"Retrieval failed: {e}")
            raise

    async def _execute_with_fallback(self,
                                   primary_strategy: BaseRetrievalStrategy,
                                   context: RetrievalContext) -> List[RetrievalResult]:
        """执行检索并支持回退策略"""
        try:
            # 尝试主策略
            return await primary_strategy.retrieve(context)

        except Exception as e:
            logger.warning(f"Primary strategy {primary_strategy.name} failed: {e}")

            # 尝试回退策略
            for fallback_name in context.fallback_strategies:
                if fallback_name in self.strategy_selector.strategies:
                    fallback_strategy = self.strategy_selector.strategies[fallback_name]
                    try:
                        logger.info(f"Trying fallback strategy: {fallback_name}")
                        return await fallback_strategy.retrieve(context)
                    except Exception as fallback_error:
                        logger.warning(f"Fallback strategy {fallback_name} failed: {fallback_error}")
                        continue

            # 所有策略都失败
            raise RetrievalError(f"All strategies failed. Last error: {e}", ErrorType.UNKNOWN_ERROR, e)

    def _validate_context(self, context: RetrievalContext):
        """验证检索上下文"""
        if not context.query or not context.query.strip():
            raise ValidationError("Query cannot be empty", "query")

        if context.top_k <= 0:
            raise ValidationError("top_k must be positive", "top_k")

        if context.timeout <= 0:
            raise ValidationError("timeout must be positive", "timeout")

    def _get_cached_result(self, context: RetrievalContext) -> Optional[List[RetrievalResult]]:
        """获取缓存结果"""
        cache_key = self._generate_cache_key(context)
        return self.cache.get(cache_key)

    def _cache_result(self, context: RetrievalContext, results: List[RetrievalResult]):
        """缓存结果"""
        cache_key = self._generate_cache_key(context)
        self.cache[cache_key] = results

        # 简单的LRU缓存清理
        if len(self.cache) > 1000:
            # 删除最旧的一半
            keys_to_delete = list(self.cache.keys())[:500]
            for key in keys_to_delete:
                del self.cache[key]

    def _generate_cache_key(self, context: RetrievalContext) -> str:
        """生成缓存键"""
        import hashlib
        key_data = f"{context.query}_{context.top_k}_{context.user_id}"
        return hashlib.md5(key_data.encode()).hexdigest()

    def _filter_by_quality(self, results: List[RetrievalResult], threshold: float) -> List[RetrievalResult]:
        """按质量阈值过滤结果"""
        return [r for r in results if r.confidence >= threshold]

    def _classify_error(self, error: Exception) -> ErrorType:
        """分类错误类型"""
        if isinstance(error, asyncio.TimeoutError):
            return ErrorType.TIMEOUT_ERROR
        elif isinstance(error, (ConnectionError, OSError)):
            return ErrorType.NETWORK_ERROR
        elif isinstance(error, ValueError):
            return ErrorType.VALIDATION_ERROR
        elif "API" in str(error) or "HTTP" in str(error):
            return ErrorType.API_ERROR
        else:
            return ErrorType.UNKNOWN_ERROR

    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        return self.performance_monitor.get_performance_report()

    def get_strategy_recommendations(self) -> Dict[str, Any]:
        """获取策略推荐"""
        report = self.get_performance_report()
        recommendations = []

        if "strategy_performance" in report:
            for strategy, stats in report["strategy_performance"].items():
                if stats["success_rate"] < 0.8:
                    recommendations.append(f"Strategy '{strategy}' has low success rate: {stats['success_rate']:.1%}")

                if stats["avg_execution_time"] > 10.0:
                    recommendations.append(f"Strategy '{strategy}' is slow: {stats['avg_execution_time']:.2f}s average")

        return {
            "recommendations": recommendations,
            "best_strategy": self._find_best_strategy(),
            "performance_summary": report
        }

    def _find_best_strategy(self) -> str:
        """找到最佳策略"""
        best_strategy = None
        best_score = -1

        for strategy_name, strategy in self.strategy_selector.strategies.items():
            metrics = strategy.get_performance_metrics()

            # 安全获取指标值
            success_rate = metrics.get("success_rate", 0.0)
            avg_confidence = metrics.get("avg_confidence", 0.0)
            avg_time = metrics.get("avg_time", 1.0)

            # 综合评分
            score = (
                success_rate * 0.5 +
                avg_confidence * 0.3 +
                (1 / max(avg_time, 0.001)) * 0.2
            )

            if score > best_score:
                best_score = score
                best_strategy = strategy_name

        return best_strategy
