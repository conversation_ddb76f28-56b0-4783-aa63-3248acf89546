#!/usr/bin/env python3
"""
优化的检索系统
Optimized Retrieval System

集成统一检索框架、错误处理、性能优化等功能
"""

import asyncio
import logging
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from .unified_retrieval_framework import (
    UnifiedRetrievalManager, RetrievalContext, RetrievalResult,
    BaseRetrievalStrategy, StrategySelector, PerformanceMonitor
)
from .concrete_retrieval_strategies import (
    HybridRetrievalStrategy, AgentBasedRetrievalStrategy,
    AliyunEnhancedRetrievalStrategy, EnsembleRetrievalStrategy,
    AdaptiveRetrievalStrategy
)
from .enhanced_error_handling import (
    ErrorRecoveryManager, QualityEvaluator, ErrorContext
)
from .performance_optimization import (
    PerformanceProfiler, CacheManager, ParallelExecutor,
    AdaptiveLoadBalancer, MemoryManager
)

logger = logging.getLogger('OptimizedRetrievalSystem')


@dataclass
class SystemConfiguration:
    """系统配置"""
    enable_caching: bool = True
    enable_parallel_processing: bool = True
    enable_error_recovery: bool = True
    enable_quality_evaluation: bool = True
    enable_performance_monitoring: bool = True
    max_concurrent_requests: int = 10
    cache_size: int = 1000
    cache_ttl: float = 3600
    default_timeout: float = 30.0
    quality_threshold: float = 0.1


class OptimizedRetrievalSystem:
    """优化的检索系统"""
    
    def __init__(self, 
                 cached_store=None,
                 multi_agent_system=None,
                 aliyun_enhanced_retrieval=None,
                 config: Optional[SystemConfiguration] = None):
        """
        初始化优化检索系统
        
        Args:
            cached_store: 缓存存储系统
            multi_agent_system: 多Agent系统
            aliyun_enhanced_retrieval: 阿里云增强检索
            config: 系统配置
        """
        self.config = config or SystemConfiguration()
        
        # 核心组件
        self.retrieval_manager = UnifiedRetrievalManager()
        self.error_recovery_manager = ErrorRecoveryManager()
        self.quality_evaluator = QualityEvaluator()
        self.performance_profiler = PerformanceProfiler()
        
        # 性能优化组件
        self.cache_manager = CacheManager(
            max_size=self.config.cache_size,
            ttl=self.config.cache_ttl
        ) if self.config.enable_caching else None
        
        self.parallel_executor = ParallelExecutor(
            max_workers=self.config.max_concurrent_requests
        ) if self.config.enable_parallel_processing else None
        
        self.memory_manager = MemoryManager()
        
        # 负载均衡器
        self.load_balancer = AdaptiveLoadBalancer([
            "hybrid", "agent_based", "aliyun_enhanced", "ensemble"
        ])
        
        # 注册检索策略
        self._register_strategies(cached_store, multi_agent_system, aliyun_enhanced_retrieval)
        
        # 统计信息
        self.request_count = 0
        self.success_count = 0
        self.error_count = 0
        
        logger.info("Optimized Retrieval System initialized")
    
    def _register_strategies(self, cached_store, multi_agent_system, aliyun_enhanced_retrieval):
        """注册检索策略"""
        strategies = []
        
        # 混合检索策略
        if cached_store:
            hybrid_strategy = HybridRetrievalStrategy(cached_store)
            self.retrieval_manager.register_strategy(hybrid_strategy, weight=1.0)
            strategies.append(hybrid_strategy)
            logger.info("Registered hybrid retrieval strategy")
        
        # Agent检索策略
        if multi_agent_system:
            agent_strategy = AgentBasedRetrievalStrategy(multi_agent_system)
            self.retrieval_manager.register_strategy(agent_strategy, weight=1.2)
            strategies.append(agent_strategy)
            logger.info("Registered agent-based retrieval strategy")
        
        # 阿里云增强检索策略
        if aliyun_enhanced_retrieval:
            aliyun_strategy = AliyunEnhancedRetrievalStrategy(aliyun_enhanced_retrieval)
            self.retrieval_manager.register_strategy(aliyun_strategy, weight=1.1)
            strategies.append(aliyun_strategy)
            logger.info("Registered Aliyun enhanced retrieval strategy")
        
        # 集成策略
        if len(strategies) > 1:
            ensemble_strategy = EnsembleRetrievalStrategy(strategies[:2])  # 使用前两个策略
            self.retrieval_manager.register_strategy(ensemble_strategy, weight=0.9)
            logger.info("Registered ensemble retrieval strategy")
        
        # 自适应策略
        if strategies:
            adaptive_strategy = AdaptiveRetrievalStrategy(strategies)
            self.retrieval_manager.register_strategy(adaptive_strategy, weight=1.3)
            logger.info("Registered adaptive retrieval strategy")
    
    async def retrieve(self, 
                      query: str,
                      user_id: str = "default",
                      session_id: str = "default",
                      top_k: int = 10,
                      strategy_hint: Optional[str] = None,
                      context_metadata: Optional[Dict[str, Any]] = None) -> List[RetrievalResult]:
        """
        执行优化检索
        
        Args:
            query: 查询文本
            user_id: 用户ID
            session_id: 会话ID
            top_k: 返回结果数量
            strategy_hint: 策略提示
            context_metadata: 上下文元数据
            
        Returns:
            检索结果列表
        """
        start_time = time.time()
        self.request_count += 1
        
        # 性能监控
        if self.config.enable_performance_monitoring:
            self.performance_profiler.start_operation("retrieve")
        
        # 内存跟踪
        self.memory_manager.track_memory("retrieve")
        
        try:
            # 检查缓存
            cache_key = None
            if self.config.enable_caching and self.cache_manager:
                cache_key = f"{query}_{user_id}_{top_k}"
                cached_result = self.cache_manager.get(cache_key)
                if cached_result:
                    logger.info(f"Cache hit for query: {query[:50]}...")
                    self.success_count += 1
                    return cached_result
            
            # 创建检索上下文
            context = RetrievalContext(
                query=query,
                user_id=user_id,
                session_id=session_id,
                top_k=top_k,
                timeout=self.config.default_timeout,
                cache_enabled=self.config.enable_caching,
                parallel_enabled=self.config.enable_parallel_processing,
                fallback_strategies=["hybrid", "agent_based"],
                quality_threshold=self.config.quality_threshold,
                metadata=context_metadata or {}
            )
            
            # 选择策略（如果没有提示，使用负载均衡器）
            if not strategy_hint:
                strategy_hint = self.load_balancer.select_strategy()
            
            # 执行检索
            results = await self.retrieval_manager.retrieve(context)
            
            # 质量评估
            if self.config.enable_quality_evaluation:
                execution_time = time.time() - start_time
                quality_metrics = self.quality_evaluator.evaluate_results(
                    results, query, execution_time
                )
                logger.debug(f"Quality metrics: relevance={quality_metrics.relevance_score:.3f}, "
                           f"diversity={quality_metrics.diversity_score:.3f}")
            
            # 缓存结果
            if self.config.enable_caching and self.cache_manager and cache_key and results:
                self.cache_manager.set(cache_key, results)
            
            # 报告负载均衡器
            execution_time = time.time() - start_time
            self.load_balancer.report_completion(strategy_hint, execution_time)
            
            self.success_count += 1
            logger.info(f"Retrieval completed successfully: {len(results)} results in {execution_time:.2f}s")
            
            return results
            
        except Exception as e:
            self.error_count += 1
            
            # 错误处理
            if self.config.enable_error_recovery:
                error_context = ErrorContext(
                    error_type=type(e).__name__,
                    error_message=str(e),
                    stack_trace="",  # 简化
                    timestamp=time.time(),
                    strategy_name=strategy_hint or "unknown",
                    query=query,
                    user_id=user_id,
                    session_id=session_id
                )
                
                recovery_result = await self.error_recovery_manager.handle_error(error_context)
                
                # 如果建议重试
                if recovery_result.get("should_retry", False):
                    retry_delay = recovery_result.get("retry_delay", 1.0)
                    logger.info(f"Retrying after {retry_delay}s...")
                    await asyncio.sleep(retry_delay)
                    
                    # 递归重试（简化版本，实际应该有重试计数）
                    return await self.retrieve(query, user_id, session_id, top_k, strategy_hint, context_metadata)
                
                # 如果有回退策略
                fallback_strategies = recovery_result.get("fallback_strategies", [])
                for fallback_strategy in fallback_strategies:
                    try:
                        logger.info(f"Trying fallback strategy: {fallback_strategy}")
                        return await self.retrieve(query, user_id, session_id, top_k, fallback_strategy, context_metadata)
                    except Exception as fallback_error:
                        logger.warning(f"Fallback strategy {fallback_strategy} failed: {fallback_error}")
                        continue
            
            logger.error(f"Retrieval failed: {e}")
            raise
            
        finally:
            # 性能监控结束
            if self.config.enable_performance_monitoring:
                additional_metrics = {}
                if self.cache_manager:
                    additional_metrics["cache_hit_rate"] = self.cache_manager.get_hit_rate()
                
                self.performance_profiler.end_operation("retrieve", additional_metrics)
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        status = {
            "request_count": self.request_count,
            "success_count": self.success_count,
            "error_count": self.error_count,
            "success_rate": self.success_count / max(self.request_count, 1),
            "memory_usage": self.memory_manager.get_total_memory_usage()
        }
        
        # 缓存状态
        if self.cache_manager:
            status["cache_hit_rate"] = self.cache_manager.get_hit_rate()
        
        # 负载均衡状态
        status["load_balancer"] = self.load_balancer.get_load_status()
        
        # 性能报告
        if self.config.enable_performance_monitoring:
            status["performance"] = self.performance_profiler.get_performance_report()
        
        # 错误统计
        if self.config.enable_error_recovery:
            status["error_statistics"] = self.error_recovery_manager.get_error_statistics()
        
        # 质量报告
        if self.config.enable_quality_evaluation:
            status["quality_report"] = self.quality_evaluator.get_quality_report()
        
        return status
    
    def get_optimization_recommendations(self) -> List[str]:
        """获取优化建议"""
        recommendations = []
        status = self.get_system_status()
        
        # 成功率检查
        if status["success_rate"] < 0.9:
            recommendations.append(f"Low success rate: {status['success_rate']:.1%}. Consider reviewing error handling.")
        
        # 缓存命中率检查
        if "cache_hit_rate" in status and status["cache_hit_rate"] < 0.3:
            recommendations.append(f"Low cache hit rate: {status['cache_hit_rate']:.1%}. Consider adjusting cache settings.")
        
        # 内存使用检查
        if status["memory_usage"] > 400:  # MB
            recommendations.append(f"High memory usage: {status['memory_usage']:.1f}MB. Consider memory optimization.")
        
        # 性能检查
        if "performance" in status:
            perf = status["performance"]
            if "avg_execution_time" in perf and perf["avg_execution_time"] > 10.0:
                recommendations.append(f"Slow average execution time: {perf['avg_execution_time']:.2f}s. Consider performance tuning.")
        
        return recommendations
    
    async def cleanup(self):
        """清理资源"""
        if self.parallel_executor:
            self.parallel_executor.shutdown()
        
        if self.cache_manager:
            self.cache_manager.clear()
        
        logger.info("Optimized Retrieval System cleaned up")


# 便捷函数
async def create_optimized_retrieval_system(
    cached_store=None,
    multi_agent_system=None,
    aliyun_enhanced_retrieval=None,
    **config_kwargs
) -> OptimizedRetrievalSystem:
    """创建优化检索系统的便捷函数"""
    config = SystemConfiguration(**config_kwargs)
    system = OptimizedRetrievalSystem(
        cached_store=cached_store,
        multi_agent_system=multi_agent_system,
        aliyun_enhanced_retrieval=aliyun_enhanced_retrieval,
        config=config
    )
    return system
