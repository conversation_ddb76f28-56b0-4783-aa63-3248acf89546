#!/usr/bin/env python3
"""
性能优化和并行处理模块
Performance Optimization and Parallel Processing Module
"""

import asyncio
import logging
import time
import threading
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional, Callable, Union, Tuple
from collections import defaultdict, deque
import concurrent.futures
import multiprocessing
from functools import wraps, lru_cache
import weakref
import gc

logger = logging.getLogger('PerformanceOptimization')


@dataclass
class PerformanceMetrics:
    """性能指标"""
    operation_name: str
    execution_time: float
    memory_usage: float
    cpu_usage: float
    cache_hit_rate: float
    throughput: float
    latency: float
    error_rate: float
    timestamp: float


class MemoryManager:
    """内存管理器"""
    
    def __init__(self, max_memory_mb: int = 512):
        self.max_memory_mb = max_memory_mb
        self.memory_usage = {}
        self.cleanup_threshold = 0.8
        
    def track_memory(self, operation_name: str):
        """跟踪内存使用"""
        import psutil
        process = psutil.Process()
        memory_info = process.memory_info()
        self.memory_usage[operation_name] = memory_info.rss / 1024 / 1024  # MB
        
        # 检查是否需要清理
        if self.get_total_memory_usage() > self.max_memory_mb * self.cleanup_threshold:
            self._cleanup_memory()
    
    def get_total_memory_usage(self) -> float:
        """获取总内存使用量"""
        import psutil
        process = psutil.Process()
        return process.memory_info().rss / 1024 / 1024  # MB
    
    def _cleanup_memory(self):
        """清理内存"""
        logger.info("Performing memory cleanup...")
        gc.collect()
        
        # 清理弱引用
        for obj in list(weakref.WeakSet()):
            if hasattr(obj, 'cleanup'):
                obj.cleanup()


class CacheManager:
    """缓存管理器"""
    
    def __init__(self, max_size: int = 1000, ttl: float = 3600):
        self.max_size = max_size
        self.ttl = ttl
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.access_times: Dict[str, float] = {}
        self.hit_count = 0
        self.miss_count = 0
        
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        if key in self.cache:
            cache_entry = self.cache[key]
            
            # 检查TTL
            if time.time() - cache_entry['timestamp'] < self.ttl:
                self.access_times[key] = time.time()
                self.hit_count += 1
                return cache_entry['value']
            else:
                # 过期，删除
                del self.cache[key]
                if key in self.access_times:
                    del self.access_times[key]
        
        self.miss_count += 1
        return None
    
    def set(self, key: str, value: Any):
        """设置缓存值"""
        # 检查缓存大小
        if len(self.cache) >= self.max_size:
            self._evict_lru()
        
        self.cache[key] = {
            'value': value,
            'timestamp': time.time()
        }
        self.access_times[key] = time.time()
    
    def _evict_lru(self):
        """LRU淘汰"""
        if not self.access_times:
            return
        
        # 找到最久未访问的key
        lru_key = min(self.access_times.items(), key=lambda x: x[1])[0]
        
        # 删除
        if lru_key in self.cache:
            del self.cache[lru_key]
        del self.access_times[lru_key]
    
    def get_hit_rate(self) -> float:
        """获取缓存命中率"""
        total = self.hit_count + self.miss_count
        return self.hit_count / total if total > 0 else 0.0
    
    def clear(self):
        """清空缓存"""
        self.cache.clear()
        self.access_times.clear()
        self.hit_count = 0
        self.miss_count = 0


class ParallelExecutor:
    """并行执行器"""
    
    def __init__(self, max_workers: int = None):
        self.max_workers = max_workers or min(32, (multiprocessing.cpu_count() or 1) + 4)
        self.thread_pool = concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers)
        self.process_pool = concurrent.futures.ProcessPoolExecutor(max_workers=min(4, multiprocessing.cpu_count() or 1))
        
    async def execute_parallel_async(self, 
                                   tasks: List[Callable],
                                   max_concurrent: int = None) -> List[Any]:
        """并行执行异步任务"""
        max_concurrent = max_concurrent or self.max_workers
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def bounded_task(task):
            async with semaphore:
                return await task()
        
        # 创建有界任务
        bounded_tasks = [bounded_task(task) for task in tasks]
        
        # 并行执行
        results = await asyncio.gather(*bounded_tasks, return_exceptions=True)
        
        return results
    
    async def execute_parallel_sync(self, 
                                  tasks: List[Callable],
                                  use_processes: bool = False) -> List[Any]:
        """并行执行同步任务"""
        loop = asyncio.get_event_loop()
        executor = self.process_pool if use_processes else self.thread_pool
        
        # 提交任务
        futures = [loop.run_in_executor(executor, task) for task in tasks]
        
        # 等待完成
        results = await asyncio.gather(*futures, return_exceptions=True)
        
        return results
    
    def shutdown(self):
        """关闭执行器"""
        self.thread_pool.shutdown(wait=True)
        self.process_pool.shutdown(wait=True)


class PerformanceProfiler:
    """性能分析器"""
    
    def __init__(self):
        self.metrics_history: List[PerformanceMetrics] = []
        self.active_operations: Dict[str, float] = {}
        
    def start_operation(self, operation_name: str):
        """开始操作计时"""
        self.active_operations[operation_name] = time.time()
    
    def end_operation(self, operation_name: str, 
                     additional_metrics: Optional[Dict[str, Any]] = None) -> PerformanceMetrics:
        """结束操作计时"""
        if operation_name not in self.active_operations:
            raise ValueError(f"Operation {operation_name} not started")
        
        start_time = self.active_operations.pop(operation_name)
        execution_time = time.time() - start_time
        
        # 获取系统指标
        memory_usage, cpu_usage = self._get_system_metrics()
        
        # 创建性能指标
        metrics = PerformanceMetrics(
            operation_name=operation_name,
            execution_time=execution_time,
            memory_usage=memory_usage,
            cpu_usage=cpu_usage,
            cache_hit_rate=additional_metrics.get('cache_hit_rate', 0.0) if additional_metrics else 0.0,
            throughput=additional_metrics.get('throughput', 0.0) if additional_metrics else 0.0,
            latency=execution_time,
            error_rate=additional_metrics.get('error_rate', 0.0) if additional_metrics else 0.0,
            timestamp=time.time()
        )
        
        self.metrics_history.append(metrics)
        return metrics
    
    def _get_system_metrics(self) -> Tuple[float, float]:
        """获取系统指标"""
        try:
            import psutil
            process = psutil.Process()
            memory_usage = process.memory_info().rss / 1024 / 1024  # MB
            cpu_usage = process.cpu_percent()
            return memory_usage, cpu_usage
        except ImportError:
            return 0.0, 0.0
    
    def get_performance_report(self, operation_name: Optional[str] = None) -> Dict[str, Any]:
        """获取性能报告"""
        if not self.metrics_history:
            return {"message": "No performance data available"}
        
        # 过滤指定操作
        metrics = self.metrics_history
        if operation_name:
            metrics = [m for m in metrics if m.operation_name == operation_name]
        
        if not metrics:
            return {"message": f"No data for operation: {operation_name}"}
        
        # 计算统计信息
        avg_execution_time = sum(m.execution_time for m in metrics) / len(metrics)
        avg_memory_usage = sum(m.memory_usage for m in metrics) / len(metrics)
        avg_cpu_usage = sum(m.cpu_usage for m in metrics) / len(metrics)
        avg_cache_hit_rate = sum(m.cache_hit_rate for m in metrics) / len(metrics)
        
        # 找出最慢的操作
        slowest_operation = max(metrics, key=lambda m: m.execution_time)
        
        return {
            "operation_name": operation_name or "all",
            "total_operations": len(metrics),
            "avg_execution_time": avg_execution_time,
            "avg_memory_usage": avg_memory_usage,
            "avg_cpu_usage": avg_cpu_usage,
            "avg_cache_hit_rate": avg_cache_hit_rate,
            "slowest_operation": {
                "name": slowest_operation.operation_name,
                "execution_time": slowest_operation.execution_time,
                "timestamp": slowest_operation.timestamp
            }
        }


def performance_monitor(operation_name: str = None):
    """性能监控装饰器"""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            profiler = getattr(func, '_profiler', PerformanceProfiler())
            op_name = operation_name or f"{func.__module__}.{func.__name__}"
            
            profiler.start_operation(op_name)
            try:
                result = await func(*args, **kwargs)
                return result
            finally:
                profiler.end_operation(op_name)
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            profiler = getattr(func, '_profiler', PerformanceProfiler())
            op_name = operation_name or f"{func.__module__}.{func.__name__}"
            
            profiler.start_operation(op_name)
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                profiler.end_operation(op_name)
        
        # 检查是否是协程函数
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


class BatchProcessor:
    """批处理器"""
    
    def __init__(self, batch_size: int = 10, max_wait_time: float = 1.0):
        self.batch_size = batch_size
        self.max_wait_time = max_wait_time
        self.pending_items: List[Any] = []
        self.pending_futures: List[asyncio.Future] = []
        self.last_batch_time = time.time()
        self.processing_lock = asyncio.Lock()
        
    async def add_item(self, item: Any) -> Any:
        """添加项目到批处理队列"""
        future = asyncio.Future()
        
        async with self.processing_lock:
            self.pending_items.append(item)
            self.pending_futures.append(future)
            
            # 检查是否需要处理批次
            should_process = (
                len(self.pending_items) >= self.batch_size or
                time.time() - self.last_batch_time >= self.max_wait_time
            )
            
            if should_process:
                await self._process_batch()
        
        return await future
    
    async def _process_batch(self):
        """处理当前批次"""
        if not self.pending_items:
            return
        
        items = self.pending_items.copy()
        futures = self.pending_futures.copy()
        
        # 清空队列
        self.pending_items.clear()
        self.pending_futures.clear()
        self.last_batch_time = time.time()
        
        try:
            # 处理批次（这里需要子类实现具体逻辑）
            results = await self._process_items(items)
            
            # 设置结果
            for future, result in zip(futures, results):
                if not future.done():
                    future.set_result(result)
                    
        except Exception as e:
            # 设置异常
            for future in futures:
                if not future.done():
                    future.set_exception(e)
    
    async def _process_items(self, items: List[Any]) -> List[Any]:
        """处理项目列表（需要子类实现）"""
        raise NotImplementedError("Subclasses must implement _process_items")


class AdaptiveLoadBalancer:
    """自适应负载均衡器"""
    
    def __init__(self, strategies: List[str]):
        self.strategies = strategies
        self.strategy_loads: Dict[str, int] = {strategy: 0 for strategy in strategies}
        self.strategy_performance: Dict[str, deque] = {strategy: deque(maxlen=100) for strategy in strategies}
        
    def select_strategy(self) -> str:
        """选择负载最低的策略"""
        # 计算每个策略的负载分数
        strategy_scores = {}
        
        for strategy in self.strategies:
            load = self.strategy_loads[strategy]
            performance_history = self.strategy_performance[strategy]
            
            # 计算平均性能
            avg_performance = sum(performance_history) / len(performance_history) if performance_history else 1.0
            
            # 负载分数 = 当前负载 / 平均性能
            strategy_scores[strategy] = load / max(avg_performance, 0.1)
        
        # 选择分数最低的策略
        best_strategy = min(strategy_scores.items(), key=lambda x: x[1])[0]
        
        # 增加负载
        self.strategy_loads[best_strategy] += 1
        
        return best_strategy
    
    def report_completion(self, strategy: str, execution_time: float):
        """报告策略完成"""
        # 减少负载
        if self.strategy_loads[strategy] > 0:
            self.strategy_loads[strategy] -= 1
        
        # 记录性能
        self.strategy_performance[strategy].append(execution_time)
    
    def get_load_status(self) -> Dict[str, Any]:
        """获取负载状态"""
        return {
            "current_loads": dict(self.strategy_loads),
            "avg_performance": {
                strategy: sum(history) / len(history) if history else 0.0
                for strategy, history in self.strategy_performance.items()
            }
        }
