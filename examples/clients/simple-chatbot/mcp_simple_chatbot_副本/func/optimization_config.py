#!/usr/bin/env python3
"""
优化配置文件
Optimization Configuration

用于调整工具选择优化的各种参数。
"""

from typing import Dict, Any
from enum import Enum
import json
from pathlib import Path


class OptimizationMode(Enum):
    """优化模式"""
    SPEED_FOCUSED = "speed_focused"      # 速度优先
    ACCURACY_FOCUSED = "accuracy_focused"  # 准确性优先
    BALANCED = "balanced"                # 平衡模式
    CUSTOM = "custom"                    # 自定义模式


class OptimizationConfig:
    """优化配置管理器"""
    
    # 默认配置
    DEFAULT_CONFIG = {
        "mode": OptimizationMode.BALANCED.value,
        
        # 自适应参数
        "adaptation_threshold": 0.1,
        "min_calls_for_adaptation": 10,
        
        # 性能权重
        "success_rate_weight": 0.7,
        "speed_weight": 0.3,
        
        # 超时设置
        "hybrid_timeout": 5.0,
        "agent_timeout": 10.0,
        "ensemble_timeout": 15.0,
        
        # 缓存设置
        "enable_result_caching": True,
        "cache_ttl": 3600,  # 1小时
        
        # 并发设置
        "max_concurrent_selections": 3,
        
        # 质量阈值
        "min_confidence_threshold": 0.1,
        "max_results_per_method": 20,
        
        # 预设模式配置
        "presets": {
            "speed_focused": {
                "adaptation_threshold": 0.05,
                "min_calls_for_adaptation": 5,
                "success_rate_weight": 0.5,
                "speed_weight": 0.5,
                "hybrid_timeout": 3.0,
                "agent_timeout": 5.0,
                "ensemble_timeout": 8.0
            },
            "accuracy_focused": {
                "adaptation_threshold": 0.15,
                "min_calls_for_adaptation": 20,
                "success_rate_weight": 0.8,
                "speed_weight": 0.2,
                "hybrid_timeout": 10.0,
                "agent_timeout": 20.0,
                "ensemble_timeout": 30.0
            },
            "balanced": {
                "adaptation_threshold": 0.1,
                "min_calls_for_adaptation": 10,
                "success_rate_weight": 0.7,
                "speed_weight": 0.3,
                "hybrid_timeout": 5.0,
                "agent_timeout": 10.0,
                "ensemble_timeout": 15.0
            }
        }
    }
    
    def __init__(self, config_file: str = "optimization_config.json"):
        """初始化配置管理器"""
        self.config_file = Path(config_file)
        self.config = self.DEFAULT_CONFIG.copy()
        self.load_config()
    
    def load_config(self) -> None:
        """加载配置文件"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    self.config.update(loaded_config)
                print(f"✅ Loaded optimization config from {self.config_file}")
            else:
                print(f"📝 Using default optimization config")
        except Exception as e:
            print(f"⚠️ Error loading config, using defaults: {e}")
    
    def save_config(self) -> None:
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            print(f"💾 Saved optimization config to {self.config_file}")
        except Exception as e:
            print(f"❌ Error saving config: {e}")
    
    def apply_preset(self, mode: OptimizationMode) -> None:
        """应用预设模式"""
        if mode == OptimizationMode.CUSTOM:
            return
        
        preset_name = mode.value
        if preset_name in self.config["presets"]:
            preset_config = self.config["presets"][preset_name]
            self.config.update(preset_config)
            self.config["mode"] = mode.value
            print(f"✅ Applied {mode.value} optimization preset")
        else:
            print(f"❌ Preset {preset_name} not found")
    
    def get(self, key: str, default=None):
        """获取配置值"""
        return self.config.get(key, default)
    
    def set(self, key: str, value: Any) -> None:
        """设置配置值"""
        self.config[key] = value
        self.config["mode"] = OptimizationMode.CUSTOM.value
    
    def get_current_mode(self) -> OptimizationMode:
        """获取当前模式"""
        mode_str = self.config.get("mode", OptimizationMode.BALANCED.value)
        try:
            return OptimizationMode(mode_str)
        except ValueError:
            return OptimizationMode.BALANCED
    
    def get_adaptation_params(self) -> Dict[str, Any]:
        """获取自适应参数"""
        return {
            "adaptation_threshold": self.get("adaptation_threshold", 0.1),
            "min_calls_for_adaptation": self.get("min_calls_for_adaptation", 10)
        }
    
    def get_performance_weights(self) -> Dict[str, float]:
        """获取性能权重"""
        return {
            "success_rate_weight": self.get("success_rate_weight", 0.7),
            "speed_weight": self.get("speed_weight", 0.3)
        }
    
    def get_timeout_settings(self) -> Dict[str, float]:
        """获取超时设置"""
        return {
            "hybrid_timeout": self.get("hybrid_timeout", 5.0),
            "agent_timeout": self.get("agent_timeout", 10.0),
            "ensemble_timeout": self.get("ensemble_timeout", 15.0)
        }
    
    def get_quality_settings(self) -> Dict[str, Any]:
        """获取质量设置"""
        return {
            "min_confidence_threshold": self.get("min_confidence_threshold", 0.1),
            "max_results_per_method": self.get("max_results_per_method", 20)
        }
    
    def print_current_config(self) -> None:
        """打印当前配置"""
        print("\n⚙️ Current Optimization Configuration")
        print("=" * 50)
        
        mode = self.get_current_mode()
        print(f"Mode: {mode.value}")
        
        print(f"\nAdaptation Parameters:")
        adaptation = self.get_adaptation_params()
        for key, value in adaptation.items():
            print(f"  {key}: {value}")
        
        print(f"\nPerformance Weights:")
        weights = self.get_performance_weights()
        for key, value in weights.items():
            print(f"  {key}: {value}")
        
        print(f"\nTimeout Settings:")
        timeouts = self.get_timeout_settings()
        for key, value in timeouts.items():
            print(f"  {key}: {value}s")
        
        print(f"\nQuality Settings:")
        quality = self.get_quality_settings()
        for key, value in quality.items():
            print(f"  {key}: {value}")
    
    def interactive_config(self) -> None:
        """交互式配置"""
        print("\n⚙️ Interactive Optimization Configuration")
        print("=" * 50)
        
        # 选择模式
        print("\nAvailable modes:")
        for i, mode in enumerate(OptimizationMode, 1):
            print(f"  {i}. {mode.value}")
        
        try:
            choice = input("\nSelect mode (1-4 or Enter to keep current): ").strip()
            if choice:
                mode_index = int(choice) - 1
                modes = list(OptimizationMode)
                if 0 <= mode_index < len(modes):
                    selected_mode = modes[mode_index]
                    if selected_mode != OptimizationMode.CUSTOM:
                        self.apply_preset(selected_mode)
                        return
        except (ValueError, IndexError):
            print("Invalid choice, keeping current mode")
        
        # 自定义配置
        if self.get_current_mode() == OptimizationMode.CUSTOM:
            print("\n🔧 Custom Configuration")
            
            # 自适应阈值
            try:
                threshold = input(f"Adaptation threshold (current: {self.get('adaptation_threshold')}): ").strip()
                if threshold:
                    self.set("adaptation_threshold", float(threshold))
            except ValueError:
                print("Invalid threshold value")
            
            # 最小调用次数
            try:
                min_calls = input(f"Min calls for adaptation (current: {self.get('min_calls_for_adaptation')}): ").strip()
                if min_calls:
                    self.set("min_calls_for_adaptation", int(min_calls))
            except ValueError:
                print("Invalid min calls value")
            
            # 成功率权重
            try:
                success_weight = input(f"Success rate weight (current: {self.get('success_rate_weight')}): ").strip()
                if success_weight:
                    weight = float(success_weight)
                    self.set("success_rate_weight", weight)
                    self.set("speed_weight", 1.0 - weight)
            except ValueError:
                print("Invalid weight value")
        
        # 保存配置
        save_choice = input("\nSave configuration? (y/N): ").strip().lower()
        if save_choice == 'y':
            self.save_config()


# 全局配置实例
optimization_config = OptimizationConfig()
