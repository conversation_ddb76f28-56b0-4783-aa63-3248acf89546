"""
阿里云增强检索配置管理
"""

import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class AliyunRetrievalConfig:
    """阿里云增强检索配置类"""

    # 默认配置
    DEFAULT_CONFIG = {
        # 权重配置
        "weights": {
            "intent_weight": 0,      # 意图匹配权重
            "rerank_weight": 0.3,      # 重排序权重
            "original_weight": 0.7     # 原始相似度权重
        },


        # 文本重排序配置
        "text_rerank": {
            "enabled": True,           # 是否启用文本重排序
            "model": "gte-rerank-v2",
            "max_documents": 50,       # 最大重排序文档数
            "top_n_ratio": 3.0         # 初始检索倍数（相对于最终top_k）
        },

        # 检索配置
        "retrieval": {
            "default_top_k": 10,       # 默认返回结果数
            "max_top_k": 50,           # 最大返回结果数
            "fallback_enabled": True,  # 是否启用回退机制
            "cache_results": True      # 是否缓存检索结果
        },

        # 性能配置
        "performance": {
            "timeout": 30.0,           # 请求超时时间（秒）
            "retry_count": 2,          # 重试次数
            "concurrent_requests": 5,  # 并发请求数
            "enable_metrics": True     # 是否启用性能指标
        }
    }

    def __init__(self, config_file: str = "aliyun_retrieval_config.json"):
        """
        初始化配置管理器

        Args:
            config_file: 配置文件路径
        """
        self.config_file = Path(config_file)
        self.config = self.DEFAULT_CONFIG.copy()
        self._load_config()

    def _load_config(self):
        """加载配置文件"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)

                # 递归合并配置
                self._merge_config(self.config, user_config)
                logger.info(f"已加载配置文件: {self.config_file}")
            else:
                logger.info("配置文件不存在，使用默认配置")
                self.save_config()  # 保存默认配置
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}，使用默认配置")

    def _merge_config(self, default: Dict[str, Any], user: Dict[str, Any]):
        """递归合并配置"""
        for key, value in user.items():
            if key in default:
                if isinstance(default[key], dict) and isinstance(value, dict):
                    self._merge_config(default[key], value)
                else:
                    default[key] = value
            else:
                default[key] = value

    def save_config(self):
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            logger.info(f"配置已保存到: {self.config_file}")
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")

    def get(self, key_path: str, default=None):
        """
        获取配置值

        Args:
            key_path: 配置键路径，如 "weights.intent_weight"
            default: 默认值

        Returns:
            配置值
        """
        keys = key_path.split('.')
        value = self.config

        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default

    def set(self, key_path: str, value: Any):
        """
        设置配置值

        Args:
            key_path: 配置键路径，如 "weights.intent_weight"
            value: 配置值
        """
        keys = key_path.split('.')
        config = self.config

        # 导航到父级配置
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]

        # 设置值
        config[keys[-1]] = value
        logger.info(f"配置已更新: {key_path} = {value}")

    def get_weights(self) -> Dict[str, float]:
        """获取权重配置"""
        return self.config["weights"].copy()

    def set_weights(self,
                   intent_weight: float,
                   rerank_weight: float,
                   original_weight: float):
        """
        设置权重配置

        Args:
            intent_weight: 意图匹配权重
            rerank_weight: 重排序权重
            original_weight: 原始相似度权重
        """
        total = intent_weight + rerank_weight + original_weight
        if abs(total - 1.0) > 0.01:
            raise ValueError(f"权重总和必须为1.0，当前为: {total}")

        self.config["weights"] = {
            "intent_weight": intent_weight,
            "rerank_weight": rerank_weight,
            "original_weight": original_weight
        }

        logger.info(f"权重配置已更新: 意图={intent_weight:.2f}, 重排序={rerank_weight:.2f}, 原始={original_weight:.2f}")

    def get_presets(self) -> Dict[str, Dict[str, Any]]:
        """获取预定义的权重预设"""
        return {
            "intent_focused": {
                "intent_weight": 0.5,
                "rerank_weight": 0.3,
                "original_weight": 0.2,
                "description": "优先考虑意图匹配"
            },
            "rerank_focused": {
                "intent_weight": 0.2,
                "rerank_weight": 0.6,
                "original_weight": 0.2,
                "description": "优先考虑重排序结果"
            },
            "original_focused": {
                "intent_weight": 0.2,
                "rerank_weight": 0.2,
                "original_weight": 0.6,
                "description": "优先考虑原始相似度"
            },
            "balanced": {
                "intent_weight": 0.3,
                "rerank_weight": 0.4,
                "original_weight": 0.3,
                "description": "平衡所有因素（默认）"
            },
            "ai_enhanced": {
                "intent_weight": 0.4,
                "rerank_weight": 0.5,
                "original_weight": 0.1,
                "description": "最大化AI增强效果"
            }
        }

    def apply_preset(self, preset_name: str):
        """
        应用预定义权重预设

        Args:
            preset_name: 预设名称

        Returns:
            str: 预设描述
        """
        presets = self.get_presets()
        if preset_name not in presets:
            available = ', '.join(presets.keys())
            raise ValueError(f"未知预设'{preset_name}'。可用预设：{available}")

        preset = presets[preset_name]
        self.set_weights(
            preset["intent_weight"],
            preset["rerank_weight"],
            preset["original_weight"]
        )

        return preset["description"]

    def is_intent_enabled(self) -> bool:
        """检查是否启用意图检测"""
        return self.get("intent_detection.enabled", False)

    def is_rerank_enabled(self) -> bool:
        """检查是否启用文本重排序"""
        return self.get("text_rerank.enabled", True)

    def get_max_tools_for_intent(self) -> int:
        """获取意图检测的最大工具数"""
        return self.get("intent_detection.max_tools", 10)

    def get_max_documents_for_rerank(self) -> int:
        """获取重排序的最大文档数"""
        return self.get("text_rerank.max_documents", 50)

    def get_top_n_ratio(self) -> float:
        """获取初始检索倍数"""
        return self.get("text_rerank.top_n_ratio", 3.0)

    def get_timeout(self) -> float:
        """获取请求超时时间"""
        return self.get("performance.timeout", 30.0)

    def get_retry_count(self) -> int:
        """获取重试次数"""
        return self.get("performance.retry_count", 2)

    def print_current_config(self):
        """打印当前配置"""
        print("\n⚙️ 阿里云增强检索配置")
        print("=" * 50)

        # 权重配置
        weights = self.get_weights()
        print(f"\n🎯 权重配置:")
        print(f"  意图匹配权重:    {weights['intent_weight']:.2f}")
        print(f"  重排序权重:      {weights['rerank_weight']:.2f}")
        print(f"  原始相似度权重:  {weights['original_weight']:.2f}")

        # 功能开关
        print(f"\n🔧 功能开关:")
        # print(f"  意图检测:        {'启用' if self.is_intent_enabled() else '禁用'}")
        print(f"  文本重排序:      {'启用' if self.is_rerank_enabled() else '禁用'}")
        print(f"  回退机制:        {'启用' if self.get('retrieval.fallback_enabled') else '禁用'}")

        # 性能参数
        print(f"\n⚡ 性能参数:")
        print(f"  请求超时:        {self.get_timeout():.1f}s")
        print(f"  重试次数:        {self.get_retry_count()}")
        print(f"  最大工具数:      {self.get_max_tools_for_intent()}")
        print(f"  最大文档数:      {self.get_max_documents_for_rerank()}")


# 全局配置实例
aliyun_config = AliyunRetrievalConfig()
