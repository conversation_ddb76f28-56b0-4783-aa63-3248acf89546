#!/usr/bin/env python3
"""
增强错误处理和评估系统
Enhanced Error Handling and Evaluation System
"""

import asyncio
import logging
import time
import traceback
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from enum import Enum
from typing import Dict, List, Any, Optional, Callable, Union
from collections import defaultdict, deque
import json
from pathlib import Path

logger = logging.getLogger('EnhancedErrorHandling')


class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class RecoveryAction(Enum):
    """恢复动作"""
    RETRY = "retry"
    FALLBACK = "fallback"
    CIRCUIT_BREAK = "circuit_break"
    ESCALATE = "escalate"
    IGNORE = "ignore"


@dataclass
class ErrorContext:
    """错误上下文"""
    error_type: str
    error_message: str
    stack_trace: str
    timestamp: float
    strategy_name: str
    query: str
    user_id: str
    session_id: str
    retry_count: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class RecoveryPlan:
    """恢复计划"""
    action: RecoveryAction
    fallback_strategies: List[str]
    max_retries: int
    retry_delay: float
    escalation_threshold: int
    custom_handler: Optional[Callable] = None


@dataclass
class QualityMetrics:
    """质量指标"""
    relevance_score: float
    diversity_score: float
    coverage_score: float
    freshness_score: float
    user_satisfaction: float
    response_time: float
    success_rate: float


class ErrorClassifier:
    """错误分类器"""
    
    def __init__(self):
        self.error_patterns = {
            "timeout": {
                "keywords": ["timeout", "time out", "timed out"],
                "severity": ErrorSeverity.MEDIUM,
                "recovery": RecoveryAction.RETRY
            },
            "api_error": {
                "keywords": ["api", "http", "connection", "network"],
                "severity": ErrorSeverity.HIGH,
                "recovery": RecoveryAction.FALLBACK
            },
            "validation_error": {
                "keywords": ["validation", "invalid", "missing"],
                "severity": ErrorSeverity.LOW,
                "recovery": RecoveryAction.IGNORE
            },
            "resource_error": {
                "keywords": ["memory", "disk", "resource", "limit"],
                "severity": ErrorSeverity.CRITICAL,
                "recovery": RecoveryAction.CIRCUIT_BREAK
            }
        }
    
    def classify_error(self, error_context: ErrorContext) -> Dict[str, Any]:
        """分类错误"""
        error_text = f"{error_context.error_message} {error_context.stack_trace}".lower()
        
        for error_type, pattern in self.error_patterns.items():
            if any(keyword in error_text for keyword in pattern["keywords"]):
                return {
                    "classified_type": error_type,
                    "severity": pattern["severity"],
                    "recommended_action": pattern["recovery"],
                    "confidence": 0.8
                }
        
        return {
            "classified_type": "unknown",
            "severity": ErrorSeverity.MEDIUM,
            "recommended_action": RecoveryAction.RETRY,
            "confidence": 0.3
        }


class ErrorRecoveryManager:
    """错误恢复管理器"""
    
    def __init__(self):
        self.error_classifier = ErrorClassifier()
        self.recovery_plans: Dict[str, RecoveryPlan] = {}
        self.error_history: List[ErrorContext] = []
        self.recovery_stats = defaultdict(int)
        
        # 默认恢复计划
        self._setup_default_recovery_plans()
    
    def _setup_default_recovery_plans(self):
        """设置默认恢复计划"""
        self.recovery_plans.update({
            "timeout": RecoveryPlan(
                action=RecoveryAction.RETRY,
                fallback_strategies=["hybrid", "agent_based"],
                max_retries=3,
                retry_delay=2.0,
                escalation_threshold=5
            ),
            "api_error": RecoveryPlan(
                action=RecoveryAction.FALLBACK,
                fallback_strategies=["hybrid", "cached"],
                max_retries=1,
                retry_delay=5.0,
                escalation_threshold=3
            ),
            "validation_error": RecoveryPlan(
                action=RecoveryAction.IGNORE,
                fallback_strategies=[],
                max_retries=0,
                retry_delay=0.0,
                escalation_threshold=1
            ),
            "resource_error": RecoveryPlan(
                action=RecoveryAction.CIRCUIT_BREAK,
                fallback_strategies=["simple_fallback"],
                max_retries=0,
                retry_delay=0.0,
                escalation_threshold=1
            )
        })
    
    async def handle_error(self, error_context: ErrorContext) -> Dict[str, Any]:
        """处理错误"""
        # 记录错误
        self.error_history.append(error_context)
        
        # 分类错误
        classification = self.error_classifier.classify_error(error_context)
        
        # 获取恢复计划
        error_type = classification["classified_type"]
        recovery_plan = self.recovery_plans.get(error_type, self.recovery_plans["timeout"])
        
        # 执行恢复动作
        recovery_result = await self._execute_recovery(error_context, recovery_plan, classification)
        
        # 更新统计
        self.recovery_stats[f"{error_type}_{recovery_plan.action.value}"] += 1
        
        logger.info(f"Error handled: {error_type} -> {recovery_plan.action.value}")
        
        return {
            "error_classification": classification,
            "recovery_plan": recovery_plan,
            "recovery_result": recovery_result,
            "should_retry": recovery_result.get("should_retry", False),
            "fallback_strategies": recovery_result.get("fallback_strategies", [])
        }
    
    async def _execute_recovery(self, 
                              error_context: ErrorContext, 
                              recovery_plan: RecoveryPlan,
                              classification: Dict[str, Any]) -> Dict[str, Any]:
        """执行恢复动作"""
        action = recovery_plan.action
        
        if action == RecoveryAction.RETRY:
            return await self._handle_retry(error_context, recovery_plan)
        elif action == RecoveryAction.FALLBACK:
            return await self._handle_fallback(error_context, recovery_plan)
        elif action == RecoveryAction.CIRCUIT_BREAK:
            return await self._handle_circuit_break(error_context, recovery_plan)
        elif action == RecoveryAction.ESCALATE:
            return await self._handle_escalate(error_context, recovery_plan)
        else:  # IGNORE
            return {"should_retry": False, "message": "Error ignored"}
    
    async def _handle_retry(self, error_context: ErrorContext, recovery_plan: RecoveryPlan) -> Dict[str, Any]:
        """处理重试"""
        if error_context.retry_count >= recovery_plan.max_retries:
            return {
                "should_retry": False,
                "message": "Max retries exceeded",
                "fallback_strategies": recovery_plan.fallback_strategies
            }
        
        # 计算延迟时间
        delay = recovery_plan.retry_delay * (2 ** error_context.retry_count)
        
        return {
            "should_retry": True,
            "retry_delay": delay,
            "message": f"Retrying after {delay}s (attempt {error_context.retry_count + 1})"
        }
    
    async def _handle_fallback(self, error_context: ErrorContext, recovery_plan: RecoveryPlan) -> Dict[str, Any]:
        """处理回退"""
        return {
            "should_retry": False,
            "fallback_strategies": recovery_plan.fallback_strategies,
            "message": "Using fallback strategies"
        }
    
    async def _handle_circuit_break(self, error_context: ErrorContext, recovery_plan: RecoveryPlan) -> Dict[str, Any]:
        """处理熔断"""
        return {
            "should_retry": False,
            "circuit_break": True,
            "message": "Circuit breaker activated"
        }
    
    async def _handle_escalate(self, error_context: ErrorContext, recovery_plan: RecoveryPlan) -> Dict[str, Any]:
        """处理升级"""
        return {
            "should_retry": False,
            "escalate": True,
            "message": "Error escalated to higher level"
        }
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计"""
        if not self.error_history:
            return {"total_errors": 0}
        
        # 按类型统计
        error_types = defaultdict(int)
        error_strategies = defaultdict(int)
        recent_errors = []
        
        for error in self.error_history[-100:]:  # 最近100个错误
            classification = self.error_classifier.classify_error(error)
            error_types[classification["classified_type"]] += 1
            error_strategies[error.strategy_name] += 1
            
            if time.time() - error.timestamp < 3600:  # 最近1小时
                recent_errors.append(error)
        
        return {
            "total_errors": len(self.error_history),
            "recent_errors": len(recent_errors),
            "error_types": dict(error_types),
            "error_by_strategy": dict(error_strategies),
            "recovery_stats": dict(self.recovery_stats),
            "error_rate": len(recent_errors) / max(1, len(self.error_history[-100:]))
        }


class QualityEvaluator:
    """质量评估器"""
    
    def __init__(self):
        self.evaluation_history: List[QualityMetrics] = []
        self.quality_thresholds = {
            "relevance_score": 0.7,
            "diversity_score": 0.5,
            "coverage_score": 0.6,
            "response_time": 10.0,
            "success_rate": 0.8
        }
    
    def evaluate_results(self, 
                        results: List[Any], 
                        query: str,
                        execution_time: float,
                        user_feedback: Optional[Dict[str, Any]] = None) -> QualityMetrics:
        """评估检索结果质量"""
        
        # 计算相关性得分
        relevance_score = self._calculate_relevance(results, query)
        
        # 计算多样性得分
        diversity_score = self._calculate_diversity(results)
        
        # 计算覆盖度得分
        coverage_score = self._calculate_coverage(results, query)
        
        # 计算新鲜度得分
        freshness_score = self._calculate_freshness(results)
        
        # 用户满意度（如果有反馈）
        user_satisfaction = self._extract_user_satisfaction(user_feedback)
        
        # 成功率
        success_rate = 1.0 if results else 0.0
        
        metrics = QualityMetrics(
            relevance_score=relevance_score,
            diversity_score=diversity_score,
            coverage_score=coverage_score,
            freshness_score=freshness_score,
            user_satisfaction=user_satisfaction,
            response_time=execution_time,
            success_rate=success_rate
        )
        
        self.evaluation_history.append(metrics)
        return metrics
    
    def _calculate_relevance(self, results: List[Any], query: str) -> float:
        """计算相关性得分"""
        if not results:
            return 0.0
        
        # 基于置信度的简单相关性计算
        total_confidence = sum(getattr(result, 'confidence', 0.5) for result in results)
        return min(total_confidence / len(results), 1.0)
    
    def _calculate_diversity(self, results: List[Any]) -> float:
        """计算多样性得分"""
        if len(results) <= 1:
            return 0.0
        
        # 基于工具名称的简单多样性计算
        unique_tools = set(getattr(result, 'tool_name', '') for result in results)
        return len(unique_tools) / len(results)
    
    def _calculate_coverage(self, results: List[Any], query: str) -> float:
        """计算覆盖度得分"""
        if not results:
            return 0.0
        
        # 简单的覆盖度计算：结果数量与查询复杂度的比率
        query_complexity = len(query.split())
        coverage = min(len(results) / max(query_complexity, 1), 1.0)
        return coverage
    
    def _calculate_freshness(self, results: List[Any]) -> float:
        """计算新鲜度得分"""
        # 简单返回固定值，实际应该基于工具的更新时间
        return 0.8
    
    def _extract_user_satisfaction(self, user_feedback: Optional[Dict[str, Any]]) -> float:
        """提取用户满意度"""
        if not user_feedback:
            return 0.5  # 默认中性值
        
        return user_feedback.get("satisfaction", 0.5)
    
    def get_quality_report(self) -> Dict[str, Any]:
        """获取质量报告"""
        if not self.evaluation_history:
            return {"message": "No evaluation data available"}
        
        recent_metrics = self.evaluation_history[-100:]  # 最近100次评估
        
        avg_metrics = {
            "avg_relevance": sum(m.relevance_score for m in recent_metrics) / len(recent_metrics),
            "avg_diversity": sum(m.diversity_score for m in recent_metrics) / len(recent_metrics),
            "avg_coverage": sum(m.coverage_score for m in recent_metrics) / len(recent_metrics),
            "avg_response_time": sum(m.response_time for m in recent_metrics) / len(recent_metrics),
            "avg_success_rate": sum(m.success_rate for m in recent_metrics) / len(recent_metrics),
            "avg_user_satisfaction": sum(m.user_satisfaction for m in recent_metrics) / len(recent_metrics)
        }
        
        # 质量问题检测
        quality_issues = []
        for metric, threshold in self.quality_thresholds.items():
            if metric in avg_metrics:
                if metric == "response_time":
                    if avg_metrics[f"avg_{metric}"] > threshold:
                        quality_issues.append(f"Response time too slow: {avg_metrics[f'avg_{metric}']:.2f}s > {threshold}s")
                else:
                    if avg_metrics[f"avg_{metric}"] < threshold:
                        quality_issues.append(f"Low {metric}: {avg_metrics[f'avg_{metric}']:.2f} < {threshold}")
        
        return {
            "total_evaluations": len(self.evaluation_history),
            "recent_evaluations": len(recent_metrics),
            "average_metrics": avg_metrics,
            "quality_issues": quality_issues,
            "overall_score": sum(avg_metrics.values()) / len(avg_metrics)
        }
