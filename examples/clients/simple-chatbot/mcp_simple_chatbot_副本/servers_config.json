{"mcpServers": {"哔哩哔哩": {"name": "哔哩哔哩", "type": "stdio", "command": "node", "args": ["/Users/<USER>/Desktop/mcp/mcp-servers/typescript/mcp-bilibili/dist/index.js"], "disabledTools": []}, "图书信息查询": {"name": "图书信息查询", "type": "stdio", "command": "node", "args": ["/Users/<USER>/Desktop/mcp/mcp-servers/typescript/mcp-book-info/dist/index.js"], "disabledTools": [], "env": {"JISU_ALIYUN_APPCODE": "eaa9cae795c340008f86706062c5508c"}}, "计算机环境": {"name": "计算机环境", "type": "stdio", "command": "node", "args": ["/Users/<USER>/Desktop/mcp/mcp-servers/typescript/mcp-computer-env/dist/index.js"], "disabledTools": []}, "今日热点": {"name": "今日热点", "type": "stdio", "command": "node", "args": ["/Users/<USER>/Desktop/mcp/mcp-servers/typescript/mcp-daily-hot-list/dist/index.js"], "disabledTools": []}, "对话框": {"name": "对话框", "type": "stdio", "command": "node", "args": ["/Users/<USER>/Desktop/mcp/mcp-servers/typescript/mcp-dialog/dist/index.js"], "disabledTools": []}, "文档信息": {"name": "文档信息", "type": "stdio", "command": "node", "args": ["/Users/<USER>/Desktop/mcp/mcp-servers/typescript/mcp-doc-info/dist/index.js"], "disabledTools": []}, "企业信息": {"name": "企业信息", "type": "stdio", "command": "node", "args": ["/Users/<USER>/Desktop/mcp/mcp-servers/typescript/mcp-enterprise-info/dist/index.js"], "disabledTools": [], "env": {"ENTERPRISE_INFO_API_KEY": "eaa9cae795c340008f86706062c5508c"}}, "汇率转换": {"name": "汇率转换", "type": "stdio", "command": "node", "args": ["/Users/<USER>/Desktop/mcp/mcp-servers/typescript/mcp-exchange-rate/dist/index.js"], "disabledTools": [], "env": {"EXCHANGE_RATE_API_KEY": "eaa9cae795c340008f86706062c5508c"}}, "文件压缩": {"name": "文件压缩", "type": "stdio", "command": "node", "args": ["/Users/<USER>/Desktop/mcp/mcp-servers/typescript/mcp-file-compression/dist/index.js"], "disabledTools": []}, "文件选择": {"name": "文件选择", "type": "stdio", "command": "node", "args": ["/Users/<USER>/Desktop/mcp/mcp-servers/typescript/mcp-file-select/dist/index.js"], "disabledTools": []}, "获取位置": {"name": "获取位置", "type": "stdio", "command": "node", "args": ["/Users/<USER>/Desktop/mcp/mcp-servers/typescript/mcp-get-location/dist/index.js"], "disabledTools": []}, "下班": {"name": "下班", "type": "stdio", "command": "node", "args": ["/Users/<USER>/Desktop/mcp/mcp-servers/typescript/mcp-get-off-work/dist/index.js"], "disabledTools": []}, "图像处理": {"name": "图像处理", "type": "stdio", "command": "node", "args": ["/Users/<USER>/Desktop/mcp/mcp-servers/typescript/mcp-image-processor/dist/index.js"], "disabledTools": []}, "IP查询": {"name": "IP查询", "type": "stdio", "command": "node", "args": ["/Users/<USER>/Desktop/mcp/mcp-servers/typescript/mcp-ip-query/dist/index.js"], "disabledTools": [], "env": {"ALIYUN_IP_API_KEY": "eaa9cae795c340008f86706062c5508c"}}, "数学计算": {"name": "数学计算", "type": "stdio", "command": "node", "args": ["/Users/<USER>/Desktop/mcp/mcp-servers/typescript/mcp-math-eval/dist/index.js"], "disabledTools": []}, "电影信息": {"name": "电影信息", "type": "stdio", "command": "node", "args": ["/Users/<USER>/Desktop/mcp/mcp-servers/typescript/mcp-movie-info/dist/index.js"], "disabledTools": [], "env": {"MOVIE_API_KEY": "eaa9cae795c340008f86706062c5508c"}}, "新闻头条": {"name": "新闻头条", "type": "stdio", "command": "node", "args": ["/Users/<USER>/Desktop/mcp/mcp-servers/typescript/mcp-news-headlines/dist/index.js"], "disabledTools": [], "env": {"NEWS_API_KEY": "eaa9cae795c340008f86706062c5508c"}}, "通知": {"name": "通知", "type": "stdio", "command": "node", "args": ["/Users/<USER>/Desktop/mcp/mcp-servers/typescript/mcp-notification/dist/index.js"], "disabledTools": []}, "程序启动": {"name": "程序启动", "type": "stdio", "command": "node", "args": ["/Users/<USER>/Desktop/mcp/mcp-servers/typescript/mcp-open-app/dist/index.js"], "disabledTools": []}, "网页打开": {"name": "网页打开", "type": "stdio", "command": "node", "args": ["/Users/<USER>/Desktop/mcp/mcp-servers/typescript/mcp-open-web/dist/index.js"], "disabledTools": []}, "手机号码归属地": {"name": "手机号码归属地", "type": "stdio", "command": "node", "args": ["/Users/<USER>/Desktop/mcp/mcp-servers/typescript/mcp-phone-location/dist/index.js"], "disabledTools": []}, "二维码生成": {"name": "二维码生成", "type": "stdio", "command": "node", "args": ["/Users/<USER>/Desktop/mcp/mcp-servers/typescript/mcp-qrcode/dist/index.js"], "disabledTools": []}, "截图MCP服务器": {"name": "截图MCP服务器", "type": "stdio", "command": "node", "args": ["/Users/<USER>/Desktop/mcp/mcp-servers/typescript/mcp-screenshot/dist/index.js"], "disabledTools": []}, "网速测试": {"name": "网速测试", "type": "stdio", "command": "node", "args": ["/Users/<USER>/Desktop/mcp/mcp-servers/typescript/mcp-speed-tester/dist/index.js"], "disabledTools": []}, "分屏": {"name": "分屏", "type": "stdio", "command": "node", "args": ["/Users/<USER>/Desktop/mcp/mcp-servers/typescript/mcp-split-screen/dist/index.js"], "disabledTools": []}, "系统清理": {"name": "系统清理", "type": "stdio", "command": "node", "args": ["/Users/<USER>/Desktop/mcp/mcp-servers/typescript/mcp-system-cleaner/dist/index.js"], "disabledTools": []}, "交通信息查询": {"name": "交通信息查询", "type": "stdio", "command": "node", "args": ["/Users/<USER>/Desktop/mcp/mcp-servers/typescript/mcp-traffic-info/dist/index.js"], "disabledTools": [], "env": {"OIL_PRICE_API_KEY": "eaa9cae795c340008f86706062c5508c"}}, "12306车票查询": {"name": "12306车票查询", "type": "stdio", "command": "node", "args": ["/Users/<USER>/Desktop/mcp/mcp-servers/other/12306-mcp/build/index.js"], "disabledTools": []}, "高德地图": {"name": "高德地图", "type": "stdio", "command": "node", "args": ["/Users/<USER>/Desktop/mcp/mcp-servers/other/amap-maps-mcp-server/build/index.js"], "disabledTools": [], "env": {"AMAP_MAPS_API_KEY": "eaa9cae795c340008f86706062c5508c"}}, "怎么做菜": {"name": "怎么做菜", "type": "stdio", "command": "node", "args": ["/Users/<USER>/Desktop/mcp/mcp-servers/other/howtocook-mcp/build/index.js"], "disabledTools": []}, "热点新闻": {"name": "热点新闻", "type": "stdio", "command": "node", "args": ["/Users/<USER>/Desktop/mcp/mcp-servers/other/mcp-server-hotnews/build/index.js"], "disabledTools": []}}}