import asyncio
import json
import logging
import os
import shutil
import pickle
import hashlib
import sys
from contextlib import AsyncExitStack
from typing import Any, Dict, List, Optional
from pathlib import Path

import httpx
from dotenv import load_dotenv
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

import math
import types
import uuid

from langchain.chat_models import init_chat_model
from langchain.embeddings import init_embeddings
from langgraph.store.memory import InMemoryStore


# 导入优化的工具选择系统
from func.optimized_tool_selection import (
    OptimizedToolSelector,
    SelectionStrategy,
)
from func.optimization_config import optimization_config

try:
    from func.enhanced_tool_selector import (
        EnhancedToolSelector,
        EnhancedToolResult,
        create_enhanced_tool_selector
    )
    from func.multi_tool_query_processor import (
        MultiToolRetriever,
        QueryComplexity
    )
    ENHANCED_TOOL_SELECTOR_AVAILABLE = True
except ImportError as e:
    logging.warning(f"Enhanced tool selector not available: {e}")
    ENHANCED_TOOL_SELECTOR_AVAILABLE = False

# 导入新的优化检索系统
try:
    from func.optimized_retrieval_system import (
        OptimizedRetrievalSystem,
        SystemConfiguration,
        create_optimized_retrieval_system
    )
    OPTIMIZED_RETRIEVAL_AVAILABLE = True
except ImportError as e:
    logging.warning(f"Optimized retrieval system not available: {e}")
    OPTIMIZED_RETRIEVAL_AVAILABLE = False

# 导入阿里云增强检索系统
try:
    from func.aliyun_enhanced_retrieval import (
        AliyunEnhancedRetrieval,
        AliyunTextReranker,
        EnhancedRetrievalResult
    )
    from func.aliyun_config import aliyun_config
    ALIYUN_ENHANCED_AVAILABLE = True
except ImportError as e:
    logging.warning(f"Aliyun enhanced retrieval not available: {e}")
    ALIYUN_ENHANCED_AVAILABLE = False

# 删除增强功能导入
ENHANCED_FEATURES_AVAILABLE = False
from langgraph.graph import StateGraph, MessagesState
from langchain_core.messages import HumanMessage, AIMessage

# Import similarity configuration
sys.path.append(str(Path(__file__).parent.parent))
try:
    from func.similarity_config import SimilarityConfig
except ImportError:
    class SimilarityConfig:
        SEMANTIC_WEIGHT = 0.8
        KEYWORD_WEIGHT = 0.1
        NAME_MATCH_WEIGHT = 0.1
        STOP_WORDS = set()
        MIN_WORD_LENGTH = 2


# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)


class VectorCache:
    """Manages persistent storage of tool vectors to avoid recomputation."""

    def __init__(self, cache_dir: str = ".vector_cache"):
        """Initialize vector cache with specified directory."""
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.cache_file = self.cache_dir / "tool_vectors.pkl"
        self.metadata_file = self.cache_dir / "cache_metadata.json"
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._load_cache()

    def _load_cache(self) -> None:
        """Load existing cache from disk."""
        try:
            if self.cache_file.exists():
                with open(self.cache_file, 'rb') as f:
                    self._cache = pickle.load(f)
                logging.info(f"Loaded {len(self._cache)} cached tool vectors")
            else:
                logging.info("No existing vector cache found, starting fresh")
        except Exception as e:
            logging.warning(f"Failed to load vector cache: {e}, starting fresh")
            self._cache = {}

    def _save_cache(self) -> None:
        """Save current cache to disk."""
        try:
            with open(self.cache_file, 'wb') as f:
                pickle.dump(self._cache, f)

            # Save metadata
            metadata = {
                "last_updated": asyncio.get_event_loop().time(),
                "tool_count": len(self._cache),
                "cache_version": "1.0"
            }
            with open(self.metadata_file, 'w') as f:
                json.dump(metadata, f, indent=2)

            logging.info(f"Saved {len(self._cache)} tool vectors to cache")
        except Exception as e:
            logging.error(f"Failed to save vector cache: {e}")

    def _get_tool_hash(self, tool_name: str, tool_description: str) -> str:
        """Generate a hash for tool identification."""
        content = f"{tool_name}:{tool_description}"
        return hashlib.md5(content.encode()).hexdigest()

    def get_vector(self, tool_name: str, tool_description: str) -> Optional[List[float]]:
        """Get cached vector for a tool."""
        tool_hash = self._get_tool_hash(tool_name, tool_description)
        if tool_hash in self._cache:
            logging.debug(f"Cache hit for tool: {tool_name}")
            return self._cache[tool_hash]["vector"]
        return None

    def set_vector(self, tool_name: str, tool_description: str, vector: List[float]) -> None:
        """Cache a vector for a tool."""
        tool_hash = self._get_tool_hash(tool_name, tool_description)
        self._cache[tool_hash] = {
            "tool_name": tool_name,
            "tool_description": tool_description,
            "vector": vector,
            "cached_at": asyncio.get_event_loop().time()
        }
        logging.debug(f"Cached vector for tool: {tool_name}")

    def save(self) -> None:
        """Public method to save cache."""
        self._save_cache()

    def clear(self) -> None:
        """Clear all cached vectors."""
        self._cache.clear()
        if self.cache_file.exists():
            self.cache_file.unlink()
        if self.metadata_file.exists():
            self.metadata_file.unlink()
        logging.info("Vector cache cleared")


class ToolVectorManager:
    """Manages tool vector generation and caching."""

    def __init__(self, embeddings_model, vector_cache: VectorCache):
        """Initialize with embeddings model and cache."""
        self.embeddings_model = embeddings_model
        self.vector_cache = vector_cache
        self._embedding_cache = {}

    async def get_tool_vector(self, tool_name: str, tool_description: str) -> List[float]:
        """Get vector for a tool, using cache if available."""
        # Check cache first
        cached_vector = self.vector_cache.get_vector(tool_name, tool_description)
        if cached_vector is not None:
            return cached_vector

        # Generate new vector
        logging.info(f"Generating vector for tool: {tool_name}")
        description_text = f"{tool_name}: {tool_description}"

        try:
            # Use the embeddings model to generate vector
            vector = await self.embeddings_model.aembed_query(description_text)

            # Cache the result
            self.vector_cache.set_vector(tool_name, tool_description, vector)

            return vector
        except Exception as e:
            logging.error(f"Failed to generate vector for tool {tool_name}: {e}")
            raise

    def save_cache(self) -> None:
        """Save the vector cache to disk."""
        self.vector_cache.save()


class CachedVectorStore:
    """A vector store that uses pre-computed cached vectors."""

    def __init__(self, tool_vector_manager: ToolVectorManager):
        """Initialize with tool vector manager."""
        self.tool_vector_manager = tool_vector_manager
        self.tools_data: Dict[str, Dict[str, Any]] = {}
        self.tool_vectors: Dict[str, List[float]] = {}

    async def add_tool(self, tool_id: str, tool_name: str, tool_description: str) -> None:
        """Add a tool to the vector store."""
        # Get cached or generate vector
        vector = await self.tool_vector_manager.get_tool_vector(tool_name, tool_description)

        # Store tool data and vector
        self.tools_data[tool_id] = {
            "name": tool_name,
            "description": tool_description,
            "full_description": f"{tool_name}: {tool_description}"
        }
        self.tool_vectors[tool_id] = vector

    def cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """Calculate optimized cosine similarity between two vectors."""
        import numpy as np
        vec1_np = np.array(vec1, dtype=np.float32)
        vec2_np = np.array(vec2, dtype=np.float32)

        dot_product = np.dot(vec1_np, vec2_np)
        norm1 = np.linalg.norm(vec1_np)
        norm2 = np.linalg.norm(vec2_np)

        if norm1 == 0 or norm2 == 0:
            return 0.0

        return float(dot_product / (norm1 * norm2))

    def _calculate_keyword_similarity(self, query: str, tool_name: str, tool_description: str) -> float:
        """Calculate keyword overlap similarity using Jaccard index with stop word filtering."""
        import re

        # Extract keywords (alphanumeric words, convert to lowercase)
        query_words = set(re.findall(r'\w+', query.lower()))
        tool_text = f"{tool_name} {tool_description}".lower()
        tool_words = set(re.findall(r'\w+', tool_text))

        # Filter out stop words and short words
        query_words = {w for w in query_words
                      if len(w) >= SimilarityConfig.MIN_WORD_LENGTH
                      and w not in SimilarityConfig.STOP_WORDS}
        tool_words = {w for w in tool_words
                     if len(w) >= SimilarityConfig.MIN_WORD_LENGTH
                     and w not in SimilarityConfig.STOP_WORDS}

        if not query_words or not tool_words:
            return 0.0

        # Calculate Jaccard similarity (intersection over union)
        intersection = len(query_words & tool_words)
        union = len(query_words | tool_words)

        return intersection / union if union > 0 else 0.0

    def _calculate_name_match(self, query: str, tool_name: str) -> float:
        """Calculate tool name matching score."""
        query_lower = query.lower()
        name_lower = tool_name.lower()

        # Exact substring match
        if name_lower in query_lower or query_lower in name_lower:
            return 1.0

        # Partial word match (handle underscore-separated tool names)
        name_words = set(name_lower.replace('_', ' ').split())
        query_words = set(query_lower.split())

        if name_words & query_words:
            return 0.5

        return 0.0

    def hybrid_similarity(self, query: str, tool_name: str, tool_description: str,
                         query_vector: List[float], tool_vector: List[float]) -> float:
        """Calculate hybrid similarity combining semantic and keyword matching."""

        # 1. Semantic similarity (vector-based)
        semantic_sim = self.cosine_similarity(query_vector, tool_vector)

        # 2. Keyword similarity (text-based)
        keyword_sim = self._calculate_keyword_similarity(query, tool_name, tool_description)

        # 3. Name matching bonus
        name_match = self._calculate_name_match(query, tool_name)

        # Weighted combination using configurable weights
        final_score = (
            SimilarityConfig.SEMANTIC_WEIGHT * semantic_sim +
            SimilarityConfig.KEYWORD_WEIGHT * keyword_sim +
            SimilarityConfig.NAME_MATCH_WEIGHT * name_match
        )

        return min(final_score, 1.0)  # Ensure score doesn't exceed 1.0

    async def search_similar_tools(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """Search for tools similar to the query using hybrid similarity."""
        # Generate vector for query
        query_vector = await self.tool_vector_manager.embeddings_model.aembed_query(query)

        # Calculate hybrid similarities
        similarities = []
        for tool_id, tool_vector in self.tool_vectors.items():
            tool_data = self.tools_data[tool_id]
            tool_name = tool_data["name"]
            tool_description = tool_data["description"]

            # Use hybrid similarity calculation
            similarity = self.hybrid_similarity(
                query, tool_name, tool_description, query_vector, tool_vector
            )

            similarities.append({
                "tool_id": tool_id,
                "similarity": similarity,
                "tool_data": tool_data,
                "match_details": {
                    "semantic_score": self.cosine_similarity(query_vector, tool_vector),
                    "keyword_score": self._calculate_keyword_similarity(query, tool_name, tool_description),
                    "name_match_score": self._calculate_name_match(query, tool_name)
                }
            })

        # Sort by similarity and return top_k
        similarities.sort(key=lambda x: x["similarity"], reverse=True)
        return similarities[:top_k]


class CustomToolSelector:
    """Custom tool selector that uses cached vectors for tool selection."""

    def __init__(self, cached_store: CachedVectorStore, tool_registry: Dict[str, Any]):
        """Initialize with cached store and tool registry."""
        self.cached_store = cached_store
        self.tool_registry = tool_registry

    async def select_tools(self, query: str, max_tools: int = 5) -> List[Any]:
        """Select relevant tools based on query using cached vectors."""
        # Search for similar tools
        similar_tools = await self.cached_store.search_similar_tools(query, max_tools)

        # Return the actual tool objects
        selected_tools = []
        for tool_info in similar_tools:
            tool_id = tool_info["tool_id"]
            if tool_id in self.tool_registry:
                selected_tools.append(self.tool_registry[tool_id])

        return selected_tools


class Configuration:
    """Manages configuration and environment variables for the MCP client."""

    def __init__(self) -> None:
        """Initialize configuration with environment variables."""
        self.load_env()
        self.api_key = "sk-6oJPGtsErJX67TyOTBAr5hJ3n334zaVk8n949svyJLBovDvG"

    @staticmethod
    def load_env() -> None:
        """Load environment variables from .env file."""
        load_dotenv()

    @staticmethod
    def load_config(file_path: str) -> dict[str, Any]:
        """Load server configuration from JSON file.

        Args:
            file_path: Path to the JSON configuration file.

        Returns:
            Dict containing server configuration.

        Raises:
            FileNotFoundError: If configuration file doesn't exist.
            JSONDecodeError: If configuration file is invalid JSON.
        """
        with open(file_path, "r") as f:
            return json.load(f)

    @property
    def llm_api_key(self) -> str:
        """Get the LLM API key.

        Returns:
            The API key as a string.

        Raises:
            ValueError: If the API key is not found in environment variables.
        """
        if not self.api_key:
            raise ValueError("LLM_API_KEY not found in environment variables")
        return self.api_key


class Server:
    """Manages MCP server connections and tool execution."""

    def __init__(self, name: str, config: dict[str, Any]) -> None:
        self.name: str = name
        self.config: dict[str, Any] = config
        self.stdio_context: Any | None = None
        self.session: ClientSession | None = None
        self._cleanup_lock: asyncio.Lock = asyncio.Lock()
        self.exit_stack: AsyncExitStack = AsyncExitStack()

    async def initialize(self) -> None:
        """Initialize the server connection."""
        command = (
            shutil.which("npx")
            if self.config["command"] == "npx"
            else self.config["command"]
        )
        if command is None:
            raise ValueError("The command must be a valid string and cannot be None.")

        server_params = StdioServerParameters(
            command=command,
            args=self.config["args"],
            env={**os.environ, **self.config["env"]}
            if self.config.get("env")
            else None,
        )
        try:
            stdio_transport = await self.exit_stack.enter_async_context(
                stdio_client(server_params)
            )
            read, write = stdio_transport
            session = await self.exit_stack.enter_async_context(
                ClientSession(read, write)
            )
            await session.initialize()
            self.session = session
        except Exception as e:
            logging.error(f"Error initializing server {self.name}: {e}")
            await self.cleanup()
            raise

    async def list_tools(self) -> list[Any]:
        """List available tools from the server.

        Returns:
            A list of available tools.

        Raises:
            RuntimeError: If the server is not initialized.
        """
        if not self.session:
            raise RuntimeError(f"Server {self.name} not initialized")

        tools_response = await self.session.list_tools()
        tools = []

        for item in tools_response:
            if isinstance(item, tuple) and item[0] == "tools":
                tools.extend(
                    Tool(tool.name, tool.description, tool.inputSchema)
                    for tool in item[1]
                )

        return tools

    async def execute_tool(
        self,
        tool_name: str,
        arguments: dict[str, Any],
        retries: int = 2,
        delay: float = 1.0,
    ) -> Any:
        """Execute a tool with retry mechanism.

        Args:
            tool_name: Name of the tool to execute.
            arguments: Tool arguments.
            retries: Number of retry attempts.
            delay: Delay between retries in seconds.

        Returns:
            Tool execution result.

        Raises:
            RuntimeError: If server is not initialized.
            Exception: If tool execution fails after all retries.
        """
        if not self.session:
            raise RuntimeError(f"Server {self.name} not initialized")

        attempt = 0
        while attempt < retries:
            try:
                logging.info(f"Executing {tool_name}...")
                result = await self.session.call_tool(tool_name, arguments)

                return result

            except Exception as e:
                attempt += 1
                logging.warning(
                    f"Error executing tool: {e}. Attempt {attempt} of {retries}."
                )
                if attempt < retries:
                    logging.info(f"Retrying in {delay} seconds...")
                    await asyncio.sleep(delay)
                else:
                    logging.error("Max retries reached. Failing.")
                    raise

    async def cleanup(self) -> None:
        """Clean up server resources."""
        async with self._cleanup_lock:
            try:
                await self.exit_stack.aclose()
                self.session = None
                self.stdio_context = None
            except Exception as e:
                logging.error(f"Error during cleanup of server {self.name}: {e}")


class Tool:
    """Represents a tool with its properties and formatting."""

    def __init__(
        self, name: str, description: str, input_schema: dict[str, Any]
    ) -> None:
        self.name: str = name
        self.description: str = description
        self.input_schema: dict[str, Any] = input_schema

    def format_for_llm(self) -> str:
        """Format tool information for LLM.

        Returns:
            A formatted string describing the tool.
        """
        args_desc = []
        if "properties" in self.input_schema:
            for param_name, param_info in self.input_schema["properties"].items():
                arg_desc = (
                    f"- {param_name}: {param_info.get('description', 'No description')}"
                )
                if param_name in self.input_schema.get("required", []):
                    arg_desc += " (required)"
                args_desc.append(arg_desc)

        return f"""
Tool: {self.name}
Description: {self.description}
Arguments:
{chr(10).join(args_desc)}
"""


class LLMClient:
    """Manages communication with the LLM provider."""

    def __init__(self, api_key: str) -> None:
        self.api_key: str = api_key

    def get_response(self, messages: list[dict[str, str]]) -> str:
        """Get a response from the LLM.

        Args:
            messages: A list of message dictionaries.

        Returns:
            The LLM's response as a string.

        Raises:
            httpx.RequestError: If the request to the LLM fails.
        """
        url = "https://yunwu.ai/v1/chat/completions"

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}",
        }
        payload = {
            "messages": messages,
            "model": "gpt-4.1-nano-2025-04-14",
            "temperature": 0.7,
            "max_tokens": 4096,
            "top_p": 1,
            "stream": False,
            "stop": None,
        }

        try:
            with httpx.Client() as client:
                response = client.post(url, headers=headers, json=payload)
                response.raise_for_status()
                data = response.json()
                return data["choices"][0]["message"]["content"]

        except httpx.RequestError as e:
            error_message = f"Error getting LLM response: {str(e)}"
            logging.error(error_message)

            if isinstance(e, httpx.HTTPStatusError):
                status_code = e.response.status_code
                logging.error(f"Status code: {status_code}")
                logging.error(f"Response details: {e.response.text}")

            return (
                f"I encountered an error: {error_message}. "
                "Please try again or rephrase your request."
            )


class ChatSession:
    """Orchestrates the interaction between user, LLM, and tools."""

    def __init__(self, servers: list[Server], llm_client: LLMClient) -> None:
        self.servers: list[Server] = servers
        self.llm_client: LLMClient = llm_client
        self.vector_cache = VectorCache()
        self.tool_vector_manager: Optional[ToolVectorManager] = None

        # Initialize Aliyun enhanced retrieval if available
        if ALIYUN_ENHANCED_AVAILABLE:
            self.aliyun_enhanced_retrieval = None  # Will be initialized after tool loading
            self.aliyun_mode = True
            logging.info("🚀 Aliyun Enhanced Retrieval System available!")
        else:
            self.aliyun_mode = False
            logging.info("⚠️ Aliyun Enhanced Retrieval not available")

        # 简化模式，不使用增强功能
        self.enhanced_mode = False
        logging.info("📝 Running in standard mode")

        # Initialize enhanced tool selector if available
        if ENHANCED_TOOL_SELECTOR_AVAILABLE:
            self.enhanced_tool_selector = None  # Will be initialized after tool loading
            self.enhanced_tool_mode = True
            logging.info("🎯 Enhanced Tool Selector available!")
        else:
            self.enhanced_tool_mode = False
            logging.info("📝 Using standard tool selection")

        # Initialize optimized retrieval system if available
        if OPTIMIZED_RETRIEVAL_AVAILABLE:
            self.optimized_retrieval_system = None  # Will be initialized after tool loading
            self.optimized_retrieval_mode = True
            logging.info("🎯 Optimized Retrieval System available!")
        else:
            self.optimized_retrieval_mode = False
            logging.info("📝 Using standard retrieval")

    def _handle_weights_command(self) -> None:
        """Handle the weights adjustment command."""
        print("\n⚖️  Similarity Weight Configuration")
        print("=" * 40)

        # Show current weights
        weights = SimilarityConfig.get_weights()
        print(f"Current weights:")
        print(f"  Semantic:  {weights['semantic']:.2f}")
        print(f"  Keywords:  {weights['keyword']:.2f}")
        print(f"  Name:      {weights['name_match']:.2f}")

        # Show available presets
        print(f"\nAvailable presets:")
        presets = SimilarityConfig.get_presets()
        for name, preset in presets.items():
            print(f"  {name}: {preset['description']}")

        print(f"\nOptions:")
        print(f"  1. Type preset name (e.g., 'semantic_focused')")
        print(f"  2. Type 'custom' to set custom weights")
        print(f"  3. Press Enter to keep current weights")

        choice = input("\nYour choice: ").strip().lower()

        if not choice:
            print("Keeping current weights.")
            return

        if choice == "custom":
            try:
                print("\nEnter weights (must sum to 1.0):")
                semantic = float(input("Semantic weight (0.0-1.0): "))
                keyword = float(input("Keyword weight (0.0-1.0): "))
                name_match = float(input("Name match weight (0.0-1.0): "))

                SimilarityConfig.set_weights(semantic, keyword, name_match)
                print(f"✅ Custom weights applied successfully!")

            except (ValueError, Exception) as e:
                print(f"❌ Error setting custom weights: {e}")

        elif choice in presets:
            description = SimilarityConfig.apply_preset(choice)
            print(f"✅ Applied preset '{choice}': {description}")

        else:
            print(f"❌ Unknown option: {choice}")

        # Show updated weights
        weights = SimilarityConfig.get_weights()
        print(f"\nUpdated weights:")
        print(f"  Semantic:  {weights['semantic']:.2f}")
        print(f"  Keywords:  {weights['keyword']:.2f}")
        print(f"  Name:      {weights['name_match']:.2f}")

# 删除增强功能命令处理函数

    def _handle_performance_command(self, optimized_selector) -> None:
        """Handle the performance command for tool selection optimization."""
        print("\n📊 Tool Selection Performance Report")
        print("=" * 50)

        try:
            perf_report = optimized_selector.get_performance_report()

            print(f"\n🎯 Current Strategy: {perf_report['strategy']}")
            print(f"💡 Recommendation: {perf_report['recommendation']}")

            stats = perf_report['performance_stats']
            print(f"\n📈 Performance Statistics:")
            print(f"  Hybrid Method:")
            print(f"    Calls: {stats['hybrid_calls']}")
            print(f"    Avg Time: {stats['hybrid_avg_time']:.3f}s")
            print(f"    Success Rate: {stats['hybrid_success_rate']:.1%}")
            print(f"    Score: {perf_report['hybrid_score']:.3f}")

            print(f"\n  Agent Method:")
            print(f"    Calls: {stats['agent_calls']}")
            print(f"    Avg Time: {stats['agent_avg_time']:.3f}s")
            print(f"    Success Rate: {stats['agent_success_rate']:.1%}")
            print(f"    Score: {perf_report['agent_score']:.3f}")

            # Strategy switching options
            print(f"\n⚙️ Strategy Options:")
            print(f"  1. hybrid_only - Use only hybrid similarity")
            print(f"  2. agent_only - Use only multi-agent system")
            print(f"  3. adaptive - Automatically choose best method")
            print(f"  4. ensemble - Combine both methods")

            choice = input("\nSwitch strategy? (1-4 or Enter to keep current): ").strip()

            if choice == "1":
                optimized_selector.strategy = SelectionStrategy.HYBRID_ONLY
                print("✅ Switched to hybrid-only strategy")
            elif choice == "2":
                optimized_selector.strategy = SelectionStrategy.AGENT_ONLY
                print("✅ Switched to agent-only strategy")
            elif choice == "3":
                optimized_selector.strategy = SelectionStrategy.ADAPTIVE
                print("✅ Switched to adaptive strategy")
            elif choice == "4":
                optimized_selector.strategy = SelectionStrategy.ENSEMBLE
                print("✅ Switched to ensemble strategy")
            elif choice:
                print("❌ Invalid choice")

        except Exception as e:
            print(f"❌ Error getting performance report: {e}")

    def _handle_config_command(self) -> None:
        """Handle the config command for optimization settings."""
        print("\n⚙️ Configuration Management")
        print("=" * 40)

        print("Options:")
        print("  1. View optimization configuration")
        print("  2. Interactive optimization configuration")
        print("  3. Reset optimization to defaults")
        if self.aliyun_mode:
            print("  4. View Aliyun enhanced retrieval configuration")
            print("  5. Interactive Aliyun configuration")
            print("  6. Reset Aliyun configuration to defaults")

        choice = input(f"\nYour choice (1-{'6' if self.aliyun_mode else '3'}): ").strip()

        if choice == "1":
            optimization_config.print_current_config()
        elif choice == "2":
            optimization_config.interactive_config()
        elif choice == "3":
            confirm = input("Reset optimization configuration to defaults? (y/N): ").strip().lower()
            if confirm == 'y':
                optimization_config.config = optimization_config.DEFAULT_CONFIG.copy()
                optimization_config.save_config()
                print("✅ Optimization configuration reset to defaults")
        elif choice == "4" and self.aliyun_mode:
            aliyun_config.print_current_config()
        elif choice == "5" and self.aliyun_mode:
            self._handle_aliyun_interactive_config()
        elif choice == "6" and self.aliyun_mode:
            confirm = input("Reset Aliyun configuration to defaults? (y/N): ").strip().lower()
            if confirm == 'y':
                aliyun_config.config = aliyun_config.DEFAULT_CONFIG.copy()
                aliyun_config.save_config()
                print("✅ Aliyun configuration reset to defaults")
        else:
            print("❌ Invalid choice")

    def _handle_aliyun_interactive_config(self) -> None:
        """Handle interactive Aliyun configuration."""
        print("\n🔧 阿里云增强检索交互式配置")
        print("=" * 50)

        print("配置选项:")
        print("  1. 调整权重配置")
        print("  2. 应用预设权重")
        print("  3. 功能开关设置")
        print("  4. 性能参数调整")

        choice = input("\n选择配置项 (1-4): ").strip()

        if choice == "1":
            self._configure_aliyun_weights()
        elif choice == "2":
            self._configure_aliyun_presets()
        elif choice == "3":
            self._configure_aliyun_features()
        elif choice == "4":
            self._configure_aliyun_performance()
        else:
            print("❌ 无效选择")

    def _configure_aliyun_weights(self) -> None:
        """配置阿里云权重"""
        print("\n⚖️ 权重配置")
        print("当前权重:")
        weights = aliyun_config.get_weights()
        print(f"  意图匹配权重:    {weights['intent_weight']:.2f}")
        print(f"  重排序权重:      {weights['rerank_weight']:.2f}")
        print(f"  原始相似度权重:  {weights['original_weight']:.2f}")

        try:
            print("\n输入新权重 (总和必须为1.0):")
            intent = float(input("意图匹配权重 (0.0-1.0): "))
            rerank = float(input("重排序权重 (0.0-1.0): "))
            original = float(input("原始相似度权重 (0.0-1.0): "))

            aliyun_config.set_weights(intent, rerank, original)
            aliyun_config.save_config()
            print("✅ 权重配置已更新并保存")

            # 更新运行中的系统
            if hasattr(self, 'aliyun_enhanced_retrieval') and self.aliyun_enhanced_retrieval:
                self.aliyun_enhanced_retrieval.update_weights(intent, rerank, original)

        except (ValueError, Exception) as e:
            print(f"❌ 权重配置失败: {e}")

    def _configure_aliyun_presets(self) -> None:
        """配置阿里云预设"""
        print("\n🎯 预设权重配置")
        presets = aliyun_config.get_presets()

        print("可用预设:")
        for i, (name, preset) in enumerate(presets.items(), 1):
            print(f"  {i}. {name}: {preset['description']}")
            print(f"     权重: 重排序={preset['rerank_weight']:.1f}, 原始={preset['original_weight']:.1f}")

        try:
            choice = input(f"\n选择预设 (1-{len(presets)}): ").strip()
            preset_names = list(presets.keys())

            if choice.isdigit() and 1 <= int(choice) <= len(presets):
                preset_name = preset_names[int(choice) - 1]
                description = aliyun_config.apply_preset(preset_name)
                aliyun_config.save_config()
                print(f"✅ 已应用预设 '{preset_name}': {description}")

                # 更新运行中的系统
                if hasattr(self, 'aliyun_enhanced_retrieval') and self.aliyun_enhanced_retrieval:
                    weights = aliyun_config.get_weights()
                    self.aliyun_enhanced_retrieval.update_weights(
                        weights['intent_weight'],
                        weights['rerank_weight'],
                        weights['original_weight']
                    )
            else:
                print("❌ 无效选择")
        except Exception as e:
            print(f"❌ 预设应用失败: {e}")

    def _configure_aliyun_features(self) -> None:
        """配置阿里云功能开关"""
        print("\n🔧 功能开关配置")
        print(f"当前状态:")
        # print(f"  意图检测:        {'启用' if aliyun_config.is_intent_enabled() else '禁用'}")
        print(f"  文本重排序:      {'启用' if aliyun_config.is_rerank_enabled() else '禁用'}")

        print("\n选项:")
        print("  1. 切换意图检测")
        print("  2. 切换文本重排序")

        choice = input("\n选择操作 (1-2): ").strip()

        if choice == "1":
            current = aliyun_config.is_intent_enabled()
            aliyun_config.set("intent_detection.enabled", not current)
            status = "启用" if not current else "禁用"
            print(f"✅ 意图检测已{status}")
        elif choice == "2":
            current = aliyun_config.is_rerank_enabled()
            aliyun_config.set("text_rerank.enabled", not current)
            status = "启用" if not current else "禁用"
            print(f"✅ 文本重排序已{status}")
        else:
            print("❌ 无效选择")
            return

        aliyun_config.save_config()

    def _configure_aliyun_performance(self) -> None:
        """配置阿里云性能参数"""
        print("\n⚡ 性能参数配置")
        print("当前设置:")
        print(f"  请求超时:        {aliyun_config.get_timeout():.1f}s")
        print(f"  重试次数:        {aliyun_config.get_retry_count()}")
        print(f"  最大工具数:      {aliyun_config.get_max_tools_for_intent()}")
        print(f"  最大文档数:      {aliyun_config.get_max_documents_for_rerank()}")

        print("\n选项:")
        print("  1. 调整请求超时")
        print("  2. 调整重试次数")
        print("  3. 调整最大工具数")
        print("  4. 调整最大文档数")

        choice = input("\n选择参数 (1-4): ").strip()

        try:
            if choice == "1":
                timeout = float(input("新的请求超时时间 (秒): "))
                aliyun_config.set("performance.timeout", timeout)
                print(f"✅ 请求超时已设置为 {timeout}s")
            elif choice == "2":
                retry = int(input("新的重试次数: "))
                aliyun_config.set("performance.retry_count", retry)
                print(f"✅ 重试次数已设置为 {retry}")
            elif choice == "3":
                max_tools = int(input("新的最大工具数: "))
                aliyun_config.set("intent_detection.max_tools", max_tools)
                print(f"✅ 最大工具数已设置为 {max_tools}")
            elif choice == "4":
                max_docs = int(input("新的最大文档数: "))
                aliyun_config.set("text_rerank.max_documents", max_docs)
                print(f"✅ 最大文档数已设置为 {max_docs}")
            else:
                print("❌ 无效选择")
                return

            aliyun_config.save_config()
        except (ValueError, Exception) as e:
            print(f"❌ 参数设置失败: {e}")

    def _handle_aliyun_command(self) -> None:
        """Handle the aliyun command for enhanced retrieval management."""
        print("\n🚀 阿里云增强检索管理")
        print("=" * 40)

        print("选项:")
        print("  1. 查看当前状态")
        print("  2. 测试检索功能")
        print("  3. 配置管理")
        print("  4. 性能统计")

        choice = input("\n选择操作 (1-4): ").strip()

        if choice == "1":
            self._show_aliyun_status()
        elif choice == "2":
            asyncio.create_task(self._test_aliyun_retrieval())
        elif choice == "3":
            self._handle_aliyun_interactive_config()
        elif choice == "4":
            self._show_aliyun_performance()
        else:
            print("❌ 无效选择")

    def _show_aliyun_status(self) -> None:
        """显示阿里云增强检索状态"""
        print("\n📊 阿里云增强检索状态")
        print("=" * 30)

        if hasattr(self, 'aliyun_enhanced_retrieval') and self.aliyun_enhanced_retrieval:
            print("✅ 系统状态: 已初始化")

            # 显示配置信息
            weights = aliyun_config.get_weights()
            print(f"\n⚖️ 权重配置:")
            print(f"  意图匹配权重:    {weights['intent_weight']:.2f}")
            print(f"  重排序权重:      {weights['rerank_weight']:.2f}")
            print(f"  原始相似度权重:  {weights['original_weight']:.2f}")

            print(f"\n🔧 功能状态:")
            # print(f"  意图检测:        {'启用' if aliyun_config.is_intent_enabled() else '禁用'}")
            print(f"  文本重排序:      {'启用' if aliyun_config.is_rerank_enabled() else '禁用'}")

            print(f"\n⚡ 性能参数:")
            print(f"  请求超时:        {aliyun_config.get_timeout():.1f}s")
            print(f"  重试次数:        {aliyun_config.get_retry_count()}")
            print(f"  最大工具数:      {aliyun_config.get_max_tools_for_intent()}")
            print(f"  最大文档数:      {aliyun_config.get_max_documents_for_rerank()}")
        else:
            print("❌ 系统状态: 未初始化或初始化失败")

    async def _test_aliyun_retrieval(self) -> None:
        """测试阿里云增强检索功能"""
        print("\n🧪 测试阿里云增强检索")
        print("=" * 30)

        if not (hasattr(self, 'aliyun_enhanced_retrieval') and self.aliyun_enhanced_retrieval):
            print("❌ 阿里云增强检索系统未初始化")
            return

        test_query = input("输入测试查询: ").strip()
        if not test_query:
            print("❌ 查询不能为空")
            return

        try:
            print(f"\n🔍 正在测试查询: '{test_query}'")
            start_time = asyncio.get_event_loop().time()

            results = await self.aliyun_enhanced_retrieval.enhanced_search(
                query=test_query,
                top_k=5,
                use_intent=aliyun_config.is_intent_enabled(),
                use_rerank=aliyun_config.is_rerank_enabled()
            )

            end_time = asyncio.get_event_loop().time()
            processing_time = end_time - start_time

            print(f"\n✅ 测试完成 (耗时: {processing_time:.2f}s)")
            print(f"📊 返回结果数量: {len(results)}")

            if results:
                print(f"\n🎯 前3个结果:")
                for i, result in enumerate(results[:3], 1):
                    print(f"\n{i}. {result.tool_name}")
                    print(f"   描述: {result.description}")
                    print(f"   最终分数: {result.final_score:.3f}")
                    print(f"   原始相似度: {result.original_similarity:.3f}")
                    print(f"   重排序分数: {result.rerank_score:.3f}")
                    print(f"   意图匹配: {'✅' if result.intent_match else '❌'}")
                    print(f"   推理: {result.reasoning}")
            else:
                print("❌ 未找到相关结果")

        except Exception as e:
            print(f"❌ 测试失败: {e}")

    def _show_aliyun_performance(self) -> None:
        """显示阿里云增强检索性能统计"""
        print("\n📈 阿里云增强检索性能统计")
        print("=" * 40)
        print("💡 性能统计功能正在开发中...")
        print("   将来会显示:")
        print("   - 平均响应时间")
        print("   - 成功率统计")
        print("   - 意图检测准确率")
        print("   - 重排序效果评估")

    def _handle_multi_tool_command(self) -> None:
        """处理多工具查询命令"""
        if not self.enhanced_tool_mode:
            print("多工具查询功能不可用。")
            return

        print("\n🔧 多工具查询管理")
        print("=" * 40)

        print("选项:")
        print("  1. 测试多工具查询")
        print("  2. 查看统计信息")
        print("  3. 分析查询特征")
        print("  4. 切换多工具模式")

        choice = input("\n选择操作 (1-4): ").strip()

        if choice == "1":
            asyncio.create_task(self._test_multi_tool_query())
        elif choice == "2":
            self._show_multi_tool_stats()
        elif choice == "3":
            asyncio.create_task(self._analyze_query_features())
        elif choice == "4":
            self._toggle_multi_tool_mode()
        else:
            print("❌ 无效选择")

    async def _test_multi_tool_query(self) -> None:
        """测试多工具查询"""
        print("\n🧪 测试多工具查询")
        print("=" * 30)

        if not (hasattr(self, 'enhanced_tool_selector') and self.enhanced_tool_selector):
            print("❌ 增强工具选择器未初始化")
            return

        test_query = input("输入测试查询: ").strip()
        if not test_query:
            print("❌ 查询不能为空")
            return

        try:
            print(f"\n🔍 正在测试查询: '{test_query}'")
            start_time = asyncio.get_event_loop().time()

            results = await self.enhanced_tool_selector.select_tools(
                query=test_query,
                max_tools=10,
                user_id=self.session_id if self.enhanced_mode else None,
                force_multi_tool=True
            )

            end_time = asyncio.get_event_loop().time()
            processing_time = end_time - start_time

            print(f"\n✅ 测试完成 (耗时: {processing_time:.2f}s)")
            print(f"📊 返回结果数量: {len(results)}")

            if results:
                print(f"\n🎯 检索到的工具:")
                for i, result in enumerate(results, 1):
                    print(f"\n{i}. {result.tool_name}")
                    print(f"   描述: {result.description}")
                    print(f"   置信度: {result.confidence:.3f}")
                    print(f"   选择方法: {result.selection_method}")
                    print(f"   推理: {result.reasoning}")
                    if result.subtask_info:
                        print(f"   子任务: {result.subtask_info['description']}")
                        print(f"   动作类型: {result.subtask_info['action_type']}")
            else:
                print("❌ 未找到相关工具")

        except Exception as e:
            print(f"❌ 测试失败: {e}")

    def _show_multi_tool_stats(self) -> None:
        """显示多工具统计信息"""
        print("\n📊 多工具查询统计")
        print("=" * 30)

        if hasattr(self, 'enhanced_tool_selector') and self.enhanced_tool_selector:
            stats = self.enhanced_tool_selector.get_statistics()

            print(f"📈 性能统计:")
            perf_stats = stats["performance_stats"]
            print(f"  总查询数: {perf_stats['total_queries']}")
            print(f"  多工具查询: {perf_stats['multi_tool_queries']}")
            print(f"  单工具查询: {perf_stats['single_tool_queries']}")
            print(f"  平均处理时间: {perf_stats['avg_processing_time']:.3f}s")
            print(f"  平均返回工具数: {perf_stats['avg_tools_returned']:.1f}")

            print(f"\n📊 使用比例:")
            print(f"  多工具模式: {stats['multi_tool_ratio']:.1%}")
            print(f"  单工具模式: {stats['single_tool_ratio']:.1%}")
            print(f"  多工具功能: {'启用' if stats['multi_tool_enabled'] else '禁用'}")
        else:
            print("❌ 增强工具选择器未初始化")

    async def _analyze_query_features(self) -> None:
        """分析查询特征"""
        print("\n🔍 查询特征分析")
        print("=" * 30)

        if not (hasattr(self, 'enhanced_tool_selector') and self.enhanced_tool_selector):
            print("❌ 增强工具选择器未初始化")
            return

        test_query = input("输入要分析的查询: ").strip()
        if not test_query:
            print("❌ 查询不能为空")
            return

        try:
            analysis = await self.enhanced_tool_selector.analyze_query(test_query)

            print(f"\n📋 分析结果:")
            print(f"  查询长度: {analysis['length']} 字符")
            print(f"  建议使用多工具: {'是' if analysis['should_use_multi_tool'] else '否'}")

            if analysis['indicators']:
                print(f"\n🔍 检测到的指示词:")
                for indicator in analysis['indicators']:
                    print(f"  {indicator['category']}: {', '.join(indicator['found'])}")

            if 'decomposition' in analysis:
                decomp = analysis['decomposition']
                print(f"\n🧩 查询分解:")
                print(f"  复杂度: {decomp['complexity']}")
                print(f"  子任务数: {decomp['subtask_count']}")

                if decomp['subtasks']:
                    print(f"\n📝 子任务详情:")
                    for task in decomp['subtasks']:
                        print(f"  {task['id']}: {task['description']}")
                        print(f"    动作类型: {task['action_type']}")
                        print(f"    关键词: {', '.join(task['keywords'])}")
                        if task['entities']:
                            print(f"    实体: {', '.join(task['entities'])}")

            if 'decomposition_error' in analysis:
                print(f"\n❌ 分解错误: {analysis['decomposition_error']}")

        except Exception as e:
            print(f"❌ 分析失败: {e}")

    def _toggle_multi_tool_mode(self) -> None:
        """切换多工具模式"""
        if not (hasattr(self, 'enhanced_tool_selector') and self.enhanced_tool_selector):
            print("❌ 增强工具选择器未初始化")
            return

        current_status = self.enhanced_tool_selector.enable_multi_tool_processing

        if current_status:
            self.enhanced_tool_selector.disable_multi_tool_mode()
            print("✅ 多工具模式已禁用")
        else:
            self.enhanced_tool_selector.enable_multi_tool_mode()
            print("✅ 多工具模式已启用")

    async def cleanup_servers(self) -> None:
        """Clean up all servers properly."""
        for server in reversed(self.servers):
            try:
                await server.cleanup()
            except Exception as e:
                logging.warning(f"Warning during final cleanup: {e}")

    async def process_llm_response(self, llm_response: str) -> str:
        """Process the LLM response and execute tools if needed.

        Args:
            llm_response: The response from the LLM.

        Returns:
            The result of tool execution or the original response.
        """
        import json

        try:
            tool_call = json.loads(llm_response)
            if "tool" in tool_call and "arguments" in tool_call:
                logging.info(f"Executing tool: {tool_call['tool']}")
                logging.info(f"With arguments: {tool_call['arguments']}")

                for server in self.servers:
                    tools = await server.list_tools()
                    if any(tool.name == tool_call["tool"] for tool in tools):
                        try:
                            result = await server.execute_tool(
                                tool_call["tool"], tool_call["arguments"]
                            )

                            if isinstance(result, dict) and "progress" in result:
                                progress = result["progress"]
                                total = result["total"]
                                percentage = (progress / total) * 100
                                logging.info(
                                    f"Progress: {progress}/{total} ({percentage:.1f}%)"
                                )

                            return f"Tool execution result: {result}"
                        except Exception as e:
                            error_msg = f"Error executing tool: {str(e)}"
                            logging.error(error_msg)
                            return error_msg

                return f"No server found with tool: {tool_call['tool']}"
            return llm_response
        except json.JSONDecodeError:
            return llm_response

    async def start(self) -> None:
        """Main chat session handler."""
        try:
            for server in self.servers:
                try:
                    await server.initialize()
                except Exception as e:
                    logging.error(f"Failed to initialize server: {e}")
                    await self.cleanup_servers()
                    return

            all_tools = []
            for server in self.servers:
                tools = await server.list_tools()
                all_tools.extend(tools)

            # Convert custom Tool objects to LangChain compatible tools
            from langchain_core.tools import StructuredTool

            def create_langchain_tool(custom_tool, servers):
                """Convert custom Tool to LangChain StructuredTool."""
                async def tool_func(**kwargs):
                    # Find the server that has this tool and execute it
                    for server in servers:
                        server_tools = await server.list_tools()
                        if any(t.name == custom_tool.name for t in server_tools):
                            return await server.execute_tool(custom_tool.name, kwargs)
                    raise ValueError(f"Tool {custom_tool.name} not found on any server")

                return StructuredTool.from_function(
                    coroutine=tool_func,  # Use coroutine parameter for async functions
                    name=custom_tool.name,
                    description=custom_tool.description,
                    args_schema=None  # You might want to create a proper schema here
                )

            # Initialize embeddings and vector manager
            embeddings = init_embeddings("openai:text-embedding-3-large")
            self.tool_vector_manager = ToolVectorManager(embeddings, self.vector_cache)

            # Create tool registry with LangChain compatible tools
            tool_registry = {}
            for tool in all_tools:
                tool_id = str(uuid.uuid4())
                langchain_tool = create_langchain_tool(tool, self.servers)
                tool_registry[tool_id] = langchain_tool

            # Create our custom cached vector store instead of InMemoryStore
            cached_store = CachedVectorStore(self.tool_vector_manager)

            # Populate cached store with tools (this will use cache when available)
            logging.info("Loading tool vectors (using cache when available)...")
            for tool_id, tool in tool_registry.items():

                print("tool_name:{},tool_description:{}".format(tool.name,tool.description))

                try:
                    await cached_store.add_tool(tool_id, tool.name, tool.description)
                except Exception as e:
                    logging.error(f"Failed to process tool {tool.name}: {e}")
                    continue

            # Save cache after processing all tools
            self.tool_vector_manager.save_cache()
            logging.info("Tool vector processing completed")

            # Initialize Aliyun enhanced retrieval if available
            if self.aliyun_mode:
                try:
                    self.aliyun_enhanced_retrieval = AliyunEnhancedRetrieval(
                        cached_store=cached_store,
                        api_key="sk-fcac337c29fe4d6f93bb9ff2ca2395d8",
                        intent_weight=aliyun_config.get("weights.intent_weight", 0.3),
                        rerank_weight=aliyun_config.get("weights.rerank_weight", 0.4),
                        original_weight=aliyun_config.get("weights.original_weight", 0.3)
                    )
                    logging.info("🚀 Aliyun Enhanced Retrieval System initialized")
                except Exception as e:
                    logging.error(f"Failed to initialize Aliyun Enhanced Retrieval: {e}")
                    self.aliyun_mode = False

# 删除多Agent系统初始化

            # Initialize optimized retrieval system if available
            if self.optimized_retrieval_mode:
                try:
                    config = SystemConfiguration(
                        enable_caching=True,
                        enable_parallel_processing=True,
                        enable_error_recovery=True,
                        enable_quality_evaluation=True,
                        enable_performance_monitoring=True,
                        max_concurrent_requests=optimization_config.get("max_concurrent_selections", 3),
                        cache_size=1000,
                        cache_ttl=3600,
                        default_timeout=optimization_config.get("ensemble_timeout", 15.0),
                        quality_threshold=optimization_config.get("min_confidence_threshold", 0.1)
                    )

                    self.optimized_retrieval_system = OptimizedRetrievalSystem(
                        cached_store=cached_store,
                        multi_agent_system=self.multi_agent_system if self.enhanced_mode else None,
                        aliyun_enhanced_retrieval=self.aliyun_enhanced_retrieval if self.aliyun_mode else None,
                        config=config
                    )
                    logging.info("🎯 Optimized Retrieval System initialized")
                except Exception as e:
                    logging.error(f"Failed to initialize Optimized Retrieval System: {e}")
                    self.optimized_retrieval_mode = False

            # Create optimized tool selector with adaptive strategy (保持向后兼容)
            optimized_selector = OptimizedToolSelector(
                cached_store=cached_store,
                multi_agent_system=None,  # 不使用多Agent系统
                strategy=SelectionStrategy.HYBRID_ONLY  # 使用混合相似度策略
            )

            # Create custom tool selector (保持向后兼容)
            tool_selector = CustomToolSelector(cached_store, tool_registry)

            # Initialize enhanced tool selector if available
            if self.enhanced_tool_mode:
                try:
                    self.enhanced_tool_selector = await create_enhanced_tool_selector(
                        cached_store=cached_store,
                        tool_registry=tool_registry,
                        multi_agent_system=None,  # 不使用多Agent系统
                        enable_multi_tool=True
                    )
                    logging.info("🎯 Enhanced Tool Selector initialized")
                except Exception as e:
                    logging.error(f"Failed to initialize Enhanced Tool Selector: {e}")
                    self.enhanced_tool_mode = False

            # Create LLM
            llm = init_chat_model("openai:gpt-4o-mini")

            # Create a simple agent that uses our custom tool selection

            async def tool_selection_node(state: MessagesState):
                """Node that selects and calls relevant tools based on user query."""
                messages = state["messages"]
                last_message = messages[-1]

                if isinstance(last_message, HumanMessage):
                    query = last_message.content

                    # 最优先使用增强工具选择器
                    if self.enhanced_tool_mode and hasattr(self, 'enhanced_tool_selector') and self.enhanced_tool_selector:
                        try:
                            enhanced_results = await self.enhanced_tool_selector.select_tools(
                                query=query,
                                max_tools=10,
                                user_id=None  # 不使用用户ID
                            )

                            # Convert to similar_tools format for compatibility
                            similar_tools = []
                            for result in enhanced_results:
                                similar_tools.append({
                                    "tool_id": result.tool_id,
                                    "similarity": result.confidence,
                                    "tool_data": {
                                        "name": result.tool_name,
                                        "description": result.description
                                    },
                                    "match_details": result.match_details,
                                    "reasoning": result.reasoning,
                                    "selection_method": result.selection_method,
                                    "execution_time": result.execution_time,
                                    "subtask_info": result.subtask_info
                                })

                            logging.info(f"🎯 Enhanced Tool Selector returned {len(similar_tools)} results")

                            # 如果是多工具查询，显示详细信息
                            if any(result.selection_method == "multi_tool_processing" for result in enhanced_results):
                                logging.info("🔧 Multi-tool query detected and processed")
                                for result in enhanced_results:
                                    if result.subtask_info:
                                        logging.info(f"  - {result.tool_name} for subtask: {result.subtask_info['description']}")

                        except Exception as e:
                            logging.error(f"Enhanced Tool Selector error, falling back: {e}")
                            similar_tools = None

                    # 次优先使用新的优化检索系统
                    elif self.optimized_retrieval_mode and hasattr(self, 'optimized_retrieval_system') and self.optimized_retrieval_system and not hasattr(self, 'similar_tools'):
                        try:
                            retrieval_results = await self.optimized_retrieval_system.retrieve(
                                query=query,
                                user_id="default",
                                session_id="default",
                                top_k=10
                            )

                            # Convert to similar_tools format for compatibility
                            similar_tools = []
                            for result in retrieval_results:
                                similar_tools.append({
                                    "tool_id": result.tool_id,
                                    "similarity": result.confidence,
                                    "tool_data": {
                                        "name": result.tool_name,
                                        "description": result.description
                                    },
                                    "match_details": result.metadata,
                                    "reasoning": result.reasoning,
                                    "selection_method": result.strategy_used,
                                    "execution_time": result.execution_time
                                })

                            logging.info(f"🎯 Optimized Retrieval System returned {len(similar_tools)} results")

                        except Exception as e:
                            logging.error(f"Optimized Retrieval System error, falling back: {e}")
                            similar_tools = None

                    # 回退到阿里云增强检索
                    elif self.aliyun_mode and hasattr(self, 'aliyun_enhanced_retrieval') and self.aliyun_enhanced_retrieval and not hasattr(self, 'similar_tools'):
                        try:
                            enhanced_results = await self.aliyun_enhanced_retrieval.enhanced_search(
                                query=query,
                                top_k=10,
                                use_intent=aliyun_config.is_intent_enabled(),
                                use_rerank=aliyun_config.is_rerank_enabled()
                            )

                            # Convert to similar_tools format for compatibility
                            similar_tools = []
                            for result in enhanced_results:
                                similar_tools.append({
                                    "tool_id": result.tool_id,
                                    "similarity": result.final_score,
                                    "tool_data": {
                                        "name": result.tool_name,
                                        "description": result.description
                                    },
                                    "match_details": {
                                        "original_similarity": result.original_similarity,
                                        "rerank_score": result.rerank_score,
                                        "intent_match": result.intent_match
                                    },
                                    "reasoning": result.reasoning,
                                    "selection_method": "aliyun_enhanced",
                                    "execution_time": 0.0
                                })

                            logging.info(f"🚀 Aliyun Enhanced Retrieval returned {len(similar_tools)} results")

                        except Exception as e:
                            logging.error(f"Aliyun Enhanced Retrieval error, falling back: {e}")
                            # 回退到优化选择器
                            try:
                                selection_results = await optimized_selector.select_tools(
                                    query=query,
                                    user_id=None,
                                    top_k=10
                                )

                                # Convert to similar_tools format for compatibility
                                similar_tools = []
                                for result in selection_results:
                                    similar_tools.append({
                                        "tool_id": result.tool_id,
                                        "similarity": result.confidence,
                                        "tool_data": {
                                            "name": result.tool_name,
                                            "description": result.description
                                        },
                                        "match_details": result.match_details,
                                        "reasoning": result.reasoning,
                                        "selection_method": result.selection_method,
                                        "execution_time": result.execution_time
                                    })

                            except Exception as e2:
                                logging.error(f"Optimized selector also failed, using basic search: {e2}")
                                similar_tools = await cached_store.search_similar_tools(query, top_k=10)

                    else:
                        # 使用优化选择器
                        try:
                            selection_results = await optimized_selector.select_tools(
                                query=query,
                                user_id=None,
                                top_k=10
                            )

                            # Convert to similar_tools format for compatibility
                            similar_tools = []
                            for result in selection_results:
                                similar_tools.append({
                                    "tool_id": result.tool_id,
                                    "similarity": result.confidence,
                                    "tool_data": {
                                        "name": result.tool_name,
                                        "description": result.description
                                    },
                                    "match_details": result.match_details,
                                    "reasoning": result.reasoning,
                                    "selection_method": result.selection_method,
                                    "execution_time": result.execution_time
                                })

                        except Exception as e:
                            logging.error(f"Optimized selector error, falling back to standard: {e}")
                            similar_tools = await cached_store.search_similar_tools(query, top_k=10)

                    if similar_tools:
                        # Create detailed response with similarity scores
                        tool_descriptions = []
                        selection_methods = set()
                        total_execution_time = 0

                        for tool_info in similar_tools:
                            tool_data = tool_info["tool_data"]
                            similarity = tool_info["similarity"]
                            match_details = tool_info.get("match_details", {})
                            selection_method = tool_info.get("selection_method", "unknown")
                            execution_time = tool_info.get("execution_time", 0)

                            selection_methods.add(selection_method)
                            total_execution_time += execution_time

                            # Format similarity score as percentage
                            similarity_pct = similarity * 100

                            # Create detailed description with method indicator
                            method_emoji = {
                                "hybrid_similarity": "🔄",
                                "multi_agent": "🤖",
                                "ensemble": "🎯",
                                "aliyun_enhanced": "🚀",
                                "unknown": "❓"
                            }.get(selection_method, "❓")

                            desc = f"{method_emoji} **{tool_data['name']}** (Match: {similarity_pct:.1f}%)"
                            desc += f"\n   📝 {tool_data['description']}"

                            # Add match breakdown based on selection method
                            if selection_method == "aliyun_enhanced" and match_details:
                                # 阿里云增强检索的详细信息
                                original_sim = match_details.get('original_similarity', 0) * 100
                                rerank_score = match_details.get('rerank_score', 0) * 100
                                intent_match = match_details.get('intent_match', False)
                                desc += f"\n   📊 Original: {original_sim:.1f}% | Rerank: {rerank_score:.1f}% "
                            elif match_details:
                                # 传统检索的详细信息
                                semantic = match_details.get('semantic_score', 0) * 100
                                keyword = match_details.get('keyword_score', 0) * 100
                                name_match = match_details.get('name_match_score', 0) * 100
                                desc += f"\n   📊 Semantic: {semantic:.1f}% | Keywords: {keyword:.1f}% | Name: {name_match:.1f}%"

                            # Add reasoning if available
                            if 'reasoning' in tool_info:
                                desc += f"\n   🧠 Reasoning: {tool_info['reasoning']}"

                            # Add selection method info
                            desc += f"\n   ⚙️ Method: {selection_method}"

                            tool_descriptions.append(desc)

                        tools_text = "\n\n".join(tool_descriptions)

                        # Create response with method information
                        methods_used = ", ".join(selection_methods)
                        avg_time = total_execution_time / len(similar_tools) if similar_tools else 0

                        response = f"🎯 Found {len(similar_tools)} relevant tools using {methods_used}:\n\n{tools_text}\n\n"
                        response += f"⏱️ Average selection time: {avg_time:.3f}s\n"

                        # Add performance info if available
                        if hasattr(optimized_selector, 'get_performance_report'):
                            perf_report = optimized_selector.get_performance_report()
                            response += f"📈 Strategy: {perf_report['strategy']} | Recommendation: {perf_report['recommendation']}"

                        return {"messages": [AIMessage(content=response)]}

                    else:
                        return {"messages": [AIMessage(content="❌ I couldn't find any relevant tools for your query. Could you please rephrase or be more specific?")]}

                return {"messages": []}

            # Create a simple graph
            workflow = StateGraph(MessagesState)
            workflow.add_node("tool_selection", tool_selection_node)
            workflow.set_entry_point("tool_selection")
            workflow.set_finish_point("tool_selection")

            agent = workflow.compile()

            # Create a mapping of tool names to descriptions for enhanced display
            tool_name_to_description = {tool.name: tool.description for tool in all_tools}

            print("\n🤖 MCP Chatbot with Enhanced Retrieval Ready!")
            print(f"Loaded {len(all_tools)} tools with cached vectors")

            if self.aliyun_mode:
                print("🚀 Aliyun Enhanced Retrieval: ENABLED")
                aliyun_weights = aliyun_config.get_weights()
                print(f"   权重配置: 重排序={aliyun_weights['rerank_weight']:.1f}, 原始={aliyun_weights['original_weight']:.1f}")
                # print(f"   意图检测: {'启用' if aliyun_config.is_intent_enabled() else '禁用'}")
                print(f"   文本重排序: {'启用' if aliyun_config.is_rerank_enabled() else '禁用'}")
            else:
                print("🎯 Using hybrid similarity: semantic + keyword + name matching")
                weights = SimilarityConfig.get_weights()
                print(f"⚖️  Current weights: Semantic={weights['semantic']:.1f}, Keywords={weights['keyword']:.1f}, Name={weights['name_match']:.1f}")

            # 显示可用命令
            commands = ["'quit'/'exit'", "'clear_cache'", "'weights'", "'performance'", "'config'"]
            if self.aliyun_mode:
                commands.append("'aliyun'")
            if self.enhanced_tool_mode:
                commands.append("'multi_tool'")

            print(f"Commands: {', '.join(commands)}")

            if self.enhanced_tool_mode:
                print("🎯 Enhanced Tool Selector: ENABLED (supports multi-tool queries)")
                if hasattr(self, 'enhanced_tool_selector') and self.enhanced_tool_selector:
                    stats = self.enhanced_tool_selector.get_statistics()
                    print(f"   Multi-tool processing: {'启用' if stats['multi_tool_enabled'] else '禁用'}")

            while True:
                try:
                    user_input = input("\nYou: ").strip()
                    if user_input.lower() in ["quit", "exit"]:
                        logging.info("Exiting...")
                        break
                    elif user_input.lower() == "clear_cache":
                        self.vector_cache.clear()
                        print("Vector cache cleared!")
                        continue
                    elif user_input.lower() == "weights":
                        self._handle_weights_command()
                        continue
                    elif user_input.lower() == "performance":
                        self._handle_performance_command(optimized_selector)
                        continue
                    elif user_input.lower() == "config":
                        self._handle_config_command()
                        continue
                    elif user_input.lower() == "aliyun" and self.aliyun_mode:
                        self._handle_aliyun_command()
                        continue
                    elif user_input.lower() == "multi_tool" and self.enhanced_tool_mode:
                        self._handle_multi_tool_command()
                        continue
                    # Use our custom agent
                    start_time = asyncio.get_event_loop().time()
                    result = await agent.ainvoke({"messages": [HumanMessage(content=user_input)]})
                    end_time = asyncio.get_event_loop().time()

                    # Display the response
                    if "messages" in result and result["messages"]:
                        last_message = result["messages"][-1]
                        if hasattr(last_message, 'content'):
                            print(f"\n🤖 Assistant: {last_message.content}")
                        else:
                            print(f"\n🤖 Assistant: {last_message}")
                except KeyboardInterrupt:
                    logging.info("Exiting...")
                    break
                except Exception as e:
                    logging.error(f"Error processing request: {e}")
                    print(f"Sorry, I encountered an error: {e}")

        finally:
            await self.cleanup_servers()


async def main() -> None:
    """Initialize and run the chat session."""
    config = Configuration()
    server_config = config.load_config("servers_config.json")
    servers = [
        Server(name, srv_config)
        for name, srv_config in server_config["mcpServers"].items()
    ]
    llm_client = LLMClient(config.llm_api_key)
    chat_session = ChatSession(servers, llm_client)
    await chat_session.start()


if __name__ == "__main__":
    asyncio.run(main())
