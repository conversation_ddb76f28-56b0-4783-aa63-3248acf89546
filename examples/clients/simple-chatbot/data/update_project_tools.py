#!/usr/bin/env python3
"""
脚本用于将 projects_202507031628.json 中的 name 和 description 
根据 id 对应关系写入到 project_tools_202507031621.json 中
"""

import json
import os
from typing import Dict, Any

def load_json_file(file_path: str) -> Dict[str, Any]:
    """加载 JSON 文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"错误：找不到文件 {file_path}")
        return {}
    except json.JSONDecodeError as e:
        print(f"错误：解析 JSON 文件 {file_path} 失败: {e}")
        return {}

def save_json_file(file_path: str, data: Dict[str, Any]) -> bool:
    """保存 JSON 文件"""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent='\t')
        return True
    except Exception as e:
        print(f"错误：保存文件 {file_path} 失败: {e}")
        return False

def create_project_mapping(projects_data: Dict[str, Any]) -> Dict[int, Dict[str, str]]:
    """创建项目 ID 到名称和描述的映射"""
    mapping = {}
    
    if 'projects' not in projects_data:
        print("错误：projects_202507031628.json 中没有找到 'projects' 键")
        return mapping
    
    for project in projects_data['projects']:
        if 'id' in project and 'name' in project and 'description' in project:
            mapping[project['id']] = {
                'name': project['name'],
                'description': project['description']
            }
    
    print(f"成功创建了 {len(mapping)} 个项目的映射")
    return mapping

def update_project_tools(tools_data: Dict[str, Any], project_mapping: Dict[int, Dict[str, str]]) -> int:
    """更新项目工具数据，添加项目名称和描述"""
    updated_count = 0
    
    if 'project_tools' not in tools_data:
        print("错误：project_tools_202507031621.json 中没有找到 'project_tools' 键")
        return updated_count
    
    for tool in tools_data['project_tools']:
        if 'project_id' in tool:
            project_id = tool['project_id']
            
            if project_id in project_mapping:
                # 添加项目名称和描述
                tool['project_name'] = project_mapping[project_id]['name']
                tool['project_description'] = project_mapping[project_id]['description']
                updated_count += 1
            else:
                print(f"警告：找不到 project_id {project_id} 对应的项目信息")
    
    return updated_count

def main():
    """主函数"""
    # 文件路径
    projects_file = "projects_202507031628.json"
    tools_file = "project_tools_202507031621.json"
    
    # 检查文件是否存在
    if not os.path.exists(projects_file):
        print(f"错误：文件 {projects_file} 不存在")
        return
    
    if not os.path.exists(tools_file):
        print(f"错误：文件 {tools_file} 不存在")
        return
    
    print("开始处理文件...")
    
    # 加载项目数据
    print(f"正在加载 {projects_file}...")
    projects_data = load_json_file(projects_file)
    if not projects_data:
        return
    
    # 加载工具数据
    print(f"正在加载 {tools_file}...")
    tools_data = load_json_file(tools_file)
    if not tools_data:
        return
    
    # 创建项目映射
    print("正在创建项目映射...")
    project_mapping = create_project_mapping(projects_data)
    if not project_mapping:
        return
    
    # 更新工具数据
    print("正在更新工具数据...")
    updated_count = update_project_tools(tools_data, project_mapping)
    
    if updated_count > 0:
        # 创建备份文件
        backup_file = f"{tools_file}.backup"
        print(f"正在创建备份文件 {backup_file}...")
        if save_json_file(backup_file, load_json_file(tools_file)):
            print(f"备份文件创建成功")
        
        # 保存更新后的数据
        print(f"正在保存更新后的数据到 {tools_file}...")
        if save_json_file(tools_file, tools_data):
            print(f"成功更新了 {updated_count} 个工具记录")
            print("处理完成！")
        else:
            print("保存文件失败")
    else:
        print("没有找到需要更新的记录")

if __name__ == "__main__":
    main()
