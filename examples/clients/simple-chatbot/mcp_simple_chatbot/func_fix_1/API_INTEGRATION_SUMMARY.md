# API Integration Summary

## 修改概述

本项目已成功修改为从API接口获取工具数据，替代了原来的servers_config.json文件读取方式。

## 主要变更

### 1. 新增API客户端 (`src/core/api_client.py`)

- **功能**: 从 `https://www.mcpcn.cc/api/projectTools/getAllProjectToolsList` 获取工具数据
- **主要方法**:
  - `fetch_tools_data()`: 获取原始API数据
  - `transform_to_tool_format()`: 将API数据转换为所需格式
  - `generate_mock_server_config()`: 生成兼容的服务器配置
  - `get_server_config()`: 获取完整的服务器配置
  - `get_tools_list()`: 获取转换后的工具列表

### 2. 更新配置类 (`src/core/config.py`)

- **新增方法**:
  - `load_config_from_api()`: 异步从API加载配置
  - `get_tools_from_api()`: 从API获取工具列表

### 3. 更新主程序 (`main_simple.py`)

- **参数变更**: `initialize_global_session()` 新增 `use_api` 参数
- **默认行为**: 默认使用API获取数据（`use_api=True`）
- **兼容性**: 保留了从文件读取配置的选项

### 4. 更新API服务器 (`api_server.py`)

- **请求模型**: `InitRequest` 新增 `use_api` 字段
- **响应数据**: 包含API使用状态信息

## 数据转换格式

### API返回格式
```json
{
  "code": 0,
  "data": [
    {
      "name": "get_video_info",
      "description": "Get detailed information about a video file",
      "descriptionChinese": "获取有关视频文件的详细信息",
      "inputSchema": {
        "type": "object",
        "properties": {
          "filePath": {
            "type": "string",
            "description": "Path to the video file"
          }
        },
        "required": ["filePath"]
      }
    }
  ]
}
```

### 转换后格式
```json
[
  {
    "name": "get_video_info",
    "description": "获取有关视频文件的详细信息",
    "parameters": {
      "type": "object",
      "properties": {
        "filePath": {
          "type": "string",
          "description": "Path to the video file"
        }
      },
      "required": ["filePath"]
    }
  }
]
```

## 使用方式

### 1. 默认使用API（推荐）

```python
# 使用API获取工具数据
await initialize_global_session(embedding_type="aliyun", use_api=True)
```

### 2. 使用本地配置文件

```python
# 使用本地servers_config.json文件
await initialize_global_session(embedding_type="aliyun", use_api=False, config_file="servers_config.json")
```

### 3. API服务器初始化

```bash
curl -X POST "http://localhost:8000/initialize" \
  -H "Content-Type: application/json" \
  -d '{
    "embedding_type": "aliyun",
    "use_api": true
  }'
```

## 测试验证

运行测试脚本验证API集成：

```bash
python test_api_integration.py
```

测试结果显示：
- ✅ 成功获取762个工具
- ✅ 数据转换正常工作
- ✅ 生成677个服务器配置
- ✅ 配置类API方法正常工作

## 优势

1. **实时数据**: 从API获取最新的工具数据
2. **自动更新**: 无需手动维护本地配置文件
3. **向后兼容**: 保留了原有的文件读取方式
4. **错误处理**: 完善的异常处理和日志记录
5. **性能优化**: 异步处理，提高响应速度

## 注意事项

1. 需要网络连接才能从API获取数据
2. API超时设置为30秒
3. 保留了原有的servers_config.json作为备用方案
4. 所有API调用都是异步的，确保不会阻塞主线程