#!/usr/bin/env python3
"""
简化版MCP工具检索系统主入口
将复杂逻辑拆分到各个专门的模块中
"""

import asyncio
import logging
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional

# 添加模块路径
sys.path.append(str(Path(__file__).parent))

# 导入模块化组件
from src.core.config import Configuration
from src.core.server import Server
from src.core.api_server import APIServer
from src.chat.session import ChatSession

# 配置日志
logging.basicConfig(
    level=logging.INFO, 
    format="%(asctime)s - %(levelname)s - %(message)s"
)

# 全局变量存储初始化后的chat_session
_global_chat_session: Optional[ChatSession] = None
_initialization_lock = asyncio.Lock()


async def initialize_global_session(
    embedding_type: str = "aliyun", 
    use_api: bool = True,
    config_file: str = "servers_config.json"
) -> None:
    """
    全局初始化函数，只执行一次初始化工作

    Args:
        embedding_type: 嵌入模型类型 ("aliyun", "doubao", "openai")
        use_api: 是否使用API获取工具数据，默认为True
        config_file: 服务器配置文件路径（当use_api=False时使用）
    """
    global _global_chat_session

    async with _initialization_lock:
        if _global_chat_session is not None:
            logging.info("🔄 Global session already initialized, skipping...")
            return

        logging.info(f"🚀 Initializing global MCP session (Embedding: {embedding_type}, API: {use_api})")

        try:
            # 加载配置
            config = Configuration()
            
            if use_api:
                # 从API获取配置
                logging.info("📡 Loading configuration from API...")
                server_config = await config.load_config_from_api()
            else:
                # 从文件获取配置
                logging.info(f"📁 Loading configuration from file: {config_file}")
                server_config = config.load_config(config_file)
            
            # 创建服务器实例
            servers = []
            for name, srv_config in server_config["mcpServers"].items():
                if srv_config.get("type") == "api":
                    # API类型的服务器使用APIServer
                    servers.append(APIServer(name, srv_config))
                else:
                    # 传统MCP服务器使用Server
                    servers.append(Server(name, srv_config))

            # 创建聊天会话
            chat_session = ChatSession(
                servers=servers,
                max_tools_per_task=5,
                embedding_type=embedding_type,
                config=config
            )

            # 先初始化服务器
            await chat_session.initialize_servers()

            # 然后初始化系统组件
            (all_tools, cached_store, tool_registry,
             original_tools_map, tool_selector, llm) = await chat_session.initialize_system()

            # 保存组件到chat_session实例
            chat_session.all_tools = all_tools
            chat_session.cached_store = cached_store
            chat_session.tool_registry = tool_registry
            chat_session.original_tools_map = original_tools_map
            chat_session.tool_selector = tool_selector
            chat_session.llm = llm
            chat_session._initialized = True

            _global_chat_session = chat_session
            logging.info("✅ Global session initialized successfully")

        except Exception as e:
            logging.error(f"❌ Failed to initialize global session: {e}")
            raise


async def query_tools_json(user_query: str, max_tools: int = 10) -> List[Dict[str, Any]]:
    """
    查询工具并返回JSON格式的数据
    
    Args:
        user_query: 用户查询字符串
        
    Returns:
        工具列表，格式为 [{"name": "...", "description": "...", "parameters": {...}}]
    """
    global _global_chat_session
    
    if _global_chat_session is None:
        return []
    
    try:
        # 确保系统已初始化
        if not _global_chat_session._initialized:
            logging.info("🔄 正在初始化系统组件...")
            (all_tools, cached_store, tool_registry,
             original_tools_map, tool_selector, llm) = await _global_chat_session.initialize_system()
            
            # 保存组件到chat_session实例
            _global_chat_session.all_tools = all_tools
            _global_chat_session.cached_store = cached_store
            _global_chat_session.tool_registry = tool_registry
            _global_chat_session.original_tools_map = original_tools_map
            _global_chat_session.tool_selector = tool_selector
            _global_chat_session.llm = llm
            _global_chat_session._initialized = True
        
        # 使用工具选择器获取JSON格式的工具数据
        if _global_chat_session.tool_selector:
            return await _global_chat_session.tool_selector.get_tools_json_format(
                user_query, 
                max_tools=max_tools, 
                original_tools_map=_global_chat_session.original_tools_map
            )
        else:
            return []
    except Exception as e:
        logging.error(f"❌ 查询工具JSON格式时出错: {e}")
        return []


async def query_tools_fast(user_query: str) -> str:
    """
    快速查询接口，使用已初始化的全局session

    Args:
        user_query: 用户查询字符串

    Returns:
        工具检索结果
    """
    global _global_chat_session

    if _global_chat_session is None:
        return "❌ Global session not initialized. Please call initialize_global_session() first."

    try:
        return await _global_chat_session.process_query(user_query)
    except Exception as e:
        logging.error(f"❌ Error in fast query: {e}")
        return f"❌ Error processing query: {str(e)}"


async def query_tools(
    user_query: str, 
    config_file: str = "servers_config.json", 
    embedding_type: str = "aliyun"
) -> str:
    """
    兼容性查询接口（保持向后兼容）

    Args:
        user_query: The user's query string
        config_file: Path to the server configuration file
        embedding_type: Type of embedding service to use

    Returns:
        The response from the tool selection system
    """
    # 如果全局session未初始化，先初始化
    if _global_chat_session is None:
        await initialize_global_session(embedding_type, use_api=True, config_file=config_file)

    # 使用快速查询
    return await query_tools_fast(user_query)


def get_global_session() -> Optional[ChatSession]:
    """获取全局session对象"""
    return _global_chat_session


def is_global_session_initialized() -> bool:
    """检查全局session是否已初始化"""
    return _global_chat_session is not None


def get_global_session_tools_count() -> int:
    """获取全局session中的工具数量"""
    if _global_chat_session and hasattr(_global_chat_session, 'all_tools'):
        return len(_global_chat_session.all_tools)
    return 0


async def cleanup_global_session() -> None:
    """清理全局session资源"""
    global _global_chat_session

    if _global_chat_session is not None:
        try:
            await _global_chat_session.cleanup_servers()
            _global_chat_session = None
            logging.info("🧹 Global session cleaned up")
        except Exception as e:
            logging.error(f"❌ Error cleaning up global session: {e}")


async def main(embedding_type: str = "aliyun") -> None:
    """运行交互式聊天会话"""
    # 初始化全局session（包括完整的系统初始化）
    await initialize_global_session(embedding_type)
    
    # 预先完成系统初始化，避免第一次查询时重新初始化
    if _global_chat_session:
        logging.info("🔄 Pre-initializing system components...")
        await _global_chat_session.initialize_system()
        _global_chat_session._initialized = True
        logging.info("✅ System pre-initialization completed")
        
        # 运行交互式会话
        await _global_chat_session.start()
    else:
        logging.error("❌ Failed to start interactive session")


async def start_server_mode(embedding_type: str = "aliyun") -> None:
    """启动服务器模式：先初始化，然后等待查询"""
    print(f"🚀 启动MCP工具检索服务器 (Embedding: {embedding_type})")
    print("💡 支持的embedding类型: aliyun, doubao, openai")

    try:
        # 初始化全局session（包括完整的系统初始化）
        await initialize_global_session(embedding_type)
        
        # 预先完成系统初始化，避免查询时重新初始化
        if _global_chat_session:
            print("🔄 正在预初始化系统组件...")
            await _global_chat_session.initialize_system()
            _global_chat_session._initialized = True
            print("✅ 系统预初始化完成")
        
        print("✅ 服务器初始化完成，可以开始接收查询请求")

        # 进入交互模式
        print("\\n" + "="*50)
        print("🔍 进入查询模式 - 输入查询内容，输入 'quit' 退出")
        print("💡 现在查询将直接使用缓存的向量，无需重新初始化")
        print("="*50)

        while True:
            try:
                user_input = input("\\n🤖 请输入查询: ").strip()

                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("👋 退出查询模式")
                    break

                if not user_input:
                    print("❌ 查询内容不能为空")
                    continue

                print(f"\\n🔍 正在检索: {user_input}")
                start_time = asyncio.get_event_loop().time()

                # 使用快速查询接口（直接使用缓存向量）
                result = await query_tools_fast(user_input)

                end_time = asyncio.get_event_loop().time()
                processing_time = end_time - start_time

                print(f"\\n⚡ 检索完成 (耗时: {processing_time:.2f}s)")
                print(f"📋 结果: {result}")

            except KeyboardInterrupt:
                print("\\n👋 用户中断，退出查询模式")
                break
            except Exception as e:
                print(f"❌ 查询出错: {e}")

    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
    finally:
        # 清理资源
        await cleanup_global_session()
        print("🧹 资源清理完成")


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="MCP工具检索系统")
    parser.add_argument("--embedding", default="aliyun", 
                       choices=["aliyun", "doubao", "openai"],
                       help="选择embedding类型")
    parser.add_argument("--server", action="store_true", 
                       help="启动服务器模式")
    parser.add_argument("query", nargs="*", 
                       help="单次查询内容")

    args = parser.parse_args()

    if args.server:
        # 服务器模式：先初始化，然后等待查询
        asyncio.run(start_server_mode(embedding_type=args.embedding))
    elif args.query:
        # 单次查询模式：执行查询后退出
        query = " ".join(args.query)
        result = asyncio.run(query_tools(query, embedding_type=args.embedding))
        print(result)
    else:
        # 传统交互模式
        print(f"🚀 启动MCP聊天机器人 (Embedding: {args.embedding})")
        print("💡 支持的embedding类型: aliyun, doubao, openai")
        print("💡 使用方式:")
        print("   - 交互模式: python main_simple.py --embedding aliyun")
        print("   - 服务器模式: python main_simple.py --server --embedding aliyun")
        print("   - 单次查询: python main_simple.py --embedding aliyun 'your query here'")
        asyncio.run(main(embedding_type=args.embedding))