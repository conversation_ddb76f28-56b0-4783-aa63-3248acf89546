#!/usr/bin/env python3
"""
API使用示例：从API获取工具数据并进行查询
"""

import asyncio
import json
from pathlib import Path
import sys

# 添加模块路径
sys.path.append(str(Path(__file__).parent))

from main_simple import initialize_global_session, query_tools_fast, cleanup_global_session
from src.core.api_client import ProjectToolsAPIClient


async def example_direct_api_usage():
    """直接使用API客户端的示例"""
    print("📡 直接API客户端使用示例")
    print("="*50)
    
    client = ProjectToolsAPIClient()
    
    # 获取工具列表
    tools = await client.get_tools_list()
    print(f"✅ 获取到 {len(tools)} 个工具")
    
    # 显示前5个工具
    print("\n📋 前5个工具:")
    for i, tool in enumerate(tools[:5]):
        print(f"{i+1}. {tool['name']}: {tool['description']}")
    
    # 保存工具列表到JSON文件
    with open("tools_from_api.json", "w", encoding="utf-8") as f:
        json.dump(tools, f, ensure_ascii=False, indent=2)
    
    print("\n💾 工具列表已保存到 tools_from_api.json")


async def example_integration_usage():
    """集成使用示例：初始化系统并进行查询"""
    print("\n🔧 集成使用示例")
    print("="*50)
    
    try:
        # 初始化系统，使用API获取工具数据
        print("🚀 正在初始化系统（使用API数据）...")
        await initialize_global_session(
            embedding_type="aliyun", 
            use_api=True
        )
        print("✅ 系统初始化完成")
        
        # 进行几个示例查询
        queries = [
            "查询天气",
            "计算数学",
            "获取新闻",
            "文件压缩",
            "二维码生成"
        ]
        
        print("\n🔍 进行示例查询:")
        for query in queries:
            print(f"\n查询: {query}")
            result = await query_tools_fast(query)
            print(f"结果: {result}")
            
    except Exception as e:
        print(f"❌ 错误: {e}")
    finally:
        # 清理资源
        await cleanup_global_session()
        print("\n🧹 系统资源已清理")


async def example_fallback_usage():
    """备用方案示例：使用本地配置文件"""
    print("\n💾 备用方案示例（使用本地配置文件）")
    print("="*50)
    
    try:
        # 清理之前的会话
        await cleanup_global_session()
        
        # 使用本地配置文件初始化
        print("🚀 正在初始化系统（使用本地配置文件）...")
        await initialize_global_session(
            embedding_type="aliyun", 
            use_api=False,
            config_file="servers_config.json"
        )
        print("✅ 系统初始化完成")
        
        # 进行查询
        result = await query_tools_fast("查询天气")
        print(f"查询结果: {result}")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
    finally:
        # 清理资源
        await cleanup_global_session()
        print("\n🧹 系统资源已清理")


async def main():
    """主函数"""
    print("🌟 MCP工具系统API集成示例")
    print("="*60)
    
    # 示例1：直接使用API客户端
    await example_direct_api_usage()
    
    # 示例2：集成使用
    await example_integration_usage()
    
    # 示例3：备用方案
    await example_fallback_usage()
    
    print("\n🎉 所有示例完成!")


if __name__ == "__main__":
    asyncio.run(main())