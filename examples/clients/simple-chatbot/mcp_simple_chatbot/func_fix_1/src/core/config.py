"""Configuration management for MCP tool retrieval system."""

import json
import os
from typing import Any, Dict, Optional
from pathlib import Path
from dotenv import load_dotenv
from .api_client import ProjectToolsAPIClient


class Configuration:
    """Manages configuration and environment variables for the MCP client."""

    def __init__(self) -> None:
        """Initialize configuration with environment variables."""
        self.load_env()
        self._load_api_keys()
        self._load_embedding_configs()

    @staticmethod
    def load_env() -> None:
        """Load environment variables from .env file."""
        load_dotenv()

    def _load_api_keys(self) -> None:
        """Load API keys from environment variables only - no hardcoded defaults."""
        self.api_keys = {
            'aliyun': "sk-fcac337c29fe4d6f93bb9ff2ca2395d8",
            'volcengine': "935e14d7-245c-4adc-8cca-179ae0947829",
            'openai': "https://yunwu.ai/v1",
            'llm':"sk-VZh4Djx1C6lFg8QsAtn75CgMsWZOmEllIjSh4KSDR7K1mdkb"
        }

    def _load_embedding_configs(self) -> None:
        """Load embedding configurations."""
        self.embedding_configs = {
            'aliyun': {
                'model': 'text-embedding-v4',
                'dimensions': 1024,
                'instruct': 'Given a web search query, retrieve relevant passages that answer the query',
                'max_batch_size': 10,
                'timeout': 30
            },
            'volcengine': {
                'model': 'doubao-embedding-large-text-250515',
                'dimensions': 2048,
                'max_batch_size': 50,
                'timeout': 30
            },
            'openai': {
                'model': 'text-embedding-3-large',
                'dimensions': 3072,
                'timeout': 30
            }
        }

    @staticmethod
    def load_config(file_path: str) -> Dict[str, Any]:
        """Load server configuration from JSON file.

        Args:
            file_path: Path to the JSON configuration file.

        Returns:
            Dict containing server configuration.

        Raises:
            FileNotFoundError: If configuration file doesn't exist.
            JSONDecodeError: If configuration file is invalid JSON.
        """
        with open(file_path, "r") as f:
            return json.load(f)

    async def load_config_from_api(self) -> Dict[str, Any]:
        """Load server configuration from API.

        Returns:
            Dict containing server configuration.

        Raises:
            Exception: If API request fails.
        """
        api_client = ProjectToolsAPIClient()
        return await api_client.get_server_config()

    async def get_tools_from_api(self) -> Dict[str, Any]:
        """Get tools list from API in the required format.

        Returns:
            Dict containing tools list.

        Raises:
            Exception: If API request fails.
        """
        api_client = ProjectToolsAPIClient()
        tools_list = await api_client.get_tools_list()
        return {"tools": tools_list}

    def get_api_key(self, provider: str) -> str:
        """Get API key for specific provider.

        Args:
            provider: The provider name (aliyun, volcengine, openai, llm).

        Returns:
            The API key as a string.

        Raises:
            ValueError: If the API key is not found.
        """
        if provider not in self.api_keys:
            raise ValueError(f"Unknown provider: {provider}")
        
        api_key = self.api_keys[provider]
        if not api_key:
            env_var_map = {
                'aliyun': 'DASHSCOPE_API_KEY',
                'volcengine': 'VOLCENGINE_API_KEY',
                'openai': 'OPENAI_API_KEY',
                'llm': 'LLM_API_KEY'
            }
            env_var = env_var_map.get(provider, f"{provider.upper()}_API_KEY")
            raise ValueError(
                f"API key for {provider} not found. "
                f"Please set the {env_var} environment variable."
            )
        
        return api_key

    def get_embedding_config(self, provider: str) -> Dict[str, Any]:
        """Get embedding configuration for specific provider.

        Args:
            provider: The provider name (aliyun, volcengine, openai).

        Returns:
            The embedding configuration as a dictionary.

        Raises:
            ValueError: If the provider is not supported.
        """
        if provider not in self.embedding_configs:
            raise ValueError(f"Unknown embedding provider: {provider}")
        
        return self.embedding_configs[provider].copy()

    def get_cache_dir(self, provider: str) -> str:
        """Get cache directory for specific provider.

        Args:
            provider: The provider name.

        Returns:
            Cache directory path.
        """
        cache_dirs = {
            'aliyun': '.vector_cache_aliyun',
            'volcengine': '.vector_cache_volcengine',
            'openai': '.vector_cache_openai'
        }
        return cache_dirs.get(provider, '.vector_cache')

    def validate_config(self) -> bool:
        """Validate the current configuration.

        Returns:
            True if configuration is valid, False otherwise.
        """
        try:
            # Check if at least one API key is available
            available_keys = [key for key in self.api_keys.values() if key]
            if not available_keys:
                return False
            
            # Check if embedding configs are valid
            for provider, config in self.embedding_configs.items():
                if not config.get('model'):
                    return False
                if not isinstance(config.get('dimensions'), int):
                    return False
            
            return True
        except Exception:
            return False

    def get_available_providers(self) -> list[str]:
        """Get list of available providers with valid API keys.

        Returns:
            List of provider names.
        """
        return [provider for provider, key in self.api_keys.items() if key]