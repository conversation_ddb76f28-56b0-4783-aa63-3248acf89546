"""Tool representation and formatting."""

from typing import Any, Dict


class Tool:
    """Represents a tool with its properties and formatting."""

    def __init__(
        self, name: str, description: str, input_schema: Dict[str, Any]
    ) -> None:
        self.name: str = name
        self.description: str = description
        self.input_schema: Dict[str, Any] = input_schema

    def format_for_llm(self) -> str:
        """Format tool information for LLM.

        Returns:
            A formatted string describing the tool.
        """
        args_desc = []
        if "properties" in self.input_schema:
            for param_name, param_info in self.input_schema["properties"].items():
                arg_desc = (
                    f"- {param_name}: {param_info.get('description', 'No description')}"
                )
                if param_name in self.input_schema.get("required", []):
                    arg_desc += " (required)"
                args_desc.append(arg_desc)

        return f"""
Tool: {self.name}
Description: {self.description}
Arguments:
{chr(10).join(args_desc)}
"""

    def to_dict(self) -> Dict[str, Any]:
        """Convert tool to dictionary representation.

        Returns:
            Tool data as dictionary.
        """
        return {
            "name": self.name,
            "description": self.description,
            "input_schema": self.input_schema
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Tool':
        """Create Tool instance from dictionary.

        Args:
            data: Tool data as dictionary.

        Returns:
            Tool instance.
        """
        return cls(
            name=data["name"],
            description=data["description"],
            input_schema=data["input_schema"]
        )