"""Similarity calculation utilities."""

import re
from typing import List
import numpy as np

try:
    from ..common.similarity_config import SimilarityConfig
except ImportError:
    # Fallback configuration if similarity_config is not available
    class SimilarityConfig:
        SEMANTIC_WEIGHT = 0.8
        KEYWORD_WEIGHT = 0.1
        NAME_MATCH_WEIGHT = 0.1
        STOP_WORDS = set()
        MIN_WORD_LENGTH = 2


class SimilarityCalculator:
    """Handles various similarity calculations for tool retrieval."""

    @staticmethod
    def cosine_similarity(vec1: List[float], vec2: List[float]) -> float:
        """Calculate optimized cosine similarity between two vectors.

        Args:
            vec1: First vector.
            vec2: Second vector.

        Returns:
            Cosine similarity score between 0 and 1.
        """
        vec1_np = np.array(vec1, dtype=np.float32)
        vec2_np = np.array(vec2, dtype=np.float32)

        dot_product = np.dot(vec1_np, vec2_np)
        norm1 = np.linalg.norm(vec1_np)
        norm2 = np.linalg.norm(vec2_np)

        if norm1 == 0 or norm2 == 0:
            return 0.0

        return float(dot_product / (norm1 * norm2))

    @staticmethod
    def keyword_similarity(query: str, tool_name: str, tool_description: str) -> float:
        """Calculate keyword overlap similarity using Jaccard index.

        Args:
            query: The search query.
            tool_name: Name of the tool.
            tool_description: Description of the tool.

        Returns:
            Keyword similarity score between 0 and 1.
        """
        # Extract keywords (alphanumeric words, convert to lowercase)
        query_words = set(re.findall(r'\w+', query.lower()))
        tool_text = f"{tool_name} {tool_description}".lower()
        tool_words = set(re.findall(r'\w+', tool_text))

        # Filter out stop words and short words
        query_words = {w for w in query_words
                      if len(w) >= SimilarityConfig.MIN_WORD_LENGTH
                      and w not in SimilarityConfig.STOP_WORDS}
        tool_words = {w for w in tool_words
                     if len(w) >= SimilarityConfig.MIN_WORD_LENGTH
                     and w not in SimilarityConfig.STOP_WORDS}

        if not query_words or not tool_words:
            return 0.0

        # Calculate Jaccard similarity (intersection over union)
        intersection = len(query_words & tool_words)
        union = len(query_words | tool_words)

        return intersection / union if union > 0 else 0.0

    @staticmethod
    def name_match_similarity(query: str, tool_name: str) -> float:
        """Calculate tool name matching score.

        Args:
            query: The search query.
            tool_name: Name of the tool.

        Returns:
            Name match similarity score between 0 and 1.
        """
        query_lower = query.lower()
        name_lower = tool_name.lower()

        # Exact substring match
        if name_lower in query_lower or query_lower in name_lower:
            return 1.0

        # Partial word match (handle underscore-separated tool names)
        name_words = set(name_lower.replace('_', ' ').split())
        query_words = set(query_lower.split())

        if name_words & query_words:
            return 0.5

        return 0.0

    @staticmethod
    def hybrid_similarity(query: str, tool_name: str, tool_description: str,
                         query_vector: List[float], tool_vector: List[float]) -> float:
        """Calculate hybrid similarity combining semantic and keyword matching.

        Args:
            query: The search query.
            tool_name: Name of the tool.
            tool_description: Description of the tool.
            query_vector: Query vector representation.
            tool_vector: Tool vector representation.

        Returns:
            Combined similarity score between 0 and 1.
        """
        # 1. Semantic similarity (vector-based)
        semantic_sim = SimilarityCalculator.cosine_similarity(query_vector, tool_vector)

        # 2. Keyword similarity (text-based)
        keyword_sim = SimilarityCalculator.keyword_similarity(query, tool_name, tool_description)

        # 3. Name matching bonus
        name_match = SimilarityCalculator.name_match_similarity(query, tool_name)

        # Weighted combination using configurable weights
        final_score = (
            SimilarityConfig.SEMANTIC_WEIGHT * semantic_sim +
            SimilarityConfig.KEYWORD_WEIGHT * keyword_sim +
            SimilarityConfig.NAME_MATCH_WEIGHT * name_match
        )

        return min(final_score, 1.0)  # Ensure score doesn't exceed 1.0

    @staticmethod
    def get_similarity_breakdown(query: str, tool_name: str, tool_description: str,
                               query_vector: List[float], tool_vector: List[float]) -> dict:
        """Get detailed breakdown of similarity scores.

        Args:
            query: The search query.
            tool_name: Name of the tool.
            tool_description: Description of the tool.
            query_vector: Query vector representation.
            tool_vector: Tool vector representation.

        Returns:
            Dictionary with detailed similarity breakdown.
        """
        semantic_score = SimilarityCalculator.cosine_similarity(query_vector, tool_vector)
        keyword_score = SimilarityCalculator.keyword_similarity(query, tool_name, tool_description)
        name_match_score = SimilarityCalculator.name_match_similarity(query, tool_name)
        
        hybrid_score = SimilarityCalculator.hybrid_similarity(
            query, tool_name, tool_description, query_vector, tool_vector
        )

        return {
            "semantic_score": semantic_score,
            "keyword_score": keyword_score,
            "name_match_score": name_match_score,
            "hybrid_score": hybrid_score,
            "weights": {
                "semantic": SimilarityConfig.SEMANTIC_WEIGHT,
                "keyword": SimilarityConfig.KEYWORD_WEIGHT,
                "name_match": SimilarityConfig.NAME_MATCH_WEIGHT
            }
        }