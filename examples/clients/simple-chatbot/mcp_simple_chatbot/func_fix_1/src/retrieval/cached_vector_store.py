"""Cached vector store for tool retrieval."""

import logging
from typing import Any, Dict, List

from ..cache.tool_vector_manager import ToolVectorManager
from .similarity import SimilarityCalculator


class CachedVectorStore:
    """A vector store that uses pre-computed cached vectors."""

    def __init__(self, tool_vector_manager: ToolVectorManager) -> None:
        """Initialize with tool vector manager.

        Args:
            tool_vector_manager: Manager for tool vector operations.
        """
        self.tool_vector_manager = tool_vector_manager
        self.tools_data: Dict[str, Dict[str, Any]] = {}
        self.tool_vectors: Dict[str, List[float]] = {}

    async def add_tool(self, tool_id: str, tool_name: str, tool_description: str) -> None:
        """Add a tool to the vector store.

        Args:
            tool_id: Unique identifier for the tool.
            tool_name: Name of the tool.
            tool_description: Description of the tool.
        """
        # Get cached or generate vector
        vector = await self.tool_vector_manager.get_tool_vector(tool_name, tool_description)

        # Store tool data and vector
        self.tools_data[tool_id] = {
            "name": tool_name,
            "description": tool_description,
            "full_description": f"{tool_name}: {tool_description}"
        }
        self.tool_vectors[tool_id] = vector

    async def search_similar_tools(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """Search for tools similar to the query using hybrid similarity.

        Args:
            query: The search query.
            top_k: Number of top results to return.

        Returns:
            List of similar tools with similarity scores and details.
        """
        if not self.tool_vectors:
            logging.warning("No tools available in vector store")
            return []

        # Generate vector for query
        query_vector = await self.tool_vector_manager.embeddings_model.aembed_query(query)

        # Calculate hybrid similarities
        similarities = []
        for tool_id, tool_vector in self.tool_vectors.items():
            tool_data = self.tools_data[tool_id]
            tool_name = tool_data["name"]
            tool_description = tool_data["description"]

            # Get detailed similarity breakdown
            similarity_breakdown = SimilarityCalculator.get_similarity_breakdown(
                query, tool_name, tool_description, query_vector, tool_vector
            )

            similarities.append({
                "tool_id": tool_id,
                "similarity": similarity_breakdown["hybrid_score"],
                "tool_data": tool_data,
                "match_details": {
                    "semantic_score": similarity_breakdown["semantic_score"],
                    "keyword_score": similarity_breakdown["keyword_score"],
                    "name_match_score": similarity_breakdown["name_match_score"]
                },
                "selection_method": "hybrid_similarity"
            })

        # Sort by similarity and return top_k
        similarities.sort(key=lambda x: x["similarity"], reverse=True)
        return similarities[:top_k]

    async def search_by_semantic_only(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """Search using only semantic similarity.

        Args:
            query: The search query.
            top_k: Number of top results to return.

        Returns:
            List of similar tools using semantic similarity only.
        """
        if not self.tool_vectors:
            return []

        query_vector = await self.tool_vector_manager.embeddings_model.aembed_query(query)

        similarities = []
        for tool_id, tool_vector in self.tool_vectors.items():
            tool_data = self.tools_data[tool_id]
            
            semantic_score = SimilarityCalculator.cosine_similarity(query_vector, tool_vector)

            similarities.append({
                "tool_id": tool_id,
                "similarity": semantic_score,
                "tool_data": tool_data,
                "match_details": {
                    "semantic_score": semantic_score,
                    "keyword_score": 0.0,
                    "name_match_score": 0.0
                },
                "selection_method": "semantic_only"
            })

        similarities.sort(key=lambda x: x["similarity"], reverse=True)
        return similarities[:top_k]

    def get_tool_count(self) -> int:
        """Get the number of tools in the store.

        Returns:
            Number of tools.
        """
        return len(self.tools_data)

    def get_tool_info(self, tool_id: str) -> Dict[str, Any]:
        """Get information about a specific tool.

        Args:
            tool_id: The tool identifier.

        Returns:
            Tool information dictionary.

        Raises:
            KeyError: If tool_id is not found.
        """
        if tool_id not in self.tools_data:
            raise KeyError(f"Tool {tool_id} not found in vector store")
        
        return {
            "tool_data": self.tools_data[tool_id],
            "has_vector": tool_id in self.tool_vectors
        }

    def list_tools(self) -> List[Dict[str, Any]]:
        """List all tools in the store.

        Returns:
            List of all tool information.
        """
        return [
            {
                "tool_id": tool_id,
                "name": data["name"],
                "description": data["description"]
            }
            for tool_id, data in self.tools_data.items()
        ]