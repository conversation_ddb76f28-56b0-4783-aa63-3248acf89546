"""Custom tool selector for MCP tool retrieval."""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional

from .cached_vector_store import CachedVectorStore

# Import rerank functionality
try:
    from ..aliyun.aliyun_enhanced_retrieval import AliyunTextReranker
    RERANK_AVAILABLE = True
except ImportError:
    RERANK_AVAILABLE = False


class CustomToolSelector:
    """Custom tool selector that uses cached vectors for tool selection."""

    def __init__(self, cached_store: CachedVectorStore, tool_registry: Dict[str, Any],
                 text_reranker: Optional[Any] = None) -> None:
        """Initialize with cached store and tool registry.

        Args:
            cached_store: The cached vector store instance.
            tool_registry: Registry mapping tool IDs to tool instances.
            text_reranker: Optional text reranker for improving results.
        """
        self.cached_store = cached_store
        self.tool_registry = tool_registry
        self.text_reranker = text_reranker
        self.rerank_enabled = text_reranker is not None and RERANK_AVA<PERSON><PERSON>LE

        if self.rerank_enabled:
            logging.info("🚀 CustomToolSelector initialized with rerank support")
        else:
            logging.info("📝 CustomToolSelector initialized without rerank")

    async def select_and_execute_tools(self, user_query: str) -> str:
        """Select and execute tools based on user query.

        Args:
            user_query: The user's query string

        Returns:
            Formatted response with tool selection results
        """
        try:
            # Search for similar tools
            similar_tools = await self.cached_store.search_similar_tools(user_query, top_k=10)

            if not similar_tools:
                return "❌ 未找到相关工具"

            # Apply rerank if available
            if self.rerank_enabled and similar_tools:
                try:
                    logging.info(f"🚀 Applying rerank to {len(similar_tools)} tools")

                    # Prepare documents for reranking
                    documents = [
                        f"{tool_info['tool_data']['name']}: {tool_info['tool_data']['description']}"
                        for tool_info in similar_tools
                    ]

                    # Call rerank
                    rerank_results = await self.text_reranker.rerank_documents(
                        user_query, documents, top_n=len(documents)
                    )

                    # Update similarity scores with rerank scores
                    for rerank_result in rerank_results:
                        if rerank_result.index < len(similar_tools):
                            original_similarity = similar_tools[rerank_result.index]["similarity"]
                            similar_tools[rerank_result.index]["similarity"] = rerank_result.relevance_score
                            similar_tools[rerank_result.index]["match_details"]["rerank_score"] = rerank_result.relevance_score
                            similar_tools[rerank_result.index]["match_details"]["original_similarity"] = original_similarity
                            similar_tools[rerank_result.index]["reranked"] = True

                    # Re-sort by new similarity scores
                    similar_tools.sort(key=lambda x: x["similarity"], reverse=True)
                    logging.info(f"🚀 Rerank completed successfully")

                except Exception as e:
                    logging.warning(f"Rerank failed, using original results: {e}")

            # Format response with tool information
            response_parts = []
            rerank_indicator = "🚀" if self.rerank_enabled else ""
            response_parts.append(f"🔍{rerank_indicator} 为查询 '{user_query}' 找到 {len(similar_tools)} 个相关工具:\n")

            for i, tool_info in enumerate(similar_tools, 1):
                tool_data = tool_info["tool_data"]
                similarity = tool_info["similarity"]
                match_details = tool_info.get("match_details", {})
                is_reranked = tool_info.get("reranked", False)

                # Format similarity score as percentage
                similarity_pct = similarity * 100

                # Add rerank indicator
                rerank_emoji = "🚀" if is_reranked else ""
                response_parts.append(f"{i}. {rerank_emoji}**{tool_data['name']}** (相似度: {similarity_pct:.1f}%)")
                response_parts.append(f"   📝 描述: {tool_data['description']}")

                # Add match breakdown if available
                if match_details:
                    if is_reranked and "original_similarity" in match_details:
                        original_sim = match_details.get('original_similarity', 0) * 100
                        rerank_score = match_details.get('rerank_score', 0) * 100
                        response_parts.append(
                            f"   📊 重排序: 原始={original_sim:.1f}% → 重排序={rerank_score:.1f}%"
                        )
                    else:
                        semantic = match_details.get('semantic_score', 0) * 100
                        keyword = match_details.get('keyword_score', 0) * 100
                        name_match = match_details.get('name_match_score', 0) * 100
                        response_parts.append(
                            f"   📊 详细匹配: 语义={semantic:.1f}% | 关键词={keyword:.1f}% | 名称={name_match:.1f}%"
                        )

                response_parts.append("")  # Empty line for spacing

            return "\n".join(response_parts)

        except Exception as e:
            logging.error(f"Error in tool selection: {e}")
            return f"❌ 工具选择过程中出错: {str(e)}"

    async def select_tools(self, query: str, max_tools: int = 5, 
                          method: str = "hybrid") -> List[Any]:
        """Select relevant tools based on query using cached vectors.

        Args:
            query: The search query.
            max_tools: Maximum number of tools to select.
            method: Selection method ("hybrid", "semantic_only").

        Returns:
            List of selected tool objects.
        """
        # Search for similar tools
        if method == "semantic_only":
            similar_tools = await self.cached_store.search_by_semantic_only(query, max_tools)
        else:
            similar_tools = await self.cached_store.search_similar_tools(query, max_tools)

        # Return the actual tool objects
        selected_tools = []
        for tool_info in similar_tools:
            tool_id = tool_info["tool_id"]
            if tool_id in self.tool_registry:
                selected_tools.append(self.tool_registry[tool_id])
            else:
                logging.warning(f"Tool {tool_id} not found in registry")

        logging.info(f"Selected {len(selected_tools)} tools using {method} method")
        return selected_tools

    async def get_tool_similarities(self, query: str, max_tools: int = 10) -> List[Dict[str, Any]]:
        """Get detailed similarity information for tools.

        Args:
            query: The search query.
            max_tools: Maximum number of tools to analyze.

        Returns:
            List of tools with detailed similarity information.
        """
        return await self.cached_store.search_similar_tools(query, max_tools)

    async def get_tools_json_format(self, query: str, max_tools: int = 10, 
                                   original_tools_map: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Get tools in the required JSON format for API responses.

        Args:
            query: The search query.
            max_tools: Maximum number of tools to return.
            original_tools_map: Mapping from tool_id to original tool objects.

        Returns:
            List of tools in the required JSON format with name, description, and parameters.
        """
        # Search for similar tools
        similar_tools = await self.cached_store.search_similar_tools(query, max_tools)
        
        # Format tools in the required JSON structure
        formatted_tools = []
        for tool_info in similar_tools:
            tool_data = tool_info["tool_data"]
            tool_id = tool_info["tool_id"]
            
            # Get parameters from original tool if available
            parameters = {}
            if original_tools_map and tool_id in original_tools_map:
                original_tool = original_tools_map[tool_id]
                parameters = getattr(original_tool, 'input_schema', {})
            
            # Create the required format
            formatted_tool = {
                "name": tool_data["name"],
                "description": tool_data["description"],
                "parameters": parameters
            }
            
            formatted_tools.append(formatted_tool)
        
        return formatted_tools

    def get_available_tools_count(self) -> int:
        """Get the number of available tools.

        Returns:
            Number of tools in the registry.
        """
        return len(self.tool_registry)

    def get_cached_tools_count(self) -> int:
        """Get the number of cached tools.

        Returns:
            Number of tools in the cached store.
        """
        return self.cached_store.get_tool_count()

    async def search_tools_by_name(self, name_pattern: str) -> List[Dict[str, Any]]:
        """Search tools by name pattern.

        Args:
            name_pattern: Pattern to match in tool names.

        Returns:
            List of matching tools.
        """
        all_tools = self.cached_store.list_tools()
        matching_tools = []
        
        name_pattern_lower = name_pattern.lower()
        for tool in all_tools:
            if name_pattern_lower in tool["name"].lower():
                matching_tools.append(tool)
        
        return matching_tools

    def validate_setup(self) -> Dict[str, Any]:
        """Validate the tool selector setup.

        Returns:
            Dictionary with validation results.
        """
        registry_count = self.get_available_tools_count()
        cached_count = self.get_cached_tools_count()
        
        validation_result = {
            "status": "ok" if registry_count > 0 and cached_count > 0 else "error",
            "registry_tools": registry_count,
            "cached_tools": cached_count,
            "tools_match": registry_count == cached_count,
            "issues": []
        }
        
        if registry_count == 0:
            validation_result["issues"].append("No tools in registry")
        
        if cached_count == 0:
            validation_result["issues"].append("No tools in cache")
        
        if registry_count != cached_count:
            validation_result["issues"].append(
                f"Mismatch between registry ({registry_count}) and cache ({cached_count})"
            )
        
        return validation_result