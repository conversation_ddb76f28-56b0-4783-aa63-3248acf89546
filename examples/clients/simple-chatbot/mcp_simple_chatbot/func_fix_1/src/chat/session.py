"""Chat session management for MCP tool retrieval system."""

import asyncio
import json
import logging
import uuid
from typing import Any, Dict, List, Optional, Tuple

from langchain.chat_models import init_chat_model
from langchain_core.messages import AIMessage, HumanMessage
from langchain_core.tools import StructuredTool
from langgraph.graph import MessagesState

from ..cache.vector_cache import VectorCache
from ..cache.tool_vector_manager import ToolVectorManager
from ..core.config import Configuration
from ..core.server import Server
from ..embeddings.factory import EmbeddingsFactory
from ..retrieval.cached_vector_store import CachedVectorStore
from ..retrieval.tool_selector import CustomToolSelector
from .handlers import CommandHandler

# Import optional dependencies
try:
    from ..doubao.multi_tool_query_processor import MultiToolRetriever
    MULTI_TOOL_AVAILABLE = True
except ImportError:
    MULTI_TOOL_AVAILABLE = False

try:
    from ..aliyun.aliyun_enhanced_retrieval import AliyunTextReranker
    RERANK_AVAILABLE = True
except ImportError:
    RERANK_AVAILABLE = False

try:
    from ..common.similarity_config import SimilarityConfig
    SIMILARITY_CONFIG_AVAILABLE = True
except ImportError:
    SIMILARITY_CONFIG_AVAILABLE = False


class ChatSession:
    """Orchestrates the interaction between user, LLM, and tools."""

    def __init__(
        self,
        servers: List[Server],
        max_tools_per_task: int,
        embedding_type: str = "aliyun",
        config: Optional[Configuration] = None
    ) -> None:
        """Initialize chat session.

        Args:
            servers: List of MCP servers
            max_tools_per_task: Maximum number of tools per task
            embedding_type: Type of embedding service to use
            config: Configuration object (optional)
        """
        self.servers: List[Server] = servers
        self.max_tools_per_task = max_tools_per_task
        self.embedding_type = embedding_type
        self.config = config or Configuration()

        # Initialize vector cache based on embedding type
        cache_dir_map = {
            "aliyun": ".vector_cache_aliyun",
            "openai": ".vector_cache_openai",
            "volcengine": ".vector_cache"
        }
        cache_dir = cache_dir_map.get(embedding_type, ".vector_cache")
        self.vector_cache = VectorCache(cache_dir=cache_dir)

        # Initialize tool vector manager
        self.tool_vector_manager: Optional[ToolVectorManager] = None

        # Initialize all_tools to store loaded tools
        self.all_tools: List[Any] = []

        # Initialize system components (will be set after initialization)
        self.cached_store: Optional[CachedVectorStore] = None
        self.tool_registry: Dict[str, Any] = {}
        self.original_tools_map: Dict[str, Any] = {}
        self.tool_selector: Optional[CustomToolSelector] = None
        self.llm: Optional[Any] = None
        self._initialized: bool = False

        # Initialize command handler
        self.command_handler = CommandHandler(self)

        # Initialize Multi-tool retrieval if available
        if MULTI_TOOL_AVAILABLE:
            self.multi_tool_retriever = None  # Will be initialized after tool loading
            self.multi_tool_mode = True
            logging.info("🔧 Multi-tool Retrieval System available!")
        else:
            self.multi_tool_mode = False
            logging.info("📝 Using standard tool selection")

        # Initialize rerank if available
        if RERANK_AVAILABLE:
            self.text_reranker = None  # Will be initialized after tool loading
            self.rerank_mode = True
            logging.info("🚀 Text Reranker available!")
        else:
            self.rerank_mode = False
            logging.info("📝 No reranker available")

    async def cleanup_servers(self) -> None:
        """Clean up all servers properly."""
        for server in reversed(self.servers):
            try:
                await server.cleanup()
            except Exception as e:
                logging.warning(f"Warning during final cleanup: {e}")

    async def process_llm_response(self, llm_response: str) -> str:
        """Process the LLM response and execute tools if needed.

        Args:
            llm_response: The response from the LLM.

        Returns:
            The result of tool execution or the original response.
        """
        try:
            tool_call = json.loads(llm_response)
            if "tool" in tool_call and "arguments" in tool_call:
                logging.info(f"Executing tool: {tool_call['tool']}")
                logging.info(f"With arguments: {tool_call['arguments']}")

                for server in self.servers:
                    tools = await server.list_tools()
                    if any(tool.name == tool_call["tool"] for tool in tools):
                        try:
                            result = await server.execute_tool(
                                tool_call["tool"], tool_call["arguments"]
                            )

                            if isinstance(result, dict) and "progress" in result:
                                progress = result["progress"]
                                total = result["total"]
                                percentage = (progress / total) * 100
                                logging.info(
                                    f"Progress: {progress}/{total} ({percentage:.1f}%)"
                                )

                            return f"Tool execution result: {result}"
                        except Exception as e:
                            error_msg = f"Error executing tool: {str(e)}"
                            logging.error(error_msg)
                            return error_msg

                return f"No server found with tool: {tool_call['tool']}"
            return llm_response
        except json.JSONDecodeError:
            return llm_response

    async def initialize_servers(self) -> None:
        """Initialize MCP servers only."""
        try:
            # Initialize servers
            for server in self.servers:
                try:
                    await server.initialize()
                except Exception as e:
                    logging.error(f"Failed to initialize server: {e}")
                    await self.cleanup_servers()
                    raise
            logging.info("✅ All MCP servers initialized successfully")
        except Exception as e:
            await self.cleanup_servers()
            raise

    async def initialize_system(self) -> Tuple[List[Any], CachedVectorStore, Dict[str, Any], Dict[str, Any], CustomToolSelector, Any]:
        """Initialize the chat system and return necessary components.

        Note: This assumes servers are already initialized.

        Returns:
            Tuple containing:
            - all_tools: List of all available tools
            - cached_store: Vector store for tool similarity search
            - tool_registry: Registry of LangChain compatible tools
            - original_tools_map: Mapping from tool_id to original tool objects
            - tool_selector: Custom tool selector instance
            - llm: Language model instance
        """
        try:
            # Get all tools from servers (servers should already be initialized)
            all_tools = []
            for server in self.servers:
                tools = await server.list_tools()
                all_tools.extend(tools)

            self.all_tools = all_tools

            # Convert custom Tool objects to LangChain compatible tools
            def create_langchain_tool(custom_tool, servers):
                """Convert custom Tool to LangChain StructuredTool."""
                async def tool_func(**kwargs):
                    # Find the server that has this tool and execute it
                    for server in servers:
                        server_tools = await server.list_tools()
                        if any(t.name == custom_tool.name for t in server_tools):
                            return await server.execute_tool(custom_tool.name, kwargs)
                    raise ValueError(f"Tool {custom_tool.name} not found on any server")

                return StructuredTool.from_function(
                    coroutine=tool_func,  # Use coroutine parameter for async functions
                    name=custom_tool.name,
                    description=custom_tool.description,
                    args_schema=None  # You might want to create a proper schema here
                )

            # Initialize embeddings based on type
            embeddings_config = self.config.get_embedding_config(self.embedding_type)
            api_key = self.config.get_api_key(self.embedding_type)
            
            try:
                embeddings, used_provider = EmbeddingsFactory.create_with_fallback(
                    preferred_provider=self.embedding_type,
                    config=embeddings_config,
                    api_key=api_key,
                    fallback_providers=["openai"]
                )
                logging.info(f"✅ Successfully initialized {used_provider} embeddings")
            except Exception as e:
                logging.error(f"❌ Failed to initialize embeddings: {e}")
                raise

            self.tool_vector_manager = ToolVectorManager(embeddings, self.vector_cache)

            # Create tool registry with LangChain compatible tools and original tools mapping
            tool_registry: Dict[str, Any] = {}
            original_tools_map: Dict[str, Any] = {}  # 映射 tool_id 到原始工具对象
            
            logging.info(f"Processing {len(all_tools)} tools for vector storage...")
            
            for tool in all_tools:
                tool_id = str(uuid.uuid4())
                langchain_tool = create_langchain_tool(tool, self.servers)
                tool_registry[tool_id] = langchain_tool
                original_tools_map[tool_id] = tool  # 保存原始工具对象

            # Create our custom cached vector store instead of InMemoryStore
            cached_store = CachedVectorStore(self.tool_vector_manager)

            # Populate cached store with tools (use cache when available)
            logging.info("Loading tool vectors (using cache when available)...")
            
            # Process tools in batches to reduce memory pressure
            batch_size = 10
            processed_count = 0
            
            for tool_id, tool in tool_registry.items():
                try:
                    tool_name = getattr(tool, 'name', 'Unknown')
                    tool_description = getattr(tool, 'description', 'No description')
                    
                    logging.debug(f"Processing tool: {tool_name}")
                    await cached_store.add_tool(tool_id, tool_name, tool_description)
                    
                    processed_count += 1
                    
                    # Save cache periodically
                    if processed_count % batch_size == 0:
                        self.tool_vector_manager.save_cache()
                        logging.info(f"Processed {processed_count}/{len(all_tools)} tools")
                        
                except Exception as e:
                    logging.error(f"Failed to process tool {getattr(tool, 'name', 'Unknown')}: {e}")
                    continue

            # Save final cache after processing all tools
            self.tool_vector_manager.save_cache()
            logging.info(f"Tool vector processing completed: {processed_count}/{len(all_tools)} tools processed")

            # Initialize Multi-tool retriever if available
            if self.multi_tool_mode and MULTI_TOOL_AVAILABLE:
                try:
                    self.multi_tool_retriever = MultiToolRetriever(
                        cached_store=cached_store,
                        tool_vector_manager=self.tool_vector_manager
                    )
                    logging.info("🔧 Multi-tool Retriever initialized")
                except Exception as e:
                    logging.error(f"Failed to initialize Multi-tool Retriever: {e}")
                    self.multi_tool_mode = False

            # Initialize Text Reranker if available
            if self.rerank_mode and RERANK_AVAILABLE:
                try:
                    rerank_api_key = self.config.get_api_key('aliyun')
                    self.text_reranker = AliyunTextReranker(api_key=rerank_api_key)
                    logging.info("🚀 Text Reranker initialized")
                except Exception as e:
                    logging.error(f"Failed to initialize Text Reranker: {e}")
                    self.rerank_mode = False

            # Create custom tool selector with reranker if available
            tool_selector = CustomToolSelector(
                cached_store,
                tool_registry,
                text_reranker=self.text_reranker if hasattr(self, 'text_reranker') else None
            )

            # Create LLM
            llm = init_chat_model("openai:gpt-4o-mini")

            return all_tools, cached_store, tool_registry, original_tools_map, tool_selector, llm

        except Exception as e:
            await self.cleanup_servers()
            raise

    async def process_query(self, user_query: str) -> str:
        """Process a user query and return the response.

        Args:
            user_query: The user's query string

        Returns:
            The response from the tool selection system
        """
        try:
            # Initialize system components if not already done (servers should already be initialized)
            if not self._initialized:
                logging.info("🔄 Initializing system components for first query...")
                (all_tools, cached_store, tool_registry,
                 original_tools_map, tool_selector, llm) = await self.initialize_system()

                # Save all components to instance variables
                self.all_tools = all_tools
                self.cached_store = cached_store
                self.tool_registry = tool_registry
                self.original_tools_map = original_tools_map
                self.tool_selector = tool_selector
                self.llm = llm
                self._initialized = True

                logging.info("✅ System components initialization completed")

            # Use the tool selector to process the query
            if self.tool_selector:
                return await self.tool_selector.select_and_execute_tools(user_query)
            else:
                return "❌ Tool selector not initialized"

        except Exception as e:
            logging.error(f"Error processing query: {e}")
            return f"❌ Error processing query: {str(e)}"

    async def start(self) -> None:
        """Start the interactive chat session."""
        print("🚀 MCP工具检索系统已启动")
        print("💡 输入查询内容，输入特殊命令或 'quit' 退出")
        
        # Show available commands
        available_commands = self.command_handler.get_available_commands()
        print(f"🔧 可用命令: {', '.join(available_commands)}")
        
        while True:
            try:
                user_input = input("\n🤖 请输入查询: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("👋 再见！")
                    break
                
                # Handle special commands
                if user_input.lower() == 'weights' and SIMILARITY_CONFIG_AVAILABLE:
                    self.command_handler.handle_weights_command()
                    continue
                elif user_input.lower() == 'multi_tool':
                    self.command_handler.handle_multi_tool_command()
                    continue
                elif user_input.lower() == 'clear_cache':
                    self.command_handler.handle_cache_command("clear")
                    continue
                elif user_input.lower() == 'cache_stats':
                    self.command_handler.handle_cache_command("stats")
                    continue
                
                if not user_input:
                    print("❌ 查询内容不能为空")
                    continue
                
                print(f"\n🔍 正在处理: {user_input}")
                start_time = asyncio.get_event_loop().time()
                
                # Process the query
                result = await self.process_query(user_input)
                
                end_time = asyncio.get_event_loop().time()
                processing_time = end_time - start_time
                
                print(f"\n⚡ 处理完成 (耗时: {processing_time:.2f}s)")
                print(f"📋 结果:\n{result}")
                
            except KeyboardInterrupt:
                print("\n👋 用户中断，退出")
                break
            except Exception as e:
                print(f"❌ 处理出错: {e}")
                logging.error(f"Error in chat session: {e}")
        
        # Cleanup
        await self.cleanup_servers()