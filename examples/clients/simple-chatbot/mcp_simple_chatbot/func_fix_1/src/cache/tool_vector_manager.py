"""Tool vector management for generating and caching tool embeddings."""

import logging
from typing import List

from .vector_cache import VectorCache


class ToolVectorManager:
    """Manages tool vector generation and caching."""

    def __init__(self, embeddings_model, vector_cache: VectorCache) -> None:
        """Initialize with embeddings model and cache.

        Args:
            embeddings_model: The embeddings model to use for vector generation.
            vector_cache: Cache instance for storing vectors.
        """
        self.embeddings_model = embeddings_model
        self.vector_cache = vector_cache
        self._embedding_cache = {}

    async def get_tool_vector(self, tool_name: str, tool_description: str) -> List[float]:
        """Get vector for a tool, using cache if available.

        Args:
            tool_name: Name of the tool.
            tool_description: Description of the tool.

        Returns:
            Vector representation of the tool.

        Raises:
            Exception: If vector generation fails.
        """
        # Check cache first
        cached_vector = self.vector_cache.get_vector(tool_name, tool_description)
        if cached_vector is not None:
            return cached_vector

        # Generate new vector
        logging.info(f"Generating vector for tool: {tool_name}")
        description_text = f"{tool_name}: {tool_description}"

        try:
            # Use the embeddings model to generate vector
            vector = await self.embeddings_model.aembed_query(description_text)

            # Cache the result
            self.vector_cache.set_vector(tool_name, tool_description, vector)

            return vector
        except Exception as e:
            logging.error(f"Failed to generate vector for tool {tool_name}: {e}")
            raise

    async def batch_generate_vectors(self, tools_data: List[tuple]) -> dict:
        """Generate vectors for multiple tools in batch.

        Args:
            tools_data: List of tuples containing (tool_name, tool_description).

        Returns:
            Dictionary mapping tool names to vectors.
        """
        results = {}
        failed = []

        for tool_name, tool_description in tools_data:
            try:
                vector = await self.get_tool_vector(tool_name, tool_description)
                results[tool_name] = vector
            except Exception as e:
                logging.error(f"Failed to generate vector for {tool_name}: {e}")
                failed.append(tool_name)

        if failed:
            logging.warning(f"Failed to generate vectors for {len(failed)} tools: {failed}")

        return results

    def save_cache(self) -> None:
        """Save the vector cache to disk."""
        self.vector_cache.save()

    def clear_cache(self) -> None:
        """Clear the vector cache."""
        self.vector_cache.clear()
        self._embedding_cache.clear()

    def get_cache_stats(self) -> dict:
        """Get cache statistics.

        Returns:
            Dictionary with cache statistics.
        """
        return self.vector_cache.get_cache_stats()