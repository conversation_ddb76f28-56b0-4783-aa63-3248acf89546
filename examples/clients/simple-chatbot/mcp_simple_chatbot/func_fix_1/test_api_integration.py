#!/usr/bin/env python3
"""测试API客户端功能"""

import asyncio
import json
from pathlib import Path
import sys

# 添加模块路径
sys.path.append(str(Path(__file__).parent))

from src.core.api_client import ProjectToolsAPIClient
from src.core.config import Configuration


async def test_api_client():
    """测试API客户端"""
    print("🧪 Testing API client...")
    
    try:
        # 创建API客户端
        client = ProjectToolsAPIClient()
        
        # 测试获取工具数据
        print("📡 Fetching tools data from API...")
        tools_data = await client.fetch_tools_data()
        print(f"✅ Fetched {len(tools_data)} tools")
        
        # 显示前3个工具的信息
        print("\n📋 First 3 tools:")
        for i, tool in enumerate(tools_data[:3]):
            print(f"{i+1}. {tool.get('name', 'Unknown')}: {tool.get('descriptionChinese', tool.get('description', 'No description'))}")
        
        # 测试转换功能
        print("\n🔄 Testing transformation...")
        transformed = client.transform_to_tool_format(tools_data)
        print(f"✅ Transformed {len(transformed)} tools")
        
        # 显示转换后的格式
        print("\n📄 Transformed format example:")
        if transformed:
            print(json.dumps(transformed[0], indent=2, ensure_ascii=False))
        
        # 测试生成mock配置
        print("\n🎭 Testing mock config generation...")
        mock_config = client.generate_mock_server_config(transformed)
        print(f"✅ Generated mock config with {len(mock_config['mcpServers'])} servers")
        
        return True
        
    except Exception as e:
        print(f"❌ API test failed: {e}")
        return False


async def test_configuration():
    """测试配置类"""
    print("\n🧪 Testing Configuration class...")
    
    try:
        config = Configuration()
        
        # 测试从API加载配置
        print("📡 Loading config from API...")
        server_config = await config.load_config_from_api()
        print(f"✅ Loaded config with {len(server_config['mcpServers'])} servers")
        
        # 测试获取工具列表
        print("📋 Getting tools list...")
        tools_response = await config.get_tools_from_api()
        print(f"✅ Got {len(tools_response['tools'])} tools")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 Starting API integration tests...")
    
    api_test_passed = await test_api_client()
    config_test_passed = await test_configuration()
    
    if api_test_passed and config_test_passed:
        print("\n✅ All tests passed!")
        return 0
    else:
        print("\n❌ Some tests failed!")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)