#!/usr/bin/env python3
"""
MCP工具检索API服务器
提供HTTP接口进行工具检索，支持先初始化后查询的模式
"""

import asyncio
import json
import logging
import time
from contextlib import asynccontextmanager
from typing import Dict, Any, Optional
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import uvicorn

# 导入主模块
from main import (
    initialize_global_session,
    query_tools_fast,
    cleanup_global_session,
    get_global_session,
    is_global_session_initialized,
    get_global_session_tools_count
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)

# 全局状态
_server_start_time = time.time()
_current_embedding_type = None
_auto_init_embedding = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global _current_embedding_type, _auto_init_embedding

    # 启动时执行
    logging.info("🚀 MCP工具检索API服务器启动中...")

    # 如果启用了自动初始化
    if _auto_init_embedding:
        logging.info(f"⚡ 开始自动初始化 (embedding: {_auto_init_embedding})")
        try:
            await initialize_global_session(embedding_type=_auto_init_embedding)
            _current_embedding_type = _auto_init_embedding

            # 检查初始化结果
            if is_global_session_initialized():
                tools_count = get_global_session_tools_count()
                logging.info(f"✅ 自动初始化完成，加载了 {tools_count} 个工具")
            else:
                logging.error("❌ 自动初始化后全局session仍为None")

        except Exception as e:
            logging.error(f"❌ 自动初始化失败: {e}")

    yield

    # 关闭时执行
    logging.info("🛑 正在关闭MCP工具检索API服务器...")
    await cleanup_global_session()
    logging.info("✅ 服务器已关闭")

# 创建FastAPI应用
app = FastAPI(
    title="MCP工具检索API",
    description="基于MCP的工具检索服务，支持多种embedding模型",
    version="1.0.0",
    lifespan=lifespan
)

# 请求模型
class QueryRequest(BaseModel):
    query: str
    max_results: Optional[int] = 10

class InitRequest(BaseModel):
    embedding_type: str = "aliyun"
    config_file: str = "servers_config.json"

# 响应模型
class QueryResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    processing_time: float
    timestamp: float

class StatusResponse(BaseModel):
    initialized: bool
    embedding_type: Optional[str] = None
    tools_count: Optional[int] = None
    uptime: Optional[float] = None



@app.get("/", response_model=Dict[str, str])
async def root():
    """根路径，返回API信息"""
    return {
        "name": "MCP工具检索API",
        "version": "1.0.0",
        "status": "running",
        "docs": "/docs"
    }

@app.get("/status", response_model=StatusResponse)
async def get_status():
    """获取服务器状态"""
    global _current_embedding_type, _server_start_time

    initialized = is_global_session_initialized()
    tools_count = get_global_session_tools_count() if initialized else None
    uptime = time.time() - _server_start_time

    return StatusResponse(
        initialized=initialized,
        embedding_type=_current_embedding_type,
        tools_count=tools_count,
        uptime=uptime
    )

@app.post("/initialize", response_model=QueryResponse)
async def initialize_system(request: InitRequest):
    """初始化MCP系统"""
    global _current_embedding_type
    
    start_time = time.time()
    
    try:
        logging.info(f"🔧 开始初始化系统 (embedding: {request.embedding_type})")
        
        await initialize_global_session(
            embedding_type=request.embedding_type,
            config_file=request.config_file
        )

        _current_embedding_type = request.embedding_type

        processing_time = time.time() - start_time

        # 获取工具数量
        tools_count = get_global_session_tools_count()

        if is_global_session_initialized():
            logging.info(f"✅ 系统初始化完成，加载了 {tools_count} 个工具")
        else:
            logging.error("❌ 全局session初始化后仍为None")
        
        return QueryResponse(
            success=True,
            message=f"系统初始化成功，加载了 {tools_count} 个工具",
            data={
                "embedding_type": request.embedding_type,
                "tools_count": tools_count,
                "config_file": request.config_file
            },
            processing_time=processing_time,
            timestamp=time.time()
        )
        
    except Exception as e:
        processing_time = time.time() - start_time
        error_msg = f"系统初始化失败: {str(e)}"
        logging.error(error_msg)
        
        return QueryResponse(
            success=False,
            message=error_msg,
            processing_time=processing_time,
            timestamp=time.time()
        )

@app.post("/query", response_model=QueryResponse)
async def query_tools_api(request: QueryRequest):
    """查询工具接口"""
    start_time = time.time()
    
    try:
        # 检查系统是否已初始化
        if not is_global_session_initialized():
            raise HTTPException(
                status_code=400,
                detail="系统未初始化，请先调用 /initialize 接口"
            )
        
        if not request.query.strip():
            raise HTTPException(
                status_code=400,
                detail="查询内容不能为空"
            )
        
        logging.info(f"🔍 处理查询: {request.query}")
        
        # 执行快速查询
        result = await query_tools_fast(request.query)
        
        processing_time = time.time() - start_time
        
        logging.info(f"⚡ 查询完成，耗时: {processing_time:.2f}s")
        
        return QueryResponse(
            success=True,
            message="查询成功",
            data={
                "query": request.query,
                "result": result,
                "max_results": request.max_results
            },
            processing_time=processing_time,
            timestamp=time.time()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        processing_time = time.time() - start_time
        error_msg = f"查询失败: {str(e)}"
        logging.error(error_msg)
        
        return QueryResponse(
            success=False,
            message=error_msg,
            processing_time=processing_time,
            timestamp=time.time()
        )

@app.post("/cleanup", response_model=QueryResponse)
async def cleanup_system():
    """清理系统资源"""
    global _current_embedding_type
    
    start_time = time.time()
    
    try:
        logging.info("🧹 开始清理系统资源")
        
        await cleanup_global_session()
        _current_embedding_type = None
        
        processing_time = time.time() - start_time
        
        logging.info("✅ 系统资源清理完成")
        
        return QueryResponse(
            success=True,
            message="系统资源清理完成",
            processing_time=processing_time,
            timestamp=time.time()
        )
        
    except Exception as e:
        processing_time = time.time() - start_time
        error_msg = f"清理失败: {str(e)}"
        logging.error(error_msg)
        
        return QueryResponse(
            success=False,
            message=error_msg,
            processing_time=processing_time,
            timestamp=time.time()
        )

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy", "timestamp": time.time()}

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="MCP工具检索API服务器")
    parser.add_argument("--host", default="0.0.0.0", help="服务器主机地址")
    parser.add_argument("--port", type=int, default=8000, help="服务器端口")
    parser.add_argument("--embedding", default="aliyun", help="默认embedding类型")
    parser.add_argument("--auto-init", action="store_true", help="启动时自动初始化")

    args = parser.parse_args()

    print(f"🚀 启动MCP工具检索API服务器")
    print(f"📍 地址: http://{args.host}:{args.port}")
    print(f"📚 文档: http://{args.host}:{args.port}/docs")
    print(f"🔧 默认embedding: {args.embedding}")

    if args.auto_init:
        print(f"⚡ 自动初始化模式已启用")
        _auto_init_embedding = args.embedding

    uvicorn.run(
        app,
        host=args.host,
        port=args.port,
        log_level="info"
    )
