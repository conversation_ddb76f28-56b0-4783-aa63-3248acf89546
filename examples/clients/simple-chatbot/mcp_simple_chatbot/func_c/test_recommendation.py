#!/usr/bin/env python3
"""
测试推荐和搜索功能集成
Test script for recommendation and search functionality integration
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from main import ChatSession, Configuration, Server

async def test_recommendation_integration():
    """测试推荐系统集成"""
    print("🧪 测试推荐和搜索功能集成")
    print("=" * 50)
    
    try:
        # 创建配置
        config = Configuration(enable_recommendation=True)
        
        # 创建空的服务器列表（用于测试）
        servers = []
        
        # 创建ChatSession实例，启用推荐功能
        chat_session = ChatSession(
            servers=servers,
            max_tools_per_task=5,
            embedding_type="aliyun",
            enable_recommendation=True,
            aliyun_api_key=config.aliyun_api_key
        )
        
        print("✅ ChatSession创建成功")
        print(f"📊 推荐系统状态: {'启用' if chat_session.enable_recommendation else '禁用'}")
        print(f"🎯 语义系统模式: {'启用' if chat_session.semantic_system_mode else '禁用'}")
        
        # 测试语义系统初始化
        if chat_session.semantic_system_mode:
            print("\n🔄 正在初始化语义系统...")
            
            # 手动初始化语义系统（因为没有工具需要初始化）
            from aliyun.aliyun_semantic_demo import AliyunSemanticSystem
            
            chat_session.semantic_system = AliyunSemanticSystem(
                api_key=chat_session.aliyun_api_key,
                model="text-embedding-v4",
                dimensions=1024
            )
            
            # 加载示例数据
            await chat_session.semantic_system.load_sample_data()
            await chat_session.semantic_system.create_sample_users()
            
            print("✅ 语义系统初始化完成")
            
            # 测试搜索功能
            print("\n🔍 测试语义搜索功能...")
            test_queries = [
                "Python编程",
                "机器学习算法",
                "数据分析方法"
            ]
            
            for query in test_queries:
                print(f"\n🔍 搜索: '{query}'")
                results = await chat_session.semantic_system.search_engine.search(
                    query, top_k=3, min_similarity=0.1
                )
                
                if results:
                    print(f"  找到 {len(results)} 个结果:")
                    for i, result in enumerate(results, 1):
                        print(f"    {i}. 相似度: {result.similarity:.3f}")
                        print(f"       内容: {result.content[:60]}...")
                else:
                    print("  未找到结果")
            
            # 测试推荐功能
            print("\n🎯 测试语义推荐功能...")
            test_users = ["beginner_programmer", "ai_enthusiast"]
            
            for user_id in test_users:
                print(f"\n👤 为用户 '{user_id}' 生成推荐:")
                recommendations = await chat_session.semantic_system.recommendation_engine.recommend_items(
                    user_id=user_id,
                    top_k=3,
                    diversity_factor=0.2
                )
                
                if recommendations:
                    print(f"  推荐 {len(recommendations)} 个课程:")
                    for i, rec in enumerate(recommendations, 1):
                        print(f"    {i}. {rec.item_id} (相似度: {rec.similarity:.3f})")
                        print(f"       {rec.content[:60]}...")
                else:
                    print("  未找到推荐")
            
            # 显示统计信息
            print("\n📊 系统统计信息:")
            await chat_session.semantic_system.print_system_stats()
            
        else:
            print("❌ 语义系统未启用，跳过功能测试")
        
        print("\n✅ 所有测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

async def test_command_line_args():
    """测试命令行参数解析"""
    print("\n🧪 测试命令行参数")
    print("=" * 30)
    
    # 模拟不同的命令行参数
    test_cases = [
        ["--server", "--embedding", "aliyun"],
        ["--server", "--enable-recommendation"],
        ["--server", "--embedding", "doubao", "--enable-recommendation"],
        ["--help"]
    ]
    
    for args in test_cases:
        print(f"\n测试参数: {' '.join(args)}")
        # 这里只是展示参数，实际解析在main函数中
        
        embedding_type = "aliyun"
        enable_recommendation = False
        
        for i, arg in enumerate(args):
            if arg == "--embedding" and i + 1 < len(args):
                embedding_type = args[i + 1]
            elif arg == "--enable-recommendation":
                enable_recommendation = True
        
        print(f"  解析结果: embedding={embedding_type}, recommendation={enable_recommendation}")

if __name__ == "__main__":
    print("🚀 开始测试推荐和搜索功能集成")
    
    # 运行测试
    asyncio.run(test_recommendation_integration())
    
    # 测试命令行参数
    asyncio.run(test_command_line_args())
    
    print("\n🎉 测试完成！")
