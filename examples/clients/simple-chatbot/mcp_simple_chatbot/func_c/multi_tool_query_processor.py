"""
多工具查询处理器
解决复杂查询中多个工具需求识别不全的问题
"""

import asyncio
import logging
import time
import re
from typing import List, Dict, Any, Optional, Tuple, Set
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

# 导出主要类和函数供接口使用
__all__ = [
    'QueryComplexity',
    'SubTask',
    'ToolMatch',
    'MultiToolResult',
    'QueryDecomposer',
    'MultiToolRetriever',
    'create_multi_tool_interface'
]


class QueryComplexity(Enum):
    """查询复杂度"""
    SIMPLE = "simple"           # 单一工具需求
    COMPOUND = "compound"       # 多个独立工具需求
    SEQUENTIAL = "sequential"   # 有依赖关系的工具序列
    PARALLEL = "parallel"       # 可并行执行的工具组合


@dataclass
class SubTask:
    """子任务"""
    task_id: str
    description: str
    keywords: List[str]
    action_type: str  # query, create, update, delete, analyze, etc.
    entities: List[str]
    priority: int = 1
    dependencies: List[str] = None
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []


@dataclass
class ToolMatch:
    """工具匹配结果"""
    tool_id: str
    tool_name: str
    description: str
    confidence: float
    match_reason: str
    subtask_id: str
    similarity_score: float
    keyword_matches: List[str]


@dataclass
class MultiToolResult:
    """多工具查询结果"""
    original_query: str
    complexity: QueryComplexity
    subtasks: List[SubTask]
    tool_matches: List[ToolMatch]
    execution_strategy: str
    processing_time: float
    confidence: float


class QueryDecomposer:
    """查询分解器"""
    
    def __init__(self):
        # 动作关键词映射
        self.action_keywords = {
            'query': ['查询', '查找', '搜索', '获取', '检索', 'search', 'find', 'get', 'retrieve', 'query'],
            'create': ['创建', '新建', '添加', '生成', 'create', 'add', 'generate', 'make', 'new'],
            'update': ['更新', '修改', '编辑', '改变', 'update', 'modify', 'edit', 'change'],
            'delete': ['删除', '移除', '清除', 'delete', 'remove', 'clear'],
            'analyze': ['分析', '统计', '计算', '评估', 'analyze', 'calculate', 'evaluate', 'assess'],
            'capture': ['截图', '抓取', '捕获', '获取', 'screenshot', 'capture', 'grab', 'fetch'],
            'control': ['控制', '设置', '调节', '管理', 'control', 'set', 'adjust', 'manage'],
            'notify': ['通知', '提醒', '告知', 'notify', 'remind', 'alert'],
            'translate': ['翻译', '转换', 'translate', 'convert'],
            'play': ['播放', '执行', '运行', 'play', 'execute', 'run']
        }
        
        # 实体识别模式
        self.entity_patterns = {
            'payment': r'(支付宝|微信支付|银行卡|订单|交易)',
            'web': r'(网站|网页|页面|URL|链接)',
            'location': r'(地址|位置|地点|城市)',
            'time': r'(时间|日期|今天|明天|小时|分钟)',
            'file': r'(文件|图片|视频|文档|PDF)',
            'weather': r'(天气|温度|降雨|风速)',
            'transport': r'(高铁|航班|公交|地铁|火车)',
            'audio': r'(音频|声音|音乐|语音)',
        }
        
        # 分隔符模式
        self.separator_patterns = [
            r'[，,；;]',  # 逗号分号
            r'然后|接着|之后|再|同时|并且|以及',  # 连接词
            r'对.*?进行',  # 对...进行...
            r'执行.*?的',  # 执行...的...
        ]
    
    async def decompose_query(self, query: str) -> Tuple[QueryComplexity, List[SubTask]]:
        """分解查询为子任务"""
        logger.info(f"开始分解查询: '{query}'")
        
        # 1. 预处理查询
        cleaned_query = self._preprocess_query(query)
        
        # 2. 检测查询复杂度
        complexity = self._detect_complexity(cleaned_query)
        logger.info(f"检测到查询复杂度: {complexity.value}")
        
        # 3. 根据复杂度分解
        if complexity == QueryComplexity.SIMPLE:
            subtasks = [self._create_single_task(cleaned_query)]
        else:
            subtasks = await self._decompose_complex_query(cleaned_query, complexity)
        
        logger.info(f"分解完成，共生成 {len(subtasks)} 个子任务")
        for i, task in enumerate(subtasks):
            logger.debug(f"子任务 {i+1}: {task.description} (动作: {task.action_type})")
        
        return complexity, subtasks
    
    def _preprocess_query(self, query: str) -> str:
        """预处理查询文本"""
        # 移除多余空格
        query = re.sub(r'\s+', ' ', query.strip())
        
        # 标准化标点符号
        query = query.replace('，', ',').replace('；', ';')
        
        return query
    
    def _detect_complexity(self, query: str) -> QueryComplexity:
        """检测查询复杂度"""
        # 计算动作词数量
        action_count = 0
        for action_type, keywords in self.action_keywords.items():
            for keyword in keywords:
                if keyword in query:
                    action_count += 1
                    break
        
        # 检测分隔符
        separator_count = 0
        for pattern in self.separator_patterns:
            if re.search(pattern, query):
                separator_count += 1
        
        # 检测连接词
        sequential_indicators = ['然后', '接着', '之后', '再']
        parallel_indicators = ['同时', '并且', '以及', '和']
        
        has_sequential = any(indicator in query for indicator in sequential_indicators)
        has_parallel = any(indicator in query for indicator in parallel_indicators)
        
        # 判断复杂度
        if action_count <= 1 and separator_count == 0:
            return QueryComplexity.SIMPLE
        elif has_sequential:
            return QueryComplexity.SEQUENTIAL
        elif has_parallel:
            return QueryComplexity.PARALLEL
        else:
            return QueryComplexity.COMPOUND
    
    def _create_single_task(self, query: str) -> SubTask:
        """创建单一任务"""
        action_type = self._identify_action_type(query)
        keywords = self._extract_keywords(query)
        entities = self._extract_entities(query)
        
        return SubTask(
            task_id="task_1",
            description=query,
            keywords=keywords,
            action_type=action_type,
            entities=entities,
            priority=1
        )
    
    async def _decompose_complex_query(self, query: str, complexity: QueryComplexity) -> List[SubTask]:
        """分解复杂查询"""
        # 1. 按分隔符分割
        segments = self._split_by_separators(query)
        
        # 2. 为每个片段创建子任务
        subtasks = []
        for i, segment in enumerate(segments):
            if segment.strip():
                action_type = self._identify_action_type(segment)
                keywords = self._extract_keywords(segment)
                entities = self._extract_entities(segment)
                
                subtask = SubTask(
                    task_id=f"task_{i+1}",
                    description=segment.strip(),
                    keywords=keywords,
                    action_type=action_type,
                    entities=entities,
                    priority=i+1
                )
                
                # 为顺序任务添加依赖关系
                if complexity == QueryComplexity.SEQUENTIAL and i > 0:
                    subtask.dependencies = [f"task_{i}"]
                
                subtasks.append(subtask)
        
        return subtasks
    
    def _split_by_separators(self, query: str) -> List[str]:
        """按分隔符分割查询"""
        segments = [query]
        
        for pattern in self.separator_patterns:
            new_segments = []
            for segment in segments:
                parts = re.split(pattern, segment)
                new_segments.extend(parts)
            segments = new_segments
        
        # 过滤空片段
        return [seg.strip() for seg in segments if seg.strip()]
    
    def _identify_action_type(self, text: str) -> str:
        """识别动作类型"""
        for action_type, keywords in self.action_keywords.items():
            for keyword in keywords:
                if keyword in text:
                    return action_type
        return 'general'
    
    def _extract_keywords(self, text: str) -> List[str]:
        """提取关键词"""
        # 简单的关键词提取（可以用更复杂的NLP方法）
        words = re.findall(r'\w+', text)
        # 过滤停用词和短词
        stop_words = {'的', '了', '在', '是', '和', '与', '或', '但', '而', 'the', 'a', 'an', 'and', 'or', 'but'}
        keywords = [word for word in words if len(word) > 1 and word not in stop_words]
        return keywords[:10]  # 限制关键词数量
    
    def _extract_entities(self, text: str) -> List[str]:
        """提取实体"""
        entities = []
        for entity_type, pattern in self.entity_patterns.items():
            matches = re.findall(pattern, text)
            entities.extend(matches)
        return list(set(entities))  # 去重


class MultiToolRetriever:
    """多工具检索器"""
    
    def __init__(self, cached_store, tool_vector_manager):
        self.cached_store = cached_store
        self.tool_vector_manager = tool_vector_manager
        self.query_decomposer = QueryDecomposer()
    
    async def retrieve_multi_tools(self, 
                                  query: str, 
                                  max_tools_per_task: int = 3,
                                  total_max_tools: int = 10) -> MultiToolResult:
        """检索多工具查询结果"""
        start_time = time.time()
        logger.info(f"开始多工具检索: '{query}'")
        
        try:
            # 1. 分解查询
            complexity, subtasks = await self.query_decomposer.decompose_query(query)
            
            # 2. 为每个子任务检索工具
            all_tool_matches = []
            for subtask in subtasks:
                task_matches = await self._retrieve_for_subtask(subtask, max_tools_per_task)
                all_tool_matches.extend(task_matches)
            
            # 3. 去重和排序
            unique_matches = self._deduplicate_tools(all_tool_matches)
            
            # 4. 限制总数量
            final_matches = unique_matches[:total_max_tools]
            
            # 5. 确定执行策略
            execution_strategy = self._determine_execution_strategy(complexity, subtasks)
            
            # 6. 计算整体置信度
            overall_confidence = self._calculate_overall_confidence(final_matches, subtasks)
            
            processing_time = time.time() - start_time
            
            result = MultiToolResult(
                original_query=query,
                complexity=complexity,
                subtasks=subtasks,
                tool_matches=final_matches,
                execution_strategy=execution_strategy,
                processing_time=processing_time,
                confidence=overall_confidence
            )
            
            logger.info(f"多工具检索完成 - 耗时: {processing_time:.2f}s, 返回 {len(final_matches)} 个工具")
            return result
            
        except Exception as e:
            logger.error(f"多工具检索失败: {e}")
            # 返回空结果
            return MultiToolResult(
                original_query=query,
                complexity=QueryComplexity.SIMPLE,
                subtasks=[],
                tool_matches=[],
                execution_strategy="sequential",
                processing_time=time.time() - start_time,
                confidence=0.0
            )
    
    async def _retrieve_for_subtask(self, subtask: SubTask, max_tools: int) -> List[ToolMatch]:
        """为子任务检索工具"""
        logger.debug(f"为子任务检索工具: {subtask.description}")
        
        # 使用子任务描述进行检索
        similar_tools = await self.cached_store.search_similar_tools(
            subtask.description, max_tools * 2  # 获取更多候选
        )
        
        tool_matches = []
        for tool_info in similar_tools[:max_tools]:
            # 计算关键词匹配
            keyword_matches = self._calculate_keyword_matches(
                subtask.keywords, 
                tool_info["tool_data"]["name"], 
                tool_info["tool_data"]["description"]
            )
            
            # 增强置信度计算
            enhanced_confidence = self._enhance_confidence(
                tool_info["similarity"],
                keyword_matches,
                subtask.action_type,
                tool_info["tool_data"]["name"],
                tool_info["tool_data"]["description"]
            )
            
            match = ToolMatch(
                tool_id=tool_info["tool_id"],
                tool_name=tool_info["tool_data"]["name"],
                description=tool_info["tool_data"]["description"],
                confidence=enhanced_confidence,
                match_reason=f"匹配子任务: {subtask.description[:30]}...",
                subtask_id=subtask.task_id,
                similarity_score=tool_info["similarity"],
                keyword_matches=keyword_matches
            )
            tool_matches.append(match)
        
        return tool_matches
    
    def _calculate_keyword_matches(self, task_keywords: List[str], tool_name: str, tool_desc: str) -> List[str]:
        """计算关键词匹配"""
        tool_text = f"{tool_name} {tool_desc}".lower()
        matches = []
        
        for keyword in task_keywords:
            if keyword.lower() in tool_text:
                matches.append(keyword)
        
        return matches
    
    def _enhance_confidence(self, base_similarity: float, keyword_matches: List[str], 
                          action_type: str, tool_name: str, tool_desc: str) -> float:
        """增强置信度计算"""
        # 基础相似度权重
        confidence = base_similarity * 0.6
        
        # 关键词匹配加分
        keyword_bonus = min(len(keyword_matches) * 0.1, 0.3)
        confidence += keyword_bonus
        
        # 动作类型匹配加分
        action_bonus = 0.0
        if action_type in tool_name.lower() or action_type in tool_desc.lower():
            action_bonus = 0.1
        confidence += action_bonus
        
        return min(confidence, 1.0)
    
    def _deduplicate_tools(self, tool_matches: List[ToolMatch]) -> List[ToolMatch]:
        """去重工具"""
        seen_tools = set()
        unique_matches = []
        
        # 按置信度排序
        sorted_matches = sorted(tool_matches, key=lambda x: x.confidence, reverse=True)
        
        for match in sorted_matches:
            if match.tool_id not in seen_tools:
                seen_tools.add(match.tool_id)
                unique_matches.append(match)
        
        return unique_matches
    
    def _determine_execution_strategy(self, complexity: QueryComplexity, subtasks: List[SubTask]) -> str:
        """确定执行策略"""
        if complexity == QueryComplexity.SEQUENTIAL:
            return "sequential"
        elif complexity == QueryComplexity.PARALLEL:
            return "parallel"
        elif len(subtasks) > 1:
            return "batch"
        else:
            return "single"
    
    def _calculate_overall_confidence(self, tool_matches: List[ToolMatch], subtasks: List[SubTask]) -> float:
        """计算整体置信度"""
        if not tool_matches:
            return 0.0
        
        # 计算平均置信度
        avg_confidence = sum(match.confidence for match in tool_matches) / len(tool_matches)
        
        # 根据子任务覆盖率调整
        covered_tasks = set(match.subtask_id for match in tool_matches)
        coverage_ratio = len(covered_tasks) / len(subtasks) if subtasks else 1.0
        
        return avg_confidence * coverage_ratio


def create_multi_tool_interface():
    """
    创建多工具查询处理器的接口函数

    Returns:
        dict: 包含所有接口函数的字典
    """

    # 模拟的缓存存储和向量管理器（在实际使用中需要传入真实的实例）
    class MockCachedStore:
        async def search_similar_tools(self, query: str, max_tools: int):
            # 模拟返回工具搜索结果
            return [
                {
                    "tool_id": f"tool_{i}",
                    "similarity": 0.8 - i * 0.1,
                    "tool_data": {
                        "name": f"示例工具{i+1}",
                        "description": f"这是示例工具{i+1}的描述，用于{query}相关操作"
                    }
                }
                for i in range(min(max_tools, 3))
            ]

    class MockToolVectorManager:
        pass

    # 创建组件实例
    cached_store = MockCachedStore()
    tool_vector_manager = MockToolVectorManager()
    query_decomposer = QueryDecomposer()
    multi_tool_retriever = MultiToolRetriever(cached_store, tool_vector_manager)

    async def decompose_query_interface(query: str) -> dict:
        """
        查询分解接口

        Args:
            query: 用户查询字符串

        Returns:
            dict: 包含分解结果的字典
        """
        try:
            complexity, subtasks = await query_decomposer.decompose_query(query)

            return {
                "success": True,
                "query": query,
                "complexity": complexity.value,
                "subtask_count": len(subtasks),
                "subtasks": [
                    {
                        "task_id": task.task_id,
                        "description": task.description,
                        "action_type": task.action_type,
                        "keywords": task.keywords,
                        "entities": task.entities,
                        "priority": task.priority,
                        "dependencies": task.dependencies
                    }
                    for task in subtasks
                ]
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "query": query
            }

    async def retrieve_multi_tools_interface(
        query: str,
        max_tools_per_task: int = 3,
        total_max_tools: int = 10
    ) -> dict:
        """
        多工具检索接口

        Args:
            query: 用户查询字符串
            max_tools_per_task: 每个子任务最大工具数
            total_max_tools: 总最大工具数

        Returns:
            dict: 包含检索结果的字典
        """
        try:
            result = await multi_tool_retriever.retrieve_multi_tools(
                query, max_tools_per_task, total_max_tools
            )

            return {
                "success": True,
                "original_query": result.original_query,
                "complexity": result.complexity.value,
                "execution_strategy": result.execution_strategy,
                "processing_time": result.processing_time,
                "confidence": result.confidence,
                "subtask_count": len(result.subtasks),
                "tool_count": len(result.tool_matches),
                "subtasks": [
                    {
                        "task_id": task.task_id,
                        "description": task.description,
                        "action_type": task.action_type,
                        "keywords": task.keywords,
                        "entities": task.entities,
                        "priority": task.priority,
                        "dependencies": task.dependencies
                    }
                    for task in result.subtasks
                ],
                "tool_matches": [
                    {
                        "tool_id": match.tool_id,
                        "tool_name": match.tool_name,
                        "description": match.description,
                        "confidence": match.confidence,
                        "match_reason": match.match_reason,
                        "subtask_id": match.subtask_id,
                        "similarity_score": match.similarity_score,
                        "keyword_matches": match.keyword_matches
                    }
                    for match in result.tool_matches
                ]
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "query": query
            }

    def analyze_query_complexity_interface(query: str) -> dict:
        """
        查询复杂度分析接口

        Args:
            query: 用户查询字符串

        Returns:
            dict: 包含复杂度分析结果的字典
        """
        try:
            complexity = query_decomposer._detect_complexity(query)

            # 分析查询特征
            action_count = 0
            detected_actions = []
            for action_type, keywords in query_decomposer.action_keywords.items():
                for keyword in keywords:
                    if keyword in query:
                        action_count += 1
                        detected_actions.append(action_type)
                        break

            # 检测实体
            entities = query_decomposer._extract_entities(query)
            keywords = query_decomposer._extract_keywords(query)

            return {
                "success": True,
                "query": query,
                "complexity": complexity.value,
                "action_count": action_count,
                "detected_actions": list(set(detected_actions)),
                "entities": entities,
                "keywords": keywords,
                "query_length": len(query),
                "word_count": len(query.split()),
                "analysis": {
                    "is_simple": complexity == QueryComplexity.SIMPLE,
                    "is_compound": complexity == QueryComplexity.COMPOUND,
                    "is_sequential": complexity == QueryComplexity.SEQUENTIAL,
                    "is_parallel": complexity == QueryComplexity.PARALLEL,
                    "has_multiple_actions": action_count > 1,
                    "has_entities": len(entities) > 0
                }
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "query": query
            }

    return {
        "decompose_query": decompose_query_interface,
        "retrieve_multi_tools": retrieve_multi_tools_interface,
        "analyze_complexity": analyze_query_complexity_interface,
        "components": {
            "query_decomposer": query_decomposer,
            "multi_tool_retriever": multi_tool_retriever,
            "cached_store": cached_store,
            "tool_vector_manager": tool_vector_manager
        }
    }
