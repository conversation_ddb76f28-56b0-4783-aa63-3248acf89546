import asyncio
import json
import logging
import os
import shutil
import pickle
import hashlib
import sys
from contextlib import AsyncExitStack
from typing import Any, Dict, List, Optional
from pathlib import Path

from dotenv import load_dotenv
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
import uuid
from langchain.chat_models import init_chat_model
from langchain.embeddings import init_embeddings
from doubao.volcengine_embeddings import create_volcengine_embeddings
from aliyun.aliyun_embeddings import create_aliyun_embeddings
try:
    from multi_tool_query_processor import (
        MultiToolRetriever,
        QueryComplexity
    )
    MULTI_TOOL_AVAILABLE = True
except ImportError as e:
    logging.warning(f"Multi-tool retrieval not available: {e}")
    MULTI_TOOL_AVAILABLE = False

# 导入阿里云重排序系统
try:
    from aliyun_enhanced_retrieval import (
        AliyunTextReranker,
        RerankResult
    )
    RERANK_AVAILABLE = True
except ImportError as e:
    logging.warning(f"Aliyun rerank not available: {e}")
    RERANK_AVAILABLE = False

# 导入阿里云语义搜索和推荐系统
try:
    from aliyun.aliyun_semantic_demo import AliyunSemanticSystem
    from aliyun.semantic_search import SemanticSearchEngine, SearchResult
    from aliyun.semantic_recommendation import SemanticRecommendationEngine, RecommendationResult, UserProfile
    SEMANTIC_SYSTEM_AVAILABLE = True
except ImportError as e:
    logging.warning(f"Aliyun semantic system not available: {e}")
    SEMANTIC_SYSTEM_AVAILABLE = False

from langgraph.graph import StateGraph, MessagesState
from langchain_core.messages import HumanMessage, AIMessage

# Import similarity configuration
sys.path.append(str(Path(__file__).parent.parent))
try:
    from similarity_config import SimilarityConfig
except ImportError:
    class SimilarityConfig:
        SEMANTIC_WEIGHT = 0.8
        KEYWORD_WEIGHT = 0.1
        NAME_MATCH_WEIGHT = 0.1
        STOP_WORDS = set()
        MIN_WORD_LENGTH = 2


# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)


class VectorCache:
    """Manages persistent storage of tool vectors to avoid recomputation."""

    def __init__(self, cache_dir: str = ".vector_cache_openai"):
        """Initialize vector cache with specified directory."""
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.cache_file = self.cache_dir / "tool_vectors.pkl"
        self.metadata_file = self.cache_dir / "cache_metadata.json"
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._load_cache()

    def _load_cache(self) -> None:
        """Load existing cache from disk."""
        try:
            if self.cache_file.exists():
                with open(self.cache_file, 'rb') as f:
                    self._cache = pickle.load(f)
                logging.info(f"Loaded {len(self._cache)} cached tool vectors")
            else:
                logging.info("No existing vector cache found, starting fresh")
        except Exception as e:
            logging.warning(f"Failed to load vector cache: {e}, starting fresh")
            self._cache = {}

    def _save_cache(self) -> None:
        """Save current cache to disk."""
        try:
            with open(self.cache_file, 'wb') as f:
                pickle.dump(self._cache, f)

            # Save metadata
            metadata = {
                "last_updated": asyncio.get_event_loop().time(),
                "tool_count": len(self._cache),
                "cache_version": "1.0"
            }
            with open(self.metadata_file, 'w') as f:
                json.dump(metadata, f, indent=2)

            logging.info(f"Saved {len(self._cache)} tool vectors to cache")
        except Exception as e:
            logging.error(f"Failed to save vector cache: {e}")

    def _get_tool_hash(self, tool_name: str, tool_description: str) -> str:
        """Generate a hash for tool identification."""
        content = f"{tool_name}:{tool_description}"
        return hashlib.md5(content.encode()).hexdigest()

    def get_vector(self, tool_name: str, tool_description: str) -> Optional[List[float]]:
        """Get cached vector for a tool."""
        tool_hash = self._get_tool_hash(tool_name, tool_description)
        if tool_hash in self._cache:
            logging.debug(f"Cache hit for tool: {tool_name}")
            return self._cache[tool_hash]["vector"]
        return None

    def set_vector(self, tool_name: str, tool_description: str, vector: List[float]) -> None:
        """Cache a vector for a tool."""
        tool_hash = self._get_tool_hash(tool_name, tool_description)
        self._cache[tool_hash] = {
            "tool_name": tool_name,
            "tool_description": tool_description,
            "vector": vector,
            "cached_at": asyncio.get_event_loop().time()
        }
        logging.debug(f"Cached vector for tool: {tool_name}")

    def save(self) -> None:
        """Public method to save cache."""
        self._save_cache()

    def clear(self) -> None:
        """Clear all cached vectors."""
        self._cache.clear()
        if self.cache_file.exists():
            self.cache_file.unlink()
        if self.metadata_file.exists():
            self.metadata_file.unlink()
        logging.info("Vector cache cleared")


class ToolVectorManager:
    """Manages tool vector generation and caching."""

    def __init__(self, embeddings_model, vector_cache: VectorCache):
        """Initialize with embeddings model and cache."""
        self.embeddings_model = embeddings_model
        self.vector_cache = vector_cache
        self._embedding_cache = {}

    async def get_tool_vector(self, tool_name: str, tool_description: str) -> List[float]:
        """Get vector for a tool, using cache if available."""
        # Check cache first
        cached_vector = self.vector_cache.get_vector(tool_name, tool_description)
        if cached_vector is not None:
            return cached_vector

        # Generate new vector
        logging.info(f"Generating vector for tool: {tool_name}")
        description_text = f"{tool_name}: {tool_description}"

        try:
            # Use the embeddings model to generate vector
            vector = await self.embeddings_model.aembed_query(description_text)

            # Cache the result
            self.vector_cache.set_vector(tool_name, tool_description, vector)

            return vector
        except Exception as e:
            logging.error(f"Failed to generate vector for tool {tool_name}: {e}")
            raise

    def save_cache(self) -> None:
        """Save the vector cache to disk."""
        self.vector_cache.save()


class CachedVectorStore:
    """A vector store that uses pre-computed cached vectors."""

    def __init__(self, tool_vector_manager: ToolVectorManager):
        """Initialize with tool vector manager."""
        self.tool_vector_manager = tool_vector_manager
        self.tools_data: Dict[str, Dict[str, Any]] = {}
        self.tool_vectors: Dict[str, List[float]] = {}

    async def add_tool(self, tool_id: str, tool_name: str, tool_description: str) -> None:
        """Add a tool to the vector store."""
        # Get cached or generate vector
        vector = await self.tool_vector_manager.get_tool_vector(tool_name, tool_description)

        # Store tool data and vector
        self.tools_data[tool_id] = {
            "name": tool_name,
            "description": tool_description,
            "full_description": f"{tool_name}: {tool_description}"
        }
        self.tool_vectors[tool_id] = vector

    def cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """Calculate optimized cosine similarity between two vectors."""
        import numpy as np
        vec1_np = np.array(vec1, dtype=np.float32)
        vec2_np = np.array(vec2, dtype=np.float32)

        dot_product = np.dot(vec1_np, vec2_np)
        norm1 = np.linalg.norm(vec1_np)
        norm2 = np.linalg.norm(vec2_np)

        if norm1 == 0 or norm2 == 0:
            return 0.0

        return float(dot_product / (norm1 * norm2))

    def _calculate_keyword_similarity(self, query: str, tool_name: str, tool_description: str) -> float:
        """Calculate keyword overlap similarity using Jaccard index with stop word filtering."""
        import re

        # Extract keywords (alphanumeric words, convert to lowercase)
        query_words = set(re.findall(r'\w+', query.lower()))
        tool_text = f"{tool_name} {tool_description}".lower()
        tool_words = set(re.findall(r'\w+', tool_text))

        # Filter out stop words and short words
        query_words = {w for w in query_words
                      if len(w) >= SimilarityConfig.MIN_WORD_LENGTH
                      and w not in SimilarityConfig.STOP_WORDS}
        tool_words = {w for w in tool_words
                     if len(w) >= SimilarityConfig.MIN_WORD_LENGTH
                     and w not in SimilarityConfig.STOP_WORDS}

        if not query_words or not tool_words:
            return 0.0

        # Calculate Jaccard similarity (intersection over union)
        intersection = len(query_words & tool_words)
        union = len(query_words | tool_words)

        return intersection / union if union > 0 else 0.0

    def _calculate_name_match(self, query: str, tool_name: str) -> float:
        """Calculate tool name matching score."""
        query_lower = query.lower()
        name_lower = tool_name.lower()

        # Exact substring match
        if name_lower in query_lower or query_lower in name_lower:
            return 1.0

        # Partial word match (handle underscore-separated tool names)
        name_words = set(name_lower.replace('_', ' ').split())
        query_words = set(query_lower.split())

        if name_words & query_words:
            return 0.5

        return 0.0

    def hybrid_similarity(self, query: str, tool_name: str, tool_description: str,
                         query_vector: List[float], tool_vector: List[float]) -> float:
        """Calculate hybrid similarity combining semantic and keyword matching."""

        # 1. Semantic similarity (vector-based)
        semantic_sim = self.cosine_similarity(query_vector, tool_vector)

        # 2. Keyword similarity (text-based)
        keyword_sim = self._calculate_keyword_similarity(query, tool_name, tool_description)

        # 3. Name matching bonus
        name_match = self._calculate_name_match(query, tool_name)

        # Weighted combination using configurable weights
        final_score = (
            SimilarityConfig.SEMANTIC_WEIGHT * semantic_sim +
            SimilarityConfig.KEYWORD_WEIGHT * keyword_sim +
            SimilarityConfig.NAME_MATCH_WEIGHT * name_match
        )

        return min(final_score, 1.0)  # Ensure score doesn't exceed 1.0

    async def search_similar_tools(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """Search for tools similar to the query using hybrid similarity."""
        # Generate vector for query
        query_vector = await self.tool_vector_manager.embeddings_model.aembed_query(query)

        # Calculate hybrid similarities
        similarities = []
        for tool_id, tool_vector in self.tool_vectors.items():
            tool_data = self.tools_data[tool_id]
            tool_name = tool_data["name"]
            tool_description = tool_data["description"]

            # Use hybrid similarity calculation
            similarity = self.hybrid_similarity(
                query, tool_name, tool_description, query_vector, tool_vector
            )

            similarities.append({
                "tool_id": tool_id,
                "similarity": similarity,
                "tool_data": tool_data,
                "match_details": {
                    "semantic_score": self.cosine_similarity(query_vector, tool_vector),
                    "keyword_score": self._calculate_keyword_similarity(query, tool_name, tool_description),
                    "name_match_score": self._calculate_name_match(query, tool_name)
                }
            })

        # Sort by similarity and return top_k
        similarities.sort(key=lambda x: x["similarity"], reverse=True)
        return similarities[:top_k]


class CustomToolSelector:
    """Custom tool selector that uses cached vectors for tool selection."""

    def __init__(self, cached_store: CachedVectorStore, tool_registry: Dict[str, Any]):
        """Initialize with cached store and tool registry."""
        self.cached_store = cached_store
        self.tool_registry = tool_registry

    async def select_tools(self, query: str, max_tools: int = 5) -> List[Any]:
        """Select relevant tools based on query using cached vectors."""
        # Search for similar tools
        similar_tools = await self.cached_store.search_similar_tools(query, max_tools)

        # Return the actual tool objects
        selected_tools = []
        for tool_info in similar_tools:
            tool_id = tool_info["tool_id"]
            if tool_id in self.tool_registry:
                selected_tools.append(self.tool_registry[tool_id])

        return selected_tools


class Configuration:
    """Manages configuration and environment variables for the MCP client."""

    def __init__(self, enable_recommendation: bool = False) -> None:
        """Initialize configuration with environment variables."""
        self.load_env()
        self.api_key = "sk-6oJPGtsErJX67TyOTBAr5hJ3n334zaVk8n949svyJLBovDvG"
        # 火山引擎 API 密钥 - 从环境变量获取或使用默认值
        self.volcengine_api_key = os.getenv("VOLCENGINE_API_KEY", "your-volcengine-api-key-here")
        # 阿里云API密钥 - 用于语义搜索和推荐系统
        self.aliyun_api_key = os.getenv("DASHSCOPE_API_KEY", "sk-fcac337c29fe4d6f93bb9ff2ca2395d8")
        # 推荐系统配置
        self.enable_recommendation = enable_recommendation

    @staticmethod
    def load_env() -> None:
        """Load environment variables from .env file."""
        load_dotenv()

    @staticmethod
    def load_config(file_path: str) -> dict[str, Any]:
        """Load server configuration from JSON file.

        Args:
            file_path: Path to the JSON configuration file.

        Returns:
            Dict containing server configuration.

        Raises:
            FileNotFoundError: If configuration file doesn't exist.
            JSONDecodeError: If configuration file is invalid JSON.
        """
        with open(file_path, "r") as f:
            return json.load(f)

    @property
    def llm_api_key(self) -> str:
        """Get the LLM API key.

        Returns:
            The API key as a string.

        Raises:
            ValueError: If the API key is not found in environment variables.
        """
        if not self.api_key:
            raise ValueError("LLM_API_KEY not found in environment variables")
        return self.api_key

    @property
    def volcengine_embedding_key(self) -> str:
        """Get the VolcEngine API key for embeddings.

        Returns:
            The VolcEngine API key as a string.

        Raises:
            ValueError: If the API key is not found in environment variables.
        """
        if not self.volcengine_api_key or self.volcengine_api_key == "your-volcengine-api-key-here":
            raise ValueError("VOLCENGINE_API_KEY not found in environment variables or not set properly")
        return self.volcengine_api_key


class Server:
    """Manages MCP server connections and tool execution."""

    def __init__(self, name: str, config: dict[str, Any]) -> None:
        self.name: str = name
        self.config: dict[str, Any] = config
        self.stdio_context: Any | None = None
        self.session: ClientSession | None = None
        self._cleanup_lock: asyncio.Lock = asyncio.Lock()
        self.exit_stack: AsyncExitStack = AsyncExitStack()

    async def initialize(self) -> None:
        """Initialize the server connection."""
        command = (
            shutil.which("npx")
            if self.config["command"] == "npx"
            else self.config["command"]
        )
        if command is None:
            raise ValueError("The command must be a valid string and cannot be None.")

        server_params = StdioServerParameters(
            command=command,
            args=self.config["args"],
            env={**os.environ, **self.config["env"]}
            if self.config.get("env")
            else None,
        )
        try:
            stdio_transport = await self.exit_stack.enter_async_context(
                stdio_client(server_params)
            )
            read, write = stdio_transport
            session = await self.exit_stack.enter_async_context(
                ClientSession(read, write)
            )
            await session.initialize()
            self.session = session
        except Exception as e:
            logging.error(f"Error initializing server {self.name}: {e}")
            await self.cleanup()
            raise

    async def list_tools(self) -> list[Any]:
        """List available tools from the server.

        Returns:
            A list of available tools.

        Raises:
            RuntimeError: If the server is not initialized.
        """
        if not self.session:
            raise RuntimeError(f"Server {self.name} not initialized")

        tools_response = await self.session.list_tools()
        tools = []

        for item in tools_response:
            if isinstance(item, tuple) and item[0] == "tools":
                tools.extend(
                    Tool(tool.name, tool.description, tool.inputSchema)
                    for tool in item[1]
                )

        return tools

    async def execute_tool(
        self,
        tool_name: str,
        arguments: dict[str, Any],
        retries: int = 2,
        delay: float = 1.0,
    ) -> Any:
        if not self.session:
            raise RuntimeError(f"Server {self.name} not initialized")

        attempt = 0
        while attempt < retries:
            try:
                logging.info(f"Executing {tool_name}...")
                result = await self.session.call_tool(tool_name, arguments)

                return result

            except Exception as e:
                attempt += 1
                logging.warning(
                    f"Error executing tool: {e}. Attempt {attempt} of {retries}."
                )
                if attempt < retries:
                    logging.info(f"Retrying in {delay} seconds...")
                    await asyncio.sleep(delay)
                else:
                    logging.error("Max retries reached. Failing.")
                    raise

    async def cleanup(self) -> None:
        """Clean up server resources."""
        async with self._cleanup_lock:
            try:
                await self.exit_stack.aclose()
                self.session = None
                self.stdio_context = None
            except Exception as e:
                logging.error(f"Error during cleanup of server {self.name}: {e}")


class Tool:
    """Represents a tool with its properties and formatting."""

    def __init__(
        self, name: str, description: str, input_schema: dict[str, Any]
    ) -> None:
        self.name: str = name
        self.description: str = description
        self.input_schema: dict[str, Any] = input_schema

    def format_for_llm(self) -> str:
        """Format tool information for LLM.

        Returns:
            A formatted string describing the tool.
        """
        args_desc = []
        if "properties" in self.input_schema:
            for param_name, param_info in self.input_schema["properties"].items():
                arg_desc = (
                    f"- {param_name}: {param_info.get('description', 'No description')}"
                )
                if param_name in self.input_schema.get("required", []):
                    arg_desc += " (required)"
                args_desc.append(arg_desc)

        return f"""
Tool: {self.name}
Description: {self.description}
Arguments:
{chr(10).join(args_desc)}
"""



class ChatSession:
    """Orchestrates the interaction between user, LLM, and tools."""

    def __init__(self, servers: list[Server], max_tools_per_task: int, embedding_type: str = "aliyun",
                 enable_recommendation: bool = False, aliyun_api_key: str = None) -> None:
        self.servers: list[Server] = servers
        self.tool_vector_manager: Optional[ToolVectorManager] = None
        self.max_tools_per_task = max_tools_per_task
        self.embedding_type = embedding_type
        self.enable_recommendation = enable_recommendation
        self.aliyun_api_key = aliyun_api_key or "sk-fcac337c29fe4d6f93bb9ff2ca2395d8"
        self.semantic_system: Optional[AliyunSemanticSystem] = None

        if self.embedding_type=="aliyun":
            self.vector_cache = VectorCache(cache_dir=".vector_cache_aliyun")
        elif self.embedding_type=="openai":
            self.vector_cache = VectorCache(cache_dir=".vector_cache_openai")
        else:
            self.vector_cache = VectorCache(cache_dir=".vector_cache")

        # Initialize Multi-tool retrieval if available
        if MULTI_TOOL_AVAILABLE:
            self.multi_tool_retriever = None  # Will be initialized after tool loading
            self.multi_tool_mode = True
            logging.info("🔧 Multi-tool Retrieval System available!")
        else:
            self.multi_tool_mode = False
            logging.info("📝 Using standard tool selection")

        # Initialize rerank if available
        if RERANK_AVAILABLE:
            self.text_reranker = None  # Will be initialized after tool loading
            self.rerank_mode = True
            logging.info("🚀 Text Reranker available!")
        else:
            self.rerank_mode = False
            logging.info("📝 No reranker available")

        # Initialize semantic system if available and enabled
        if SEMANTIC_SYSTEM_AVAILABLE and self.enable_recommendation:
            self.semantic_system_mode = True
            logging.info("🎯 Semantic Search & Recommendation System enabled!")
        else:
            self.semantic_system_mode = False
            if not SEMANTIC_SYSTEM_AVAILABLE:
                logging.info("📝 Semantic system not available")
            else:
                logging.info("📝 Semantic system disabled")

    def _handle_weights_command(self) -> None:
        """Handle the weights adjustment command."""
        print("\n⚖️  Similarity Weight Configuration")
        print("=" * 40)

        # Show current weights
        weights = SimilarityConfig.get_weights()
        print(f"Current weights:")
        print(f"  Semantic:  {weights['semantic']:.2f}")
        print(f"  Keywords:  {weights['keyword']:.2f}")
        print(f"  Name:      {weights['name_match']:.2f}")

        # Show available presets
        print(f"\nAvailable presets:")
        presets = SimilarityConfig.get_presets()
        for name, preset in presets.items():
            print(f"  {name}: {preset['description']}")

        print(f"\nOptions:")
        print(f"  1. Type preset name (e.g., 'semantic_focused')")
        print(f"  2. Type 'custom' to set custom weights")
        print(f"  3. Press Enter to keep current weights")

        choice = input("\nYour choice: ").strip().lower()

        if not choice:
            print("Keeping current weights.")
            return
        if choice == "custom":
            try:
                print("\nEnter weights (must sum to 1.0):")
                semantic = float(input("Semantic weight (0.0-1.0): "))
                keyword = float(input("Keyword weight (0.0-1.0): "))
                name_match = float(input("Name match weight (0.0-1.0): "))

                SimilarityConfig.set_weights(semantic, keyword, name_match)
                print(f"✅ Custom weights applied successfully!")

            except (ValueError, Exception) as e:
                print(f"❌ Error setting custom weights: {e}")

        elif choice in presets:
            description = SimilarityConfig.apply_preset(choice)
            print(f"✅ Applied preset '{choice}': {description}")

        else:
            print(f"❌ Unknown option: {choice}")

        # Show updated weights
        weights = SimilarityConfig.get_weights()
        print(f"\nUpdated weights:")
        print(f"  Semantic:  {weights['semantic']:.2f}")
        print(f"  Keywords:  {weights['keyword']:.2f}")
        print(f"  Name:      {weights['name_match']:.2f}")

    def _handle_multi_tool_command(self) -> None:
        """处理多工具查询命令"""
        if not self.multi_tool_mode:
            print("多工具查询功能不可用。")
            return

        print("\n🔧 多工具查询管理")
        print("=" * 40)

        print("选项:")
        print("  1. 测试多工具查询")
        print("  2. 切换多工具模式")

        choice = input("\n选择操作 (1-2): ").strip()

        if choice == "1":
            asyncio.create_task(self._test_multi_tool_query())
        elif choice == "2":
            self._toggle_multi_tool_mode()
        else:
            print("❌ 无效选择")

    async def _test_multi_tool_query(self) -> None:
        """测试多工具查询"""
        print("\n🧪 测试多工具查询")
        print("=" * 30)

        if not (hasattr(self, 'multi_tool_retriever') and self.multi_tool_retriever):
            print("❌ 多工具检索器未初始化")
            return

        test_query = input("输入测试查询: ").strip()
        if not test_query:
            print("❌ 查询不能为空")
            return

        try:
            print(f"\n🔍 正在测试查询: '{test_query}'")
            start_time = asyncio.get_event_loop().time()

            results = await self.multi_tool_retriever.retrieve_multi_tools(
                query=test_query,
                max_tools_per_task=self.max_tools_per_task,
                total_max_tools=10
            )

            end_time = asyncio.get_event_loop().time()
            processing_time = end_time - start_time

            print(f"\n✅ 测试完成 (耗时: {processing_time:.2f}s)")
            print(f"📊 返回结果数量: {len(results.tool_matches)}")

            if results.tool_matches:
                print(f"\n🎯 检索到的工具:")
                for i, tool_match in enumerate(results.tool_matches, 1):
                    print(f"\n{i}. {tool_match.tool_name}")
                    print(f"   描述: {tool_match.description}")
                    print(f"   置信度: {tool_match.confidence:.3f}")
                    print(f"   子任务ID: {tool_match.subtask_id}")
            else:
                print("❌ 未找到相关工具")

        except Exception as e:
            print(f"❌ 测试失败: {e}")

    def _toggle_multi_tool_mode(self) -> None:
        """切换多工具模式"""
        if not (hasattr(self, 'multi_tool_retriever') and self.multi_tool_retriever):
            print("❌ 多工具检索器未初始化")
            return

        # 简单的开关切换
        self.multi_tool_mode = not self.multi_tool_mode
        status = "启用" if self.multi_tool_mode else "禁用"
        print(f"✅ 多工具模式已{status}")

    def _handle_semantic_command(self) -> None:
        """处理语义搜索和推荐命令"""
        if not self.semantic_system_mode:
            print("❌ 语义搜索和推荐系统未启用。")
            return

        print("\n🎯 语义搜索和推荐系统管理")
        print("=" * 40)

        print("选项:")
        print("  1. 演示语义搜索")
        print("  2. 演示语义推荐")
        print("  3. 运行完整演示")
        print("  4. 查看系统统计")

        choice = input("\n选择操作 (1-4): ").strip()

        if choice == "1":
            asyncio.create_task(self._demo_semantic_search())
        elif choice == "2":
            asyncio.create_task(self._demo_semantic_recommendation())
        elif choice == "3":
            asyncio.create_task(self._run_full_semantic_demo())
        elif choice == "4":
            asyncio.create_task(self._show_semantic_stats())
        else:
            print("❌ 无效选择")

    async def _demo_semantic_search(self) -> None:
        """演示语义搜索"""
        if not self.semantic_system:
            print("❌ 语义系统未初始化")
            return

        print("\n🔍 语义搜索演示")
        print("=" * 30)

        query = input("输入搜索查询: ").strip()
        if not query:
            print("❌ 查询不能为空")
            return

        try:
            print(f"\n🔍 正在搜索: '{query}'")
            results = await self.semantic_system.search_engine.search(query, top_k=5, min_similarity=0.1)

            if results:
                print(f"\n✅ 找到 {len(results)} 个相关结果:")
                for i, result in enumerate(results, 1):
                    content = result.content[:100] + "..." if len(result.content) > 100 else result.content
                    print(f"  {i}. 相似度: {result.similarity:.3f}")
                    print(f"     内容: {content}")
                    print(f"     类别: {result.metadata.get('category', 'N/A')}")
            else:
                print("❌ 未找到相关结果")

        except Exception as e:
            print(f"❌ 搜索失败: {e}")

    async def _demo_semantic_recommendation(self) -> None:
        """演示语义推荐"""
        if not self.semantic_system:
            print("❌ 语义系统未初始化")
            return

        print("\n🎯 语义推荐演示")
        print("=" * 30)

        # 显示可用用户
        if hasattr(self.semantic_system.recommendation_engine, 'user_profiles'):
            users = list(self.semantic_system.recommendation_engine.user_profiles.keys())
            if users:
                print(f"可用用户: {', '.join(users)}")
                user_id = input("选择用户ID (或输入新用户ID): ").strip()
            else:
                user_id = input("输入用户ID: ").strip()
        else:
            user_id = input("输入用户ID: ").strip()

        if not user_id:
            print("❌ 用户ID不能为空")
            return

        try:
            print(f"\n🎯 为用户 '{user_id}' 生成推荐:")

            # 如果用户不存在，创建一个简单的用户画像
            if not hasattr(self.semantic_system.recommendation_engine, 'user_profiles') or \
               user_id not in self.semantic_system.recommendation_engine.user_profiles:
                interests = input("输入用户兴趣 (用逗号分隔): ").strip().split(',')
                interests = [interest.strip() for interest in interests if interest.strip()]
                if interests:
                    await self.semantic_system.recommendation_engine.create_user_profile(
                        user_id=user_id,
                        interests=interests,
                        interaction_history=[]
                    )
                    print(f"✅ 已创建用户画像: {interests}")

            # 生成推荐
            recommendations = await self.semantic_system.recommendation_engine.recommend_items(
                user_id=user_id,
                top_k=5,
                diversity_factor=0.2
            )

            if recommendations:
                print(f"\n推荐结果:")
                for i, rec in enumerate(recommendations, 1):
                    print(f"  {i}. {rec.item_id} (相似度: {rec.similarity:.3f})")
                    print(f"     {rec.content[:80]}...")
                    print(f"     价格: ¥{rec.metadata.get('price', 'N/A')} | 时长: {rec.metadata.get('duration', 'N/A')}")
            else:
                print("❌ 未找到推荐结果")

        except Exception as e:
            print(f"❌ 推荐失败: {e}")

    async def _run_full_semantic_demo(self) -> None:
        """运行完整的语义系统演示"""
        if not self.semantic_system:
            print("❌ 语义系统未初始化")
            return

        print("\n🚀 运行完整语义系统演示")
        print("=" * 40)

        try:
            await self.semantic_system.run_full_demo()
        except Exception as e:
            print(f"❌ 演示失败: {e}")

    async def _show_semantic_stats(self) -> None:
        """显示语义系统统计信息"""
        if not self.semantic_system:
            print("❌ 语义系统未初始化")
            return

        print("\n📊 语义系统统计信息")
        print("=" * 30)

        try:
            await self.semantic_system.print_system_stats()
        except Exception as e:
            print(f"❌ 获取统计信息失败: {e}")



    async def cleanup_servers(self) -> None:
        """Clean up all servers properly."""
        for server in reversed(self.servers):
            try:
                await server.cleanup()
            except Exception as e:
                logging.warning(f"Warning during final cleanup: {e}")

    async def process_llm_response(self, llm_response: str) -> str:
        """Process the LLM response and execute tools if needed.

        Args:
            llm_response: The response from the LLM.

        Returns:
            The result of tool execution or the original response.
        """
        import json

        try:
            tool_call = json.loads(llm_response)
            if "tool" in tool_call and "arguments" in tool_call:
                logging.info(f"Executing tool: {tool_call['tool']}")
                logging.info(f"With arguments: {tool_call['arguments']}")

                for server in self.servers:
                    tools = await server.list_tools()
                    if any(tool.name == tool_call["tool"] for tool in tools):
                        try:
                            result = await server.execute_tool(
                                tool_call["tool"], tool_call["arguments"]
                            )

                            if isinstance(result, dict) and "progress" in result:
                                progress = result["progress"]
                                total = result["total"]
                                percentage = (progress / total) * 100
                                logging.info(
                                    f"Progress: {progress}/{total} ({percentage:.1f}%)"
                                )

                            return f"Tool execution result: {result}"
                        except Exception as e:
                            error_msg = f"Error executing tool: {str(e)}"
                            logging.error(error_msg)
                            return error_msg

                return f"No server found with tool: {tool_call['tool']}"
            return llm_response
        except json.JSONDecodeError:
            return llm_response

    async def initialize_system(self) -> tuple:
        """Initialize the chat system and return necessary components."""
        try:
            for server in self.servers:
                try:
                    await server.initialize()
                except Exception as e:
                    logging.error(f"Failed to initialize server: {e}")
                    await self.cleanup_servers()
                    raise

            all_tools = []
            for server in self.servers:
                tools = await server.list_tools()
                all_tools.extend(tools)

            # Convert custom Tool objects to LangChain compatible tools
            from langchain_core.tools import StructuredTool

            def create_langchain_tool(custom_tool, servers):
                """Convert custom Tool to LangChain StructuredTool."""
                async def tool_func(**kwargs):
                    # Find the server that has this tool and execute it
                    for server in servers:
                        server_tools = await server.list_tools()
                        if any(t.name == custom_tool.name for t in server_tools):
                            return await server.execute_tool(custom_tool.name, kwargs)
                    raise ValueError(f"Tool {custom_tool.name} not found on any server")

                return StructuredTool.from_function(
                    coroutine=tool_func,  # Use coroutine parameter for async functions
                    name=custom_tool.name,
                    description=custom_tool.description,
                    args_schema=None  # You might want to create a proper schema here
                )

            # 根据配置选择embedding服务
            if self.embedding_type == "aliyun":
                try:
                    logging.info("🔥 使用阿里云 text-embedding-v4")
                    aliyun_api_key = "sk-fcac337c29fe4d6f93bb9ff2ca2395d8"
                    embeddings = create_aliyun_embeddings(
                        api_key=aliyun_api_key,
                        model="text-embedding-v4",  # 使用最新的v4模型
                        dimensions=1024,  # 推荐使用1024维，平衡性能和精度
                        instruct="Given a web search query, retrieve relevant passages that answer the query",
                        max_batch_size=10,  # 阿里云建议的批处理大小
                        timeout=30
                    )
                except Exception as e:
                    logging.warning(f"阿里云embedding初始化失败: {e}")
                    logging.info("🔄 回退到OpenAI embeddings")
                    embeddings = init_embeddings("openai:text-embedding-3-large")
            elif self.embedding_type == "doubao":
                try:
                    logging.info("🔄 使用豆包 embeddings")
                    volcengine_api_key = "935e14d7-245c-4adc-8cca-179ae0947829"
                    embeddings = create_volcengine_embeddings(
                        api_key=volcengine_api_key,
                        model="doubao-embedding-large-text-250515",  # 推荐使用 large 版本，支持 4096 维向量
                        dimensions=2048,  # 设置向量维度为 2048
                        max_batch_size=50,  # 批处理大小
                        timeout=30  # 请求超时时间
                    )
                except Exception as e:
                    logging.warning(f"豆包embedding初始化失败: {e}")
                    logging.info("🔄 回退到OpenAI embeddings")
                    embeddings = init_embeddings("openai:text-embedding-3-large")
            elif self.embedding_type == "openai":
                logging.info("🔄 使用OpenAI embeddings")
                embeddings = init_embeddings("openai:text-embedding-3-large")
            else:
                logging.warning(f"未知的embedding类型: {self.embedding_type}, 使用默认的阿里云embedding")
                try:
                    aliyun_api_key = os.getenv("DASHSCOPE_API_KEY", "sk-fcac337c29fe4d6f93bb9ff2ca2395d8")
                    embeddings = create_aliyun_embeddings(
                        api_key=aliyun_api_key,
                        model="text-embedding-v4",
                        dimensions=2048
                    )
                except Exception as e:
                    logging.error(f"默认embedding初始化失败: {e}")
                    logging.info("🔄 最终回退到OpenAI embeddings")
                    embeddings = init_embeddings("openai:text-embedding-3-large")

            self.tool_vector_manager = ToolVectorManager(embeddings, self.vector_cache)

            # Create tool registry with LangChain compatible tools and original tools mapping
            tool_registry = {}
            original_tools_map = {}  # 映射 tool_id 到原始工具对象
            for tool in all_tools:
                # print(tool.input_schema)
                tool_id = str(uuid.uuid4())
                langchain_tool = create_langchain_tool(tool, self.servers)
                tool_registry[tool_id] = langchain_tool
                original_tools_map[tool_id] = tool  # 保存原始工具对象

            # Create our custom cached vector store instead of InMemoryStore
            cached_store = CachedVectorStore(self.tool_vector_manager)

            # Populate cached store with toolsl use cache when available)
            logging.info("Loading tool vectors (using cache when available)...")
            a=tool_registry.items()
            for tool_id, tool in a:
                print("tool_name: {}, tool_description: {}".format(
                    tool.name if hasattr(tool, 'name') else 'Unknown',
                    tool.description if hasattr(tool, 'description') else 'No description',
                ))
                try:
                    await cached_store.add_tool(tool_id, tool.name, tool.description)
                except Exception as e:
                    logging.error(f"Failed to process tool {tool.name}: {e}")
                    continue

            # Save cache after processing all tools
            self.tool_vector_manager.save_cache()
            logging.info("Tool vector processing completed")

            # Initialize Multi-tool retriever if available
            if self.multi_tool_mode:
                try:
                    self.multi_tool_retriever = MultiToolRetriever(
                        cached_store=cached_store,
                        tool_vector_manager=self.tool_vector_manager
                    )
                    logging.info("🔧 Multi-tool Retriever initialized")
                except Exception as e:
                    logging.error(f"Failed to initialize Multi-tool Retriever: {e}")
                    self.multi_tool_mode = False

            # Initialize Text Reranker if available
            if self.rerank_mode:
                try:
                    self.text_reranker = AliyunTextReranker(
                        api_key="sk-fcac337c29fe4d6f93bb9ff2ca2395d8"
                    )
                    logging.info("🚀 Text Reranker initialized")
                except Exception as e:
                    logging.error(f"Failed to initialize Text Reranker: {e}")
                    self.rerank_mode = False

            # Initialize Semantic System if available and enabled
            if self.semantic_system_mode:
                try:
                    self.semantic_system = AliyunSemanticSystem(
                        api_key=self.aliyun_api_key,
                        model="text-embedding-v4",
                        dimensions=1024
                    )
                    # Load sample data for demonstration
                    await self.semantic_system.load_sample_data()
                    await self.semantic_system.create_sample_users()
                    logging.info("🎯 Semantic Search & Recommendation System initialized")
                except Exception as e:
                    logging.error(f"Failed to initialize Semantic System: {e}")
                    self.semantic_system_mode = False
                    self.semantic_system = None

            # Create custom tool selector
            tool_selector = CustomToolSelector(cached_store, tool_registry)

            # Create LLM
            llm = init_chat_model("openai:gpt-4o-mini")

            return all_tools, cached_store, tool_registry, original_tools_map, tool_selector, llm

        except Exception as e:
            await self.cleanup_servers()
            raise

    async def create_tool_selection_node(self, cached_store, tool_registry, original_tools_map):
        """Create the tool selection node function."""
        async def tool_selection_node(state: MessagesState):
            """Node that selects and calls relevant tools based on user query."""
            messages = state["messages"]
            last_message = messages[-1]

            if isinstance(last_message, HumanMessage):
                query = last_message.content
                similar_tools = None

                # 优先使用Multi-tool检索
                if self.multi_tool_mode and hasattr(self, 'multi_tool_retriever') and self.multi_tool_retriever:
                    try:
                        multi_result = await self.multi_tool_retriever.retrieve_multi_tools(
                            query=query,
                            max_tools_per_task=5,
                            total_max_tools=10
                        )

                        # Convert to similar_tools format
                        similar_tools = []
                        for tool_match in multi_result.tool_matches:
                            similar_tools.append({
                                "tool_id": tool_match.tool_id,
                                "similarity": tool_match.confidence,
                                "tool_data": {
                                    "name": tool_match.tool_name,
                                    "description": tool_match.description
                                },
                                "match_details": {
                                    "semantic_score": tool_match.confidence,
                                    "keyword_score": 0.0,
                                    "name_match_score": 0.0
                                },
                                "reasoning": f"Multi-tool match for subtask: {tool_match.subtask_id}",
                                "selection_method": "multi_tool",
                                "execution_time": 0.0,
                                "subtask_id": tool_match.subtask_id
                            })

                        logging.info(f"🔧 Multi-tool Retriever returned {len(similar_tools)} results")

                        # 如果启用重排序，对结果进行重排序
                        if self.rerank_mode and hasattr(self, 'text_reranker') and self.text_reranker and similar_tools:
                            try:
                                documents = [
                                    f"{result['tool_data']['name']}: {result['tool_data']['description']}"
                                    for result in similar_tools
                                ]

                                rerank_results = await self.text_reranker.rerank_documents(
                                    query, documents, top_n=len(documents)
                                )

                                # 更新相似度分数
                                for rerank_result in rerank_results:
                                    if rerank_result.index < len(similar_tools):
                                        original_similarity = similar_tools[rerank_result.index]["similarity"]
                                        similar_tools[rerank_result.index]["similarity"] = rerank_result.relevance_score
                                        similar_tools[rerank_result.index]["match_details"]["rerank_score"] = rerank_result.relevance_score
                                        similar_tools[rerank_result.index]["match_details"]["original_similarity"] = original_similarity
                                        similar_tools[rerank_result.index]["selection_method"] = "multi_tool_reranked"

                                # 重新排序
                                similar_tools.sort(key=lambda x: x["similarity"], reverse=True)
                                logging.info(f"🚀 Results reranked using text reranker")

                            except Exception as e:
                                logging.warning(f"Reranking failed, using original results: {e}")

                    except Exception as e:
                        logging.error(f"Multi-tool retrieval error, falling back: {e}")
                        similar_tools = None

                # 回退到基础向量检索
                if similar_tools is None:
                    try:
                        similar_tools = await cached_store.search_similar_tools(query, top_k=10)
                        logging.info(f"📝 Using basic vector search, returned {len(similar_tools)} results")

                        # 如果启用重排序，对基础结果进行重排序
                        if self.rerank_mode and hasattr(self, 'text_reranker') and self.text_reranker and similar_tools:
                            try:
                                documents = [
                                    f"{result['tool_data']['name']}: {result['tool_data']['description']}"
                                    for result in similar_tools
                                ]

                                rerank_results = await self.text_reranker.rerank_documents(
                                    query, documents, top_n=len(documents)
                                )

                                # 更新相似度分数
                                for rerank_result in rerank_results:
                                    if rerank_result.index < len(similar_tools):
                                        original_similarity = similar_tools[rerank_result.index]["similarity"]
                                        similar_tools[rerank_result.index]["similarity"] = rerank_result.relevance_score
                                        similar_tools[rerank_result.index]["match_details"]["rerank_score"] = rerank_result.relevance_score
                                        similar_tools[rerank_result.index]["match_details"]["original_similarity"] = original_similarity
                                        similar_tools[rerank_result.index]["selection_method"] = "basic_reranked"

                                # 重新排序
                                similar_tools.sort(key=lambda x: x["similarity"], reverse=True)
                                logging.info(f"🚀 Basic results reranked using text reranker")

                            except Exception as e:
                                logging.warning(f"Reranking failed, using original results: {e}")

                    except Exception as e:
                        logging.error(f"Basic search also failed: {e}")
                        similar_tools = []

                if similar_tools:
                    # Create detailed response with similarity scores
                    tool_descriptions = []
                    selection_methods = set()
                    # 构建返回的工具数据结构
                    tools_data_array = []

                    for tool_info in similar_tools:
                        tool_data = tool_info["tool_data"]
                        similarity = tool_info["similarity"]
                        match_details = tool_info.get("match_details", {})
                        selection_method = tool_info.get("selection_method", "unknown")

                        selection_methods.add(selection_method)

                        # Format similarity score as percentage
                        similarity_pct = similarity * 100

                        # Create detailed description with method indicator
                        method_emoji = {
                            "multi_tool": "🔧",
                            "multi_tool_reranked": "🔧🚀",
                            "basic_reranked": "📝🚀",
                            "hybrid_similarity": "🔄",
                            "unknown": "❓"
                        }.get(selection_method, "❓")
                        desc = f"{method_emoji} **{tool_data['name']}** (Match: {similarity_pct:.1f}%)"
                        desc += f"\n   📝 {tool_data['description']}"

                        # Add match breakdown
                        if "rerank_score" in match_details:
                            # 重排序结果的详细信息
                            original_sim = match_details.get('original_similarity', 0) * 100
                            rerank_score = match_details.get('rerank_score', 0) * 100
                            desc += f"\n   📊 Original: {original_sim:.1f}% | Rerank: {rerank_score:.1f}%"
                        elif match_details:
                            # 传统检索的详细信息
                            semantic = match_details.get('semantic_score', 0) * 100
                            keyword = match_details.get('keyword_score', 0) * 100
                            name_match = match_details.get('name_match_score', 0) * 100
                            desc += f"\n   📊 Semantic: {semantic:.1f}% | Keywords: {keyword:.1f}% | Name: {name_match:.1f}%"

                        # Add reasoning if available
                        if 'reasoning' in tool_info:
                            desc += f"\n   🧠 Reasoning: {tool_info['reasoning']}"

                        # Add subtask info for multi-tool results
                        if 'subtask_id' in tool_info:
                            desc += f"\n   🎯 Subtask: {tool_info['subtask_id']}"

                        # Add selection method info
                        desc += f"\n   ⚙️ Method: {selection_method}"

                        # Add schema information and build tool data structure
                        tool_id = tool_info["tool_id"]
                        tool_data_item = {
                            "name": tool_data['name'],
                            "description": tool_data['description'],
                            "parameters": None
                        }

                        if tool_id in original_tools_map:
                            original_tool = original_tools_map[tool_id]
                            input_schema = getattr(original_tool, 'input_schema', None)

                            if input_schema:
                                try:
                                    # 处理 input_schema，可能是字典或 Pydantic 模型类
                                    if hasattr(input_schema, 'model_json_schema'):
                                        schema_dict = input_schema.model_json_schema()
                                    elif hasattr(input_schema, 'schema'):
                                        schema_dict = input_schema.schema()
                                    elif isinstance(input_schema, dict):
                                        schema_dict = input_schema
                                    else:
                                        schema_dict = None

                                    if schema_dict:
                                        schema_str = json.dumps(schema_dict, indent=2, ensure_ascii=False)
                                        desc += f"\n   📋 Schema:\n```json\n{schema_str}\n```"
                                        # 添加到工具数据结构中
                                        tool_data_item["parameters"] = schema_dict
                                    else:
                                        desc += f"\n   📋 Schema: Unable to extract schema"
                                except Exception as e:
                                    desc += f"\n   📋 Schema: Error getting schema - {e}"
                            else:
                                desc += f"\n   📋 Schema: Not available"

                        tools_data_array.append(tool_data_item)
                        tool_descriptions.append(desc)

                    # 检索完成后打印JSON格式的工具数组
                    print(f"\n🔍 检索完成，找到 {len(tools_data_array)} 个相关工具:")
                    print(json.dumps(tools_data_array, indent=2, ensure_ascii=False))

                    # 注释掉详细输出，只保留JSON格式
                    # tools_text = "\n\n".join(tool_descriptions)
                    # Create response with method information
                    # methods_used = ", ".join(selection_methods)
                    # response = f"🎯 Found {len(similar_tools)} relevant tools using {methods_used}:\n\n{tools_text}"

                    # 只返回简单的确认信息，主要输出在JSON中
                    # response = f"✅ 检索完成，已输出 {len(tools_data_array)} 个工具的JSON信息"

                    return {"messages": [AIMessage(content=tools_data_array)]}

                else:
                    return {"messages": [AIMessage(content="❌ I couldn't find any relevant tools for your query. Could you please rephrase or be more specific?")]}

            return {"messages": []}

        return tool_selection_node

    async def process_query(self, user_query: str) -> str:
        """Process a single query and return the result."""
        try:
            # Initialize system if not already done
            if not hasattr(self, 'agent') or self.agent is None:
                all_tools, cached_store, tool_registry, original_tools_map, tool_selector, llm = await self.initialize_system()

                # Create tool selection node
                tool_selection_node = await self.create_tool_selection_node(cached_store, tool_registry, original_tools_map)

                # Create a simple graph
                workflow = StateGraph(MessagesState)
                workflow.add_node("tool_selection", tool_selection_node)
                workflow.set_entry_point("tool_selection")
                workflow.set_finish_point("tool_selection")

                self.agent = workflow.compile()
                self.all_tools = all_tools

                logging.info(f"🤖 MCP Chatbot initialized with {len(all_tools)} tools")

            # Process the query
            start_time = asyncio.get_event_loop().time()
            result = await self.agent.ainvoke({"messages": [HumanMessage(content=user_query)]})
            end_time = asyncio.get_event_loop().time()

            # Extract the response
            if "messages" in result and result["messages"]:
                last_message = result["messages"][-1]
                if hasattr(last_message, 'content'):
                    response = last_message.content
                else:
                    response = str(last_message)
            else:
                response = "❌ No response generated"

            processing_time = end_time - start_time
            logging.info(f"⏱️ Processing time: {processing_time:.2f}s")

            return response

        except Exception as e:
            logging.error(f"Error processing query: {e}")
            return f"❌ Error processing query: {str(e)}"

    async def start(self) -> None:
        """Interactive chat session (original functionality)."""
        try:
            all_tools, cached_store, tool_registry, original_tools_map, tool_selector, llm = await self.initialize_system()

            # Create tool selection node
            tool_selection_node = await self.create_tool_selection_node(cached_store, tool_registry, original_tools_map)

            # Create a simple graph
            workflow = StateGraph(MessagesState)
            workflow.add_node("tool_selection", tool_selection_node)
            workflow.set_entry_point("tool_selection")
            workflow.set_finish_point("tool_selection")

            agent = workflow.compile()

            # Create a mapping of tool names to descriptions for enhanced display
            tool_name_to_description = {tool.name: tool.description for tool in all_tools}

            # print("\n🤖 MCP Chatbot with Multi-tool Retrieval + Reranking Ready!")
            print(f"Loaded {len(all_tools)} tools with cached vectors")

            if self.multi_tool_mode:
                print("🔧 Multi-tool Retrieval: ENABLED")
            else:
                print("📝 Using basic vector search")

            if self.rerank_mode:
                print("🚀 Text Reranker: ENABLED")
            else:
                print("📝 No reranker available")

            if self.semantic_system_mode:
                print("🎯 Semantic Search & Recommendation: ENABLED")
            else:
                print("📝 Semantic system disabled")

            print("🎯 Using hybrid similarity: semantic + keyword + name matching")
            weights = SimilarityConfig.get_weights()
            print(f"⚖️  Current weights: Semantic={weights['semantic']:.1f}, Keywords={weights['keyword']:.1f}, Name={weights['name_match']:.1f}")

            # 显示可用命令
            commands = ["'quit'/'exit'", "'clear_cache'", "'weights'"]
            if self.multi_tool_mode:
                commands.append("'multi_tool'")
            if self.semantic_system_mode:
                commands.append("'semantic'")

            print(f"Commands: {', '.join(commands)}")

            while True:
                try:
                    user_input = input("\nYou: ").strip()
                    if user_input.lower() in ["quit", "exit"]:
                        logging.info("Exiting...")
                        break
                    elif user_input.lower() == "clear_cache":
                        self.vector_cache.clear()
                        print("Vector cache cleared!")
                        continue
                    elif user_input.lower() == "weights":
                        self._handle_weights_command()
                        continue
                    elif user_input.lower() == "multi_tool" and self.multi_tool_mode:
                        self._handle_multi_tool_command()
                        continue
                    elif user_input.lower() == "semantic" and self.semantic_system_mode:
                        self._handle_semantic_command()
                        continue

                    # Use our custom agent
                    start_time = asyncio.get_event_loop().time()
                    result = await agent.ainvoke({"messages": [HumanMessage(content=user_input)]})
                    end_time = asyncio.get_event_loop().time()

                    # Display the response
                    if "messages" in result and result["messages"]:
                        last_message = result["messages"][-1]
                        # if hasattr(last_message, 'content'):
                        #     print(f"\n🤖 Assistant: {last_message.content}")
                        # else:
                        #     print(f"\n🤖 Assistant: {last_message}")

                    processing_time = end_time - start_time
                    print(f"\n⏱️ Processing time: {processing_time:.2f}s")

                except KeyboardInterrupt:
                    logging.info("Exiting...")
                    break
                except Exception as e:
                    logging.error(f"Error processing request: {e}")
                    print(f"Sorry, I encountered an error: {e}")

        finally:
            await self.cleanup_servers()


# 全局变量存储初始化后的chat_session
_global_chat_session = None
_initialization_lock = asyncio.Lock()

async def initialize_global_session(embedding_type: str = "aliyun", enable_recommendation: bool = False, config_file: str = "servers_config.json") -> None:
    """
    全局初始化函数，只执行一次初始化工作

    Args:
        embedding_type: 嵌入模型类型 ("aliyun", "doubao", "openai")
        enable_recommendation: 是否启用语义搜索和推荐系统
        config_file: 服务器配置文件路径
    """
    global _global_chat_session

    async with _initialization_lock:
        if _global_chat_session is not None:
            logging.info("🔄 Global session already initialized, skipping...")
            return

        logging.info(f"🚀 Initializing global MCP session (Embedding: {embedding_type}, Recommendation: {enable_recommendation})")

        try:
            config = Configuration(enable_recommendation=enable_recommendation)
            server_config = config.load_config(config_file)
            servers = [
                Server(name, srv_config)
                for name, srv_config in server_config["mcpServers"].items()
            ]

            chat_session = ChatSession(
                servers,
                max_tools_per_task=5,
                embedding_type=embedding_type,
                enable_recommendation=enable_recommendation,
                aliyun_api_key=config.aliyun_api_key
            )

            # 预初始化系统（加载工具、向量等）
            all_tools, cached_store, tool_registry, original_tools_map, tool_selector, llm = await chat_session.initialize_system()

            # 创建工具选择节点
            tool_selection_node = await chat_session.create_tool_selection_node(cached_store, tool_registry, original_tools_map)

            # 创建简单的图
            from langgraph.graph import StateGraph, MessagesState
            workflow = StateGraph(MessagesState)
            workflow.add_node("tool_selection", tool_selection_node)
            workflow.set_entry_point("tool_selection")
            workflow.set_finish_point("tool_selection")

            chat_session.agent = workflow.compile()
            chat_session.all_tools = all_tools

            _global_chat_session = chat_session

            logging.info(f"✅ Global session initialized successfully with {len(all_tools)} tools")

        except Exception as e:
            logging.error(f"❌ Failed to initialize global session: {e}")
            raise

async def query_tools_fast(user_query: str) -> str:
    """
    快速查询接口，使用已初始化的全局session

    Args:
        user_query: 用户查询字符串

    Returns:
        工具检索结果
    """
    global _global_chat_session

    if _global_chat_session is None:
        return "❌ Global session not initialized. Please call initialize_global_session() first."

    try:
        start_time = asyncio.get_event_loop().time()

        from langchain_core.messages import HumanMessage
        result = await _global_chat_session.agent.ainvoke({"messages": [HumanMessage(content=user_query)]})

        end_time = asyncio.get_event_loop().time()
        processing_time = end_time - start_time

        # 提取响应
        if "messages" in result and result["messages"]:
            last_message = result["messages"][-1]
            if hasattr(last_message, 'content'):
                response = last_message.content
            else:
                response = str(last_message)
        else:
            response = "❌ No response generated"

        logging.info(f"⚡ Fast query processed in {processing_time:.2f}s")
        return response

    except Exception as e:
        logging.error(f"❌ Error in fast query: {e}")
        return f"❌ Error processing query: {str(e)}"

async def query_tools(user_query: str, config_file: str = "servers_config.json", embedding_type: str = "aliyun") -> str:
    """
    兼容性查询接口（保持向后兼容）

    Args:
        user_query: The user's query string
        config_file: Path to the server configuration file
        embedding_type: Type of embedding service to use ("aliyun", "doubao", "openai")

    Returns:
        The response from the tool selection system
    """
    # 如果全局session未初始化，先初始化
    if _global_chat_session is None:
        await initialize_global_session(embedding_type, False, config_file)

    # 使用快速查询
    return await query_tools_fast(user_query)

def get_global_session():
    """
    获取全局session对象

    Returns:
        全局chat_session对象或None
    """
    return _global_chat_session

def is_global_session_initialized() -> bool:
    """
    检查全局session是否已初始化

    Returns:
        True if initialized, False otherwise
    """
    return _global_chat_session is not None

def get_global_session_tools_count() -> int:
    """
    获取全局session中的工具数量

    Returns:
        工具数量，如果未初始化则返回0
    """
    if _global_chat_session and hasattr(_global_chat_session, 'all_tools') and _global_chat_session.all_tools:
        return len(_global_chat_session.all_tools)
    return 0

async def cleanup_global_session() -> None:
    """
    清理全局session资源
    """
    global _global_chat_session

    if _global_chat_session is not None:
        try:
            await _global_chat_session.cleanup_servers()
            _global_chat_session = None
            logging.info("🧹 Global session cleaned up")
        except Exception as e:
            logging.error(f"❌ Error cleaning up global session: {e}")

async def main(embedding_type: str = "aliyun") -> None:
    """Initialize and run the interactive chat session."""
    # 初始化全局session
    await initialize_global_session(embedding_type)

    # 运行交互式会话
    if _global_chat_session:
        await _global_chat_session.start()
    else:
        logging.error("❌ Failed to start interactive session - global session not initialized")



async def start_server_mode(embedding_type: str = "aliyun", enable_recommendation: bool = False) -> None:
    """
    启动服务器模式：先初始化，然后等待查询
    """
    print(f"🚀 启动MCP工具检索服务器 (Embedding: {embedding_type})")
    print("💡 支持的embedding类型: aliyun, doubao, openai")
    if enable_recommendation:
        print("🎯 语义搜索和推荐系统: 启用")
    else:
        print("📝 语义搜索和推荐系统: 禁用")

    try:
        # 初始化全局session
        await initialize_global_session(embedding_type, enable_recommendation)
        print("✅ 服务器初始化完成，可以开始接收查询请求")

        # 进入交互模式，等待用户输入
        print("\n" + "="*50)
        print("🔍 进入查询模式 - 输入查询内容，输入 'quit' 退出")
        print("="*50)

        while True:
            try:
                user_input = input("\n🤖 请输入查询: ").strip()

                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("👋 退出查询模式")
                    break

                if not user_input:
                    print("❌ 查询内容不能为空")
                    continue

                print(f"\n🔍 正在检索: {user_input}")
                start_time = asyncio.get_event_loop().time()

                # 使用快速查询接口
                result = await query_tools_fast(user_input)

                end_time = asyncio.get_event_loop().time()
                processing_time = end_time - start_time

                print(f"\n⚡ 检索完成 (耗时: {processing_time:.2f}s)")
                print(f"📋 结果: {result}")

            except KeyboardInterrupt:
                print("\n👋 用户中断，退出查询模式")
                break
            except Exception as e:
                print(f"❌ 查询出错: {e}")

    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
    finally:
        # 清理资源
        await cleanup_global_session()
        print("🧹 资源清理完成")

if __name__ == "__main__":
    import sys

    # 解析命令行参数
    embedding_type = "aliyun"
    query_args = []
    server_mode = True
    enable_recommendation = False

    i = 1
    while i < len(sys.argv):
        if sys.argv[i] == "--embedding" and i + 1 < len(sys.argv):
            embedding_type = sys.argv[i + 1]
            i += 2
        elif sys.argv[i] == "--server":
            server_mode = True
            i += 1
        elif sys.argv[i] == "--enable-recommendation":
            enable_recommendation = True
            i += 1
        elif sys.argv[i] == "--help" or sys.argv[i] == "-h":
            print("用法: python main.py [选项]")
            print("选项:")
            print("  --embedding TYPE        设置embedding类型 (aliyun, doubao, openai)")
            print("  --server               启动服务器模式 (默认)")
            print("  --enable-recommendation 启用语义搜索和推荐系统")
            print("  --help, -h             显示此帮助信息")
            print("\n示例:")
            print("  python main.py --server --embedding aliyun")
            print("  python main.py --server --enable-recommendation")
            print("  python main.py --embedding doubao --enable-recommendation")
            sys.exit(0)
        else:
            query_args.append(sys.argv[i])
            i += 1

    if server_mode:
        # 服务器模式：先初始化，然后等待查询
        asyncio.run(start_server_mode(embedding_type=embedding_type, enable_recommendation=True))
    elif query_args:
        # 单次查询模式：执行查询后退出
        query = " ".join(query_args)
        result = asyncio.run(query_tools(query, embedding_type=embedding_type))
        print(result)
    else:
        # 传统交互模式
        print(f"🚀 启动MCP聊天机器人 (Embedding: {embedding_type})")
        print("💡 支持的embedding类型: aliyun, doubao, openai")
        print("💡 使用方式:")
        print("   - 交互模式: python main.py --embedding aliyun")
        print("   - 服务器模式: python main.py --server --embedding aliyun")
        print("   - 单次查询: python main.py --embedding aliyun 'your query here'")
        asyncio.run(main(embedding_type=embedding_type))
