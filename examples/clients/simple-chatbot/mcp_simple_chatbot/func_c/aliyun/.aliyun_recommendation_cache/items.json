{"course_python_basic": {"item_id": "course_python_basic", "content": "Python编程基础课程：从零开始学习Python语法、数据类型、控制结构和函数编程", "metadata": {"category": "编程课程", "price": 199, "duration": "20小时", "level": "初级"}}, "course_ml_intro": {"item_id": "course_ml_intro", "content": "机器学习入门课程：学习监督学习、无监督学习算法，包括线性回归、决策树、聚类等", "metadata": {"category": "AI课程", "price": 299, "duration": "30小时", "level": "中级"}}, "course_deep_learning": {"item_id": "course_deep_learning", "content": "深度学习实战课程：神经网络原理、CNN、RNN、Transformer架构及实际项目应用", "metadata": {"category": "AI课程", "price": 399, "duration": "40小时", "level": "高级"}}, "course_data_science": {"item_id": "course_data_science", "content": "数据科学全栈课程：数据收集、清洗、分析、可视化，使用Python和R语言进行数据挖掘", "metadata": {"category": "数据科学课程", "price": 349, "duration": "35小时", "level": "中级"}}, "course_cloud_computing": {"item_id": "course_cloud_computing", "content": "云计算技术课程：AWS、Azure、阿里云平台使用，容器化部署和微服务架构", "metadata": {"category": "云计算课程", "price": 279, "duration": "25小时", "level": "中级"}}, "course_nlp": {"item_id": "course_nlp", "content": "自然语言处理课程：文本预处理、词向量、情感分析、机器翻译和对话系统开发", "metadata": {"category": "AI课程", "price": 329, "duration": "28小时", "level": "高级"}}, "course_blockchain": {"item_id": "course_blockchain", "content": "区块链开发课程：区块链原理、智能合约开发、DApp应用构建和加密货币技术", "metadata": {"category": "区块链课程", "price": 449, "duration": "32小时", "level": "高级"}}, "course_big_data": {"item_id": "course_big_data", "content": "大数据处理课程：Hadoop、Spark、Kafka等大数据技术栈，实时数据处理和分析", "metadata": {"category": "大数据课程", "price": 379, "duration": "38小时", "level": "高级"}}}