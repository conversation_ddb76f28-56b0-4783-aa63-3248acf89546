#!/usr/bin/env python3
"""
JSON格式输出功能演示
"""

import asyncio
import json
import sys
from pathlib import Path

# 添加模块路径
sys.path.append(str(Path(__file__).parent))

from main_simple import initialize_global_session, query_tools_json


async def demo_json_output():
    """演示JSON格式输出功能"""
    print("🚀 开始演示JSON格式输出...")
    await initialize_global_session(embedding_type="aliyun", use_api=True)
    # 初始化全局session
    print("🔄 正在初始化全局session...")

    print("✅ 全局session初始化完成")

    # 演示查询
    test_query = input("输入的内容")
    print(f"🔍 演示查询: {test_query}")

    # 获取JSON格式结果
    result = await query_tools_json(test_query, max_tools=5)

    # 打印结果
    print(f"\n📋 查询结果 (共{len(result)}个工具):")
    print(json.dumps(result, ensure_ascii=False, indent=2))

    # 显示格式信息
    if result:
        first_tool = result[0]
        print(f"\n📊 结果格式示例:")
        print(f"  • 工具名称: {first_tool.get('name')}")
        print(f"  • 工具描述: {first_tool.get('description')}")
        print(f"  • 参数数量: {len(first_tool.get('parameters', {}).get('properties', {}))}")
    else:
        print("❌ 没有找到工具")


# 直接运行演示
if __name__ == "__main__":
    asyncio.run(demo_json_output())