"""API-based server that provides tools directly without MCP server processes."""

import asyncio
import logging
from typing import Any, Dict, List

from .tool import Tool


class APIServer:
    """Server that provides tools from API data without actual MCP server processes."""

    def __init__(self, name: str, config: Dict[str, Any]) -> None:
        self.name: str = name
        self.config: Dict[str, Any] = config
        self.tools: List[Tool] = []
        self._initialized: bool = False

    async def initialize(self) -> None:
        """Initialize the API server with tool data."""
        try:
            # If this is an API-based server, create tools from the config
            if self.config.get("type") == "api":
                tool_data = {
                    "name": self.config.get("name", "unknown"),
                    "description": self.config.get("description", ""),
                    "inputSchema": self.config.get("parameters", {})
                }
                
                tool = Tool(
                    name=tool_data["name"],
                    description=tool_data["description"],
                    input_schema=tool_data["inputSchema"]
                )
                self.tools = [tool]
                self._initialized = True
                logging.info(f"API Server {self.name} initialized successfully")
            else:
                raise ValueError(f"Server {self.name} is not an API-type server")
                
        except Exception as e:
            logging.error(f"Error initializing API server {self.name}: {e}")
            raise

    async def list_tools(self) -> List[Tool]:
        """List available tools from the API server.

        Returns:
            A list of available tools.

        Raises:
            RuntimeError: If the server is not initialized.
        """
        if not self._initialized:
            raise RuntimeError(f"API Server {self.name} not initialized")

        return self.tools

    async def execute_tool(
        self,
        tool_name: str,
        arguments: Dict[str, Any],
        retries: int = 2,
        delay: float = 1.0,
    ) -> Any:
        """Execute a tool on the API server.

        For API-based tools, this is a simulation that returns the tool description
        and parameters, since we don't have actual tool execution endpoints.

        Args:
            tool_name: Name of the tool to execute.
            arguments: Arguments for the tool.
            retries: Number of retry attempts (unused for API tools).
            delay: Delay between retries in seconds (unused for API tools).

        Returns:
            Tool execution result (simulated).

        Raises:
            RuntimeError: If the server is not initialized.
            ValueError: If the tool is not found.
        """
        if not self._initialized:
            raise RuntimeError(f"API Server {self.name} not initialized")

        # Find the tool
        tool = None
        for t in self.tools:
            if t.name == tool_name:
                tool = t
                break

        if not tool:
            raise ValueError(f"Tool {tool_name} not found in server {self.name}")

        # For API tools, we return a simulated result
        logging.info(f"Simulating execution of {tool_name} on API server {self.name}")
        
        result = {
            "tool_name": tool_name,
            "server_name": self.name,
            "description": tool.description,
            "arguments": arguments,
            "status": "simulated",
            "message": f"Tool {tool_name} would be executed with arguments: {arguments}"
        }
        
        logging.info(f"API Tool {tool_name} executed successfully (simulated)")
        return result

    async def cleanup(self) -> None:
        """Clean up API server resources."""
        try:
            self.tools = []
            self._initialized = False
            logging.info(f"API Server {self.name} cleaned up successfully")
        except Exception as e:
            logging.error(f"Error during cleanup of API server {self.name}: {e}")

    def is_initialized(self) -> bool:
        """Check if the API server is initialized.

        Returns:
            True if initialized, False otherwise.
        """
        return self._initialized

    def __repr__(self) -> str:
        """String representation of the API server."""
        status = "initialized" if self.is_initialized() else "not initialized"
        return f"APIServer(name='{self.name}', status='{status}')"