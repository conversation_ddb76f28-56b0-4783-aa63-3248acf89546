"""API client for fetching project tools data from remote server."""

import json
import httpx
from typing import Dict, List, Any, Optional
import logging
from pathlib import Path


class ProjectToolsAPIClient:
    """Client for fetching project tools data from remote API."""
    
    def __init__(self, api_url: str = "https://www.mcpcn.cc/api/projectTools/getAllProjectToolsList"):
        """Initialize the API client.
        
        Args:
            api_url: The API endpoint URL
        """
        self.api_url = api_url
        self.logger = logging.getLogger(__name__)
    
    async def fetch_tools_data(self) -> List[Dict[str, Any]]:
        """Fetch tools data from the API.
        
        Returns:
            List of tool data dictionaries
            
        Raises:
            httpx.RequestError: If the HTTP request fails
            Exception: If the API response is invalid
        """
        try:
            async with httpx.AsyncClient(timeout=30) as client:
                self.logger.info(f"Fetching tools data from {self.api_url}")
                response = await client.post(self.api_url)
                response.raise_for_status()
                
                data = response.json()
                
                # Check if response has the expected structure
                if not isinstance(data, dict) or "code" not in data or "data" not in data:
                    raise ValueError("Invalid API response format")
                
                if data["code"] != 0:
                    raise ValueError(f"API returned error code {data['code']}: {data.get('msg', 'Unknown error')}")
                
                tools_data = data["data"]
                if not isinstance(tools_data, list):
                    raise ValueError("API data field is not a list")
                
                self.logger.info(f"Successfully fetched {len(tools_data)} tools")
                return tools_data
                
        except httpx.RequestError as e:
            self.logger.error(f"HTTP request failed: {e}")
            raise
        except Exception as e:
            self.logger.error(f"Failed to fetch tools data: {e}")
            raise
    
    def transform_to_tool_format(self, tools_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Transform API response data to the required tool format.
        
        Args:
            tools_data: Raw tools data from API
            
        Returns:
            List of transformed tool dictionaries
        """
        transformed_tools = []
        
        for tool in tools_data:
            try:
                # Extract required fields with fallbacks
                name = tool.get("name", "unknown_tool")
                description = tool.get("descriptionChinese", tool.get("description", ""))
                input_schema = tool.get("inputSchema", {})
                
                # Create transformed tool object
                transformed_tool = {
                    "name": name,
                    "description": description,
                    "parameters": input_schema
                }
                
                transformed_tools.append(transformed_tool)
                
            except Exception as e:
                self.logger.warning(f"Failed to transform tool {tool.get('name', 'unknown')}: {e}")
                continue
        
        self.logger.info(f"Successfully transformed {len(transformed_tools)} tools")
        return transformed_tools
    
    def generate_mock_server_config(self, tools_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate a mock server configuration that mimics the original servers_config.json format.
        
        This is used to maintain compatibility with the existing system while using API data.
        
        Args:
            tools_data: Transformed tools data
            
        Returns:
            Mock server configuration dictionary
        """
        mock_config = {
            "mcpServers": {}
        }
        
        for tool in tools_data:
            server_name = tool.get("name", "unknown_tool")
            
            # Create a mock server configuration
            mock_config["mcpServers"][server_name] = {
                "name": server_name,
                "type": "api",  # Mark as API-based
                "description": tool.get("description", ""),
                "parameters": tool.get("parameters", {}),
                "disabledTools": []
            }
        
        return mock_config
    
    async def get_server_config(self) -> Dict[str, Any]:
        """Get server configuration by fetching from API and transforming data.
        
        Returns:
            Server configuration dictionary compatible with existing system
        """
        try:
            # Fetch raw data from API
            raw_tools_data = await self.fetch_tools_data()
            
            # Transform to tool format
            transformed_tools = self.transform_to_tool_format(raw_tools_data)
            
            # Generate mock server config
            server_config = self.generate_mock_server_config(transformed_tools)
            
            return server_config
            
        except Exception as e:
            self.logger.error(f"Failed to get server config: {e}")
            raise
    
    async def get_tools_list(self) -> List[Dict[str, Any]]:
        """Get transformed tools list.
        
        Returns:
            List of transformed tools in the required format
        """
        try:
            # Fetch raw data from API
            raw_tools_data = await self.fetch_tools_data()
            
            # Transform to tool format
            transformed_tools = self.transform_to_tool_format(raw_tools_data)
            
            return transformed_tools
            
        except Exception as e:
            self.logger.error(f"Failed to get tools list: {e}")
            raise