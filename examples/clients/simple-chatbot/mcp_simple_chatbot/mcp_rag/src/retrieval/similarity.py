"""Similarity calculation utilities."""

import re
from typing import List
import numpy as np

try:
    from ..common.similarity_config import SimilarityConfig
except ImportError:
    # Fallback configuration if similarity_config is not available
    class SimilarityConfig:
        SEMANTIC_WEIGHT = 0.8
        KEYWORD_WEIGHT = 0.1
        NAME_MATCH_WEIGHT = 0.1
        STOP_WORDS = set()
        MIN_WORD_LENGTH = 2


class SimilarityCalculator:
    """Handles various similarity calculations for tool retrieval."""

    @staticmethod
    def cosine_similarity(vec1: List[float], vec2: List[float]) -> float:
        """Calculate optimized cosine similarity between two vectors.

        Args:
            vec1: First vector.
            vec2: Second vector.

        Returns:
            Cosine similarity score between 0 and 1.
        """
        vec1_np = np.array(vec1, dtype=np.float32)
        vec2_np = np.array(vec2, dtype=np.float32)

        dot_product = np.dot(vec1_np, vec2_np)
        norm1 = np.linalg.norm(vec1_np)
        norm2 = np.linalg.norm(vec2_np)

        if norm1 == 0 or norm2 == 0:
            return 0.0

        return float(dot_product / (norm1 * norm2))

    @staticmethod
    def extract_keywords(text: str) -> set:
        """Extract keywords from text with better Chinese support.
        
        Args:
            text: Input text in any language.
            
        Returns:
            Set of extracted keywords.
        """
        # Convert to lowercase
        text_lower = text.lower()
        
        # Extract English words (alphanumeric sequences, split by underscore)
        english_words = set()
        # First, split by underscores and other separators
        words = re.split(r'[_\s\-\.]+', text_lower)
        for word in words:
            # Then extract alphanumeric parts
            english_parts = re.findall(r'[a-zA-Z0-9]+', word)
            english_words.update(english_parts)
        
        # Extract Chinese characters (each character treated as a potential keyword)
        chinese_chars = set(re.findall(r'[\u4e00-\u9fff]', text_lower))
        
        # Combine all keywords
        all_keywords = english_words | chinese_chars
        
        # Filter out stop words and short words
        filtered_keywords = {w for w in all_keywords
                           if len(w) >= SimilarityConfig.MIN_WORD_LENGTH
                           and w not in SimilarityConfig.STOP_WORDS}
        
        return filtered_keywords

    @staticmethod
    def keyword_similarity(query: str, tool_name: str, tool_description: str) -> float:
        """Calculate keyword overlap similarity using Jaccard index.

        Args:
            query: The search query.
            tool_name: Name of the tool.
            tool_description: Description of the tool.

        Returns:
            Keyword similarity score between 0 and 1.
        """
        # Extract keywords using improved method
        query_words = SimilarityCalculator.extract_keywords(query)
        tool_text = f"{tool_name} {tool_description}"
        tool_words = SimilarityCalculator.extract_keywords(tool_text)

        if not query_words or not tool_words:
            return 0.0

        # Calculate Jaccard similarity (intersection over union)
        intersection = len(query_words & tool_words)
        union = len(query_words | tool_words)

        return intersection / union if union > 0 else 0.0

    @staticmethod
    def name_match_similarity(query: str, tool_name: str) -> float:
        """Calculate tool name matching score with improved Chinese-English support.

        Args:
            query: The search query.
            tool_name: Name of the tool.

        Returns:
            Name match similarity score between 0 and 1.
        """
        query_lower = query.lower()
        name_lower = tool_name.lower()

        # Exact substring match
        if name_lower in query_lower or query_lower in name_lower:
            return 1.0

        # Extract keywords using the improved method
        query_keywords = SimilarityCalculator.extract_keywords(query)
        name_keywords = SimilarityCalculator.extract_keywords(tool_name)
        
        # Check for direct keyword overlap
        common_keywords = query_keywords & name_keywords
        if common_keywords:
            overlap_ratio = len(common_keywords) / min(len(query_keywords), len(name_keywords))
            return min(overlap_ratio, 1.0)
        
        # Chinese-English semantic mapping for common terms
        # For this matching, we need to check the original query text for Chinese phrases
        cn_en_mapping = {
            # Query related
            '查询': ['query', 'search', 'get', 'find'],
            '获取': ['get', 'fetch', 'obtain', 'retrieve'],
            '搜索': ['search', 'find'],
            
            # Business related  
            '油价': ['oil', 'price'],
            '股票': ['stock', 'share'],
            '价格': ['price', 'cost'],
            '天气': ['weather'],
            '预报': ['forecast'],
            
            # Actions
            '创建': ['create', 'make', 'new'],
            '删除': ['delete', 'remove'],
            '更新': ['update', 'modify'],
            '编辑': ['edit', 'modify'],
            '移动': ['move', 'transfer'],
            '文件': ['file'],
            '操作': ['operation', 'action'],
            
            # Time related
            '今日': ['today', 'current'],
            '当前': ['current', 'now'],
            '实时': ['realtime', 'live'],
        }
        
        # Check semantic mapping by looking for Chinese phrases in the original query
        semantic_matches = 0
        total_mappings = 0
        
        for cn_phrase, en_equivalents in cn_en_mapping.items():
            if cn_phrase in query_lower:
                total_mappings += 1
                # Check if any English equivalent appears in the tool name
                if any(en_word in name_keywords for en_word in en_equivalents):
                    semantic_matches += 1
        
        if total_mappings > 0:
            semantic_score = semantic_matches / total_mappings
            return semantic_score * 0.7  # Reduce weight for semantic matching
        
        # Partial word match (handle underscore-separated tool names) - fallback
        name_words = set(name_lower.replace('_', ' ').split())
        query_words = set(query_lower.split())

        if name_words & query_words:
            return 0.3  # Lower score for simple word overlap

        return 0.0

    @staticmethod
    def hybrid_similarity(query: str, tool_name: str, tool_description: str,
                         query_vector: List[float], tool_vector: List[float]) -> float:
        """Calculate hybrid similarity combining semantic and keyword matching.

        Args:
            query: The search query.
            tool_name: Name of the tool.
            tool_description: Description of the tool.
            query_vector: Query vector representation.
            tool_vector: Tool vector representation.

        Returns:
            Combined similarity score between 0 and 1.
        """
        # 1. Semantic similarity (vector-based)
        semantic_sim = SimilarityCalculator.cosine_similarity(query_vector, tool_vector)

        # 2. Keyword similarity (text-based)
        keyword_sim = SimilarityCalculator.keyword_similarity(query, tool_name, tool_description)

        # 3. Name matching bonus
        name_match = SimilarityCalculator.name_match_similarity(query, tool_name)

        # Weighted combination using configurable weights
        final_score = (
            SimilarityConfig.SEMANTIC_WEIGHT * semantic_sim +
            SimilarityConfig.KEYWORD_WEIGHT * keyword_sim +
            SimilarityConfig.NAME_MATCH_WEIGHT * name_match
        )

        return min(final_score, 1.0)  # Ensure score doesn't exceed 1.0

    @staticmethod
    def get_similarity_breakdown(query: str, tool_name: str, tool_description: str,
                               query_vector: List[float], tool_vector: List[float]) -> dict:
        """Get detailed breakdown of similarity scores.

        Args:
            query: The search query.
            tool_name: Name of the tool.
            tool_description: Description of the tool.
            query_vector: Query vector representation.
            tool_vector: Tool vector representation.

        Returns:
            Dictionary with detailed similarity breakdown.
        """
        semantic_score = SimilarityCalculator.cosine_similarity(query_vector, tool_vector)
        keyword_score = SimilarityCalculator.keyword_similarity(query, tool_name, tool_description)
        name_match_score = SimilarityCalculator.name_match_similarity(query, tool_name)
        
        hybrid_score = SimilarityCalculator.hybrid_similarity(
            query, tool_name, tool_description, query_vector, tool_vector
        )

        return {
            "semantic_score": semantic_score,
            "keyword_score": keyword_score,
            "name_match_score": name_match_score,
            "hybrid_score": hybrid_score,
            "weights": {
                "semantic": SimilarityConfig.SEMANTIC_WEIGHT,
                "keyword": SimilarityConfig.KEYWORD_WEIGHT,
                "name_match": SimilarityConfig.NAME_MATCH_WEIGHT
            }
        }