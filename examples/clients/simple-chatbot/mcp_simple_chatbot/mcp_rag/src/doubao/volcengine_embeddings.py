"""
火山引擎文本向量化 API 集成
VolcEngine Text Embedding API Integration
"""

import asyncio
import json
import logging
import time
from typing import List, Optional, Dict, Any
import aiohttp
from langchain_core.embeddings import Embeddings


class VolcEngineEmbeddings(Embeddings):
    """火山引擎文本向量化 API 的 LangChain 兼容实现"""

    # 支持的模型列表（按优先级排序）
    SUPPORTED_MODELS = [
        "doubao-embedding-large",
        "doubao-embedding",
        "text-embedding-3-large",  # 可能的别名
        "embedding-large",         # 可能的简化名称
    ]

    def __init__(
        self,
        api_key: str,
        model: str = "doubao-embedding-large",
        base_url: str = "https://ark.cn-beijing.volces.com/api/v3/embeddings",
        dimensions: Optional[int] = None,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        timeout: int = 30,
        max_batch_size: int = 100,
        auto_fallback: bool = True,
        **kwargs
    ):
        """
        初始化火山引擎 Embeddings
        
        Args:
            api_key: 火山引擎 API 密钥
            model: 模型名称，默认为 "doubao-embedding-large" (支持4096维向量)
            base_url: API 基础 URL
            dimensions: 向量维度，None表示使用模型默认维度
                       - doubao-embedding-large: 支持 [4096, 2048, 1024, 512, 256]
                       - doubao-embedding: 支持 [2560, 2048] 等
            max_retries: 最大重试次数
            retry_delay: 重试延迟（秒）
            timeout: 请求超时时间（秒）
            max_batch_size: 批处理最大大小
            auto_fallback: 是否自动回退到其他可用模型
        """
        self.api_key = api_key
        self.model = model
        self.base_url = base_url
        self.dimensions = dimensions
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.timeout = timeout
        self.max_batch_size = max_batch_size
        self.auto_fallback = auto_fallback
        self._verified_model = None  # 已验证可用的模型
        
        # 设置请求头
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        
        # 统计信息
        self.total_requests = 0
        self.total_tokens = 0
        self.total_time = 0.0
        
        logging.info(f"🔥 VolcEngine Embeddings initialized with model: {self.model}")
        if self.dimensions:
            logging.info(f"📏 Vector dimensions set to: {self.dimensions}")
        else:
            logging.info(f"📏 Using default vector dimensions for model: {self.model}")

    async def _verify_model(self, model_name: str) -> bool:
        """验证模型是否可用"""
        try:
            test_payload = {
                "model": model_name,
                "input": ["test"],
                "encoding_format": "float"
            }

            if self.dimensions is not None:
                test_payload["dimensions"] = self.dimensions

            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
                async with session.post(
                    self.base_url,
                    headers=self.headers,
                    json=test_payload
                ) as response:
                    if response.status == 200:
                        logging.info(f"✅ Model {model_name} is available")
                        return True
                    elif response.status == 404:
                        logging.warning(f"❌ Model {model_name} not found or no access")
                        return False
                    else:
                        error_text = await response.text()
                        logging.warning(f"⚠️ Model {model_name} test failed: {response.status} - {error_text}")
                        return False
        except Exception as e:
            logging.warning(f"⚠️ Model {model_name} verification failed: {e}")
            return False

    async def _find_available_model(self) -> str:
        """查找可用的模型"""
        if self._verified_model:
            return self._verified_model

        # 首先尝试指定的模型
        if await self._verify_model(self.model):
            self._verified_model = self.model
            return self.model

        # 如果启用自动回退，尝试其他模型
        if self.auto_fallback:
            logging.info(f"🔄 Model {self.model} not available, trying fallback models...")
            for fallback_model in self.SUPPORTED_MODELS:
                if fallback_model != self.model:
                    if await self._verify_model(fallback_model):
                        logging.info(f"✅ Using fallback model: {fallback_model}")
                        self._verified_model = fallback_model
                        return fallback_model

        # 如果所有模型都不可用，抛出异常
        raise Exception(f"No available embedding models found. Tried: {[self.model] + self.SUPPORTED_MODELS}")
    
    async def _make_request(self, texts: List[str]) -> List[List[float]]:
        """发送请求到火山引擎 API"""
        # 获取可用的模型
        available_model = await self._find_available_model()

        payload = {
            "model": available_model,
            "input": texts,
            "encoding_format": "float"
        }

        # 如果指定了维度，添加到请求中
        if self.dimensions is not None:
            payload["dimensions"] = self.dimensions
        
        start_time = time.time()
        
        for attempt in range(self.max_retries):
            try:
                async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                    async with session.post(
                        self.base_url,
                        headers=self.headers,
                        json=payload
                    ) as response:
                        if response.status == 200:
                            result = await response.json()
                            
                            # 提取向量
                            embeddings = []
                            for item in result.get("data", []):
                                embeddings.append(item.get("embedding", []))
                            
                            # 更新统计信息
                            self.total_requests += 1
                            self.total_tokens += result.get("usage", {}).get("total_tokens", 0)
                            self.total_time += time.time() - start_time
                            
                            logging.debug(f"✅ VolcEngine API request successful, got {len(embeddings)} embeddings")
                            return embeddings
                        
                        elif response.status == 429:  # Rate limit
                            wait_time = self.retry_delay * (2 ** attempt)
                            logging.warning(f"⚠️ Rate limit hit, waiting {wait_time}s before retry {attempt + 1}/{self.max_retries}")
                            await asyncio.sleep(wait_time)
                            continue
                        
                        else:
                            error_text = await response.text()
                            logging.error(f"❌ VolcEngine API error {response.status}: {error_text}")
                            if attempt == self.max_retries - 1:
                                raise Exception(f"VolcEngine API error {response.status}: {error_text}")
                            
            except asyncio.TimeoutError:
                logging.warning(f"⏰ Request timeout, attempt {attempt + 1}/{self.max_retries}")
                if attempt == self.max_retries - 1:
                    raise Exception("Request timeout after all retries")
                await asyncio.sleep(self.retry_delay)
                
            except Exception as e:
                logging.error(f"❌ Request failed: {e}")
                if attempt == self.max_retries - 1:
                    raise
                await asyncio.sleep(self.retry_delay)
        
        raise Exception("All retry attempts failed")
    
    async def aembed_documents(self, texts: List[str]) -> List[List[float]]:
        """异步批量嵌入文档"""
        if not texts:
            return []
        
        # 分批处理
        all_embeddings = []
        for i in range(0, len(texts), self.max_batch_size):
            batch = texts[i:i + self.max_batch_size]
            batch_embeddings = await self._make_request(batch)
            all_embeddings.extend(batch_embeddings)
            
            # 避免请求过于频繁
            if i + self.max_batch_size < len(texts):
                await asyncio.sleep(0.1)
        
        return all_embeddings
    
    async def aembed_query(self, text: str) -> List[float]:
        """异步嵌入单个查询"""
        embeddings = await self._make_request([text])
        return embeddings[0] if embeddings else []
    
    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """同步批量嵌入文档"""
        return asyncio.run(self.aembed_documents(texts))
    
    def embed_query(self, text: str) -> List[float]:
        """同步嵌入单个查询"""
        return asyncio.run(self.aembed_query(text))
    
    def get_stats(self) -> Dict[str, Any]:
        """获取使用统计信息"""
        avg_time = self.total_time / self.total_requests if self.total_requests > 0 else 0
        return {
            "total_requests": self.total_requests,
            "total_tokens": self.total_tokens,
            "total_time": self.total_time,
            "avg_time_per_request": avg_time,
            "model": self.model
        }
    
    def print_stats(self):
        """打印使用统计信息"""
        stats = self.get_stats()
        print(f"\n🔥 VolcEngine Embeddings 使用统计:")
        print(f"   总请求数: {stats['total_requests']}")
        print(f"   总 Token 数: {stats['total_tokens']}")
        print(f"   总耗时: {stats['total_time']:.2f}s")
        print(f"   平均每请求耗时: {stats['avg_time_per_request']:.2f}s")
        print(f"   使用模型: {stats['model']}")


def create_volcengine_embeddings(
    api_key: str,
    model: str = "doubao-embedding-large",
    dimensions: Optional[int] = None,
    auto_optimize_dimensions: bool = False,
    **kwargs
) -> VolcEngineEmbeddings:
    """
    创建火山引擎 Embeddings 实例的便捷函数

    Args:
        api_key: 火山引擎 API 密钥
        model: 模型名称
        dimensions: 向量维度，None表示使用模型默认维度
        auto_optimize_dimensions: 是否根据使用场景自动优化维度
        **kwargs: 其他参数

    Returns:
        VolcEngineEmbeddings 实例
    """
    # 自动维度优化建议
    if auto_optimize_dimensions and dimensions is None:
        # 根据模型选择推荐维度
        if model == "doubao-embedding-large":
            # 对于大多数应用，2048维是性能和精度的良好平衡
            dimensions = 2048
            logging.info(f"🎯 Auto-optimized dimensions to {dimensions} for balanced performance")
        elif model == "doubao-embedding":
            dimensions = 2048
            logging.info(f"🎯 Auto-optimized dimensions to {dimensions} for standard model")

    return VolcEngineEmbeddings(api_key=api_key, model=model, dimensions=dimensions, **kwargs)


# 测试函数
async def test_volcengine_embeddings():
    """测试火山引擎 Embeddings"""
    # 注意：需要替换为实际的 API 密钥
    api_key = "your-volcengine-api-key"
    
    embeddings = VolcEngineEmbeddings(api_key=api_key)
    
    # 测试单个查询
    test_text = "这是一个测试文本"
    try:
        vector = await embeddings.aembed_query(test_text)
        print(f"✅ 测试成功，向量维度: {len(vector)}")
        print(f"向量前5个值: {vector[:5]}")
        
        # 打印统计信息
        embeddings.print_stats()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")


if __name__ == "__main__":
    asyncio.run(test_volcengine_embeddings())
