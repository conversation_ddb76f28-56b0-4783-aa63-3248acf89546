"""
基于阿里云text-embedding-v4的语义推荐系统
Semantic Recommendation System using Aliyun text-embedding-v4
"""

import asyncio
import json
import logging
import os
import pickle
import time
from typing import List, Dict, Any, Optional, Tuple, Set
from pathlib import Path
import numpy as np
import pandas as pd
from dataclasses import dataclass
from collections import defaultdict
from aliyun_embeddings import create_aliyun_embeddings


@dataclass
class RecommendationResult:
    """推荐结果数据类"""
    item_id: str
    content: str
    similarity: float
    metadata: Dict[str, Any]
    reason: str  # 推荐理由


@dataclass
class UserProfile:
    """用户画像数据类"""
    user_id: str
    interests: List[str]
    interaction_history: List[str]
    preference_vector: Optional[List[float]] = None


class SemanticRecommendationEngine:
    """语义推荐引擎"""
    
    def __init__(
        self,
        api_key: str,
        model: str = "text-embedding-v4",
        dimensions: int = 1024,
        cache_dir: str = ".semantic_recommendation_cache",
        recommendation_instruct: str = "Given user preferences and item descriptions, find the most relevant items for recommendation"
    ):
        """
        初始化语义推荐引擎
        
        Args:
            api_key: 阿里云API密钥
            model: embedding模型名称
            dimensions: 向量维度
            cache_dir: 缓存目录
            recommendation_instruct: 推荐指令，用于优化推荐效果
        """
        self.api_key = api_key
        self.model = model
        self.dimensions = dimensions
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        
        # 初始化embedding模型
        self.embeddings = create_aliyun_embeddings(
            api_key=api_key,
            model=model,
            dimensions=dimensions,
            instruct=recommendation_instruct
        )
        
        # 数据存储
        self.items: Dict[str, Dict[str, Any]] = {}  # item_id -> item_data
        self.item_embeddings: Dict[str, List[float]] = {}  # item_id -> embedding
        self.user_profiles: Dict[str, UserProfile] = {}  # user_id -> profile
        self.user_interactions: Dict[str, Set[str]] = defaultdict(set)  # user_id -> set of item_ids
        self.embedding_cache: Dict[str, List[float]] = {}
        
        # 缓存文件路径
        self.cache_file = self.cache_dir / "recommendation_cache.pkl"
        self.items_file = self.cache_dir / "items.json"
        self.users_file = self.cache_dir / "users.json"
        
        # 加载缓存
        self._load_cache()
        
        logging.info(f"🎯 Semantic Recommendation Engine initialized")
        logging.info(f"📊 Model: {model}, Dimensions: {dimensions}")

    def _load_cache(self):
        """加载缓存数据"""
        try:
            # 加载embedding缓存
            if self.cache_file.exists():
                with open(self.cache_file, 'rb') as f:
                    cache_data = pickle.load(f)
                    self.embedding_cache = cache_data.get('embedding_cache', {})
                    self.item_embeddings = cache_data.get('item_embeddings', {})
                logging.info(f"📥 Loaded {len(self.embedding_cache)} cached embeddings")
            
            # 加载物品数据
            if self.items_file.exists():
                with open(self.items_file, 'r', encoding='utf-8') as f:
                    self.items = json.load(f)
                logging.info(f"📥 Loaded {len(self.items)} items")
            
            # 加载用户数据
            if self.users_file.exists():
                with open(self.users_file, 'r', encoding='utf-8') as f:
                    user_data = json.load(f)
                    # 重建用户画像
                    for user_id, data in user_data.items():
                        profile = UserProfile(
                            user_id=user_id,
                            interests=data.get('interests', []),
                            interaction_history=data.get('interaction_history', []),
                            preference_vector=data.get('preference_vector')
                        )
                        self.user_profiles[user_id] = profile
                        self.user_interactions[user_id] = set(data.get('interaction_history', []))
                logging.info(f"📥 Loaded {len(self.user_profiles)} user profiles")
                
        except Exception as e:
            logging.warning(f"⚠️ Failed to load cache: {e}")

    def _save_cache(self):
        """保存缓存数据"""
        try:
            # 保存embedding缓存
            cache_data = {
                'embedding_cache': self.embedding_cache,
                'item_embeddings': self.item_embeddings
            }
            with open(self.cache_file, 'wb') as f:
                pickle.dump(cache_data, f)
            
            # 保存物品数据
            with open(self.items_file, 'w', encoding='utf-8') as f:
                json.dump(self.items, f, ensure_ascii=False, indent=2)
            
            # 保存用户数据
            user_data = {}
            for user_id, profile in self.user_profiles.items():
                user_data[user_id] = {
                    'interests': profile.interests,
                    'interaction_history': profile.interaction_history,
                    'preference_vector': profile.preference_vector
                }
            with open(self.users_file, 'w', encoding='utf-8') as f:
                json.dump(user_data, f, ensure_ascii=False, indent=2)
                
            logging.info(f"💾 Cache saved successfully")
            
        except Exception as e:
            logging.error(f"❌ Failed to save cache: {e}")

    async def _get_embedding(self, text: str) -> List[float]:
        """获取文本的embedding向量（带缓存）"""
        if text in self.embedding_cache:
            return self.embedding_cache[text]
        
        try:
            embedding = await self.embeddings.aembed_query(text)
            self.embedding_cache[text] = embedding
            return embedding
        except Exception as e:
            logging.error(f"❌ Failed to generate embedding: {e}")
            raise

    def cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """计算余弦相似度"""
        vec1_np = np.array(vec1, dtype=np.float32)
        vec2_np = np.array(vec2, dtype=np.float32)
        
        dot_product = np.dot(vec1_np, vec2_np)
        norm1 = np.linalg.norm(vec1_np)
        norm2 = np.linalg.norm(vec2_np)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        return float(dot_product / (norm1 * norm2))

    async def add_items(self, items: List[Dict[str, Any]], batch_size: int = 10):
        """
        添加物品到推荐系统
        
        Args:
            items: 物品列表，每个物品应包含'item_id', 'content'字段和可选的metadata
            batch_size: 批处理大小
        """
        logging.info(f"📦 Adding {len(items)} items to recommendation system...")
        
        new_items = {}
        new_embeddings = {}
        texts_to_embed = []
        item_ids = []
        
        for item in items:
            item_id = item['item_id']
            content = item['content']
            
            new_items[item_id] = item
            texts_to_embed.append(content)
            item_ids.append(item_id)
        
        # 批量生成embeddings
        try:
            embeddings_list = await self.embeddings.aembed_documents(texts_to_embed)
            
            for item_id, content, embedding in zip(item_ids, texts_to_embed, embeddings_list):
                new_embeddings[item_id] = embedding
                self.embedding_cache[content] = embedding
            
            # 更新数据
            self.items.update(new_items)
            self.item_embeddings.update(new_embeddings)
            
            # 保存缓存
            self._save_cache()
            
            logging.info(f"✅ Successfully added {len(new_items)} items")
            
        except Exception as e:
            logging.error(f"❌ Failed to add items: {e}")
            raise

    async def create_user_profile(self, user_id: str, interests: List[str], interaction_history: List[str] = None):
        """
        创建或更新用户画像
        
        Args:
            user_id: 用户ID
            interests: 用户兴趣列表
            interaction_history: 交互历史（物品ID列表）
        """
        if interaction_history is None:
            interaction_history = []
        
        # 生成用户偏好向量
        all_texts = interests + [self.items[item_id]['content'] for item_id in interaction_history if item_id in self.items]
        
        if all_texts:
            # 计算平均embedding作为用户偏好向量
            embeddings = await self.embeddings.aembed_documents(all_texts)
            preference_vector = np.mean(embeddings, axis=0).tolist()
        else:
            preference_vector = None
        
        # 创建用户画像
        profile = UserProfile(
            user_id=user_id,
            interests=interests,
            interaction_history=interaction_history,
            preference_vector=preference_vector
        )
        
        self.user_profiles[user_id] = profile
        self.user_interactions[user_id] = set(interaction_history)
        
        # 保存缓存
        self._save_cache()
        
        logging.info(f"👤 Created/updated profile for user {user_id}")

    async def recommend_items(
        self,
        user_id: str,
        top_k: int = 5,
        exclude_interacted: bool = True,
        min_similarity: float = 0.0,
        diversity_factor: float = 0.1
    ) -> List[RecommendationResult]:
        """
        为用户推荐物品
        
        Args:
            user_id: 用户ID
            top_k: 推荐物品数量
            exclude_interacted: 是否排除已交互的物品
            min_similarity: 最小相似度阈值
            diversity_factor: 多样性因子（0-1，越大越多样化）
            
        Returns:
            推荐结果列表
        """
        if user_id not in self.user_profiles:
            logging.warning(f"⚠️ User {user_id} not found")
            return []
        
        profile = self.user_profiles[user_id]
        if not profile.preference_vector:
            logging.warning(f"⚠️ User {user_id} has no preference vector")
            return []
        
        logging.info(f"🎯 Generating recommendations for user {user_id}")
        start_time = time.time()
        
        # 计算相似度
        similarities = []
        interacted_items = self.user_interactions.get(user_id, set())
        
        for item_id, item_embedding in self.item_embeddings.items():
            # 排除已交互的物品
            if exclude_interacted and item_id in interacted_items:
                continue
            
            similarity = self.cosine_similarity(profile.preference_vector, item_embedding)
            if similarity >= min_similarity:
                similarities.append((item_id, similarity))
        
        # 排序并应用多样性
        similarities.sort(key=lambda x: x[1], reverse=True)
        
        # 多样性选择算法
        selected_items = []
        remaining_items = similarities.copy()
        
        while len(selected_items) < top_k and remaining_items:
            if not selected_items:
                # 选择相似度最高的第一个物品
                item_id, similarity = remaining_items.pop(0)
                selected_items.append((item_id, similarity, "highest_similarity"))
            else:
                # 平衡相似度和多样性
                best_score = -1
                best_item = None
                best_idx = -1
                
                for i, (item_id, similarity) in enumerate(remaining_items):
                    # 计算与已选物品的平均相似度
                    item_embedding = self.item_embeddings[item_id]
                    avg_similarity_to_selected = np.mean([
                        self.cosine_similarity(item_embedding, self.item_embeddings[selected_id])
                        for selected_id, _, _ in selected_items
                    ])
                    
                    # 综合分数：相似度 - 多样性惩罚
                    score = similarity - diversity_factor * avg_similarity_to_selected
                    
                    if score > best_score:
                        best_score = score
                        best_item = (item_id, similarity, "diversity_balanced")
                        best_idx = i
                
                if best_item:
                    selected_items.append(best_item)
                    remaining_items.pop(best_idx)
                else:
                    break
        
        # 构建推荐结果
        results = []
        for item_id, similarity, reason in selected_items:
            item = self.items[item_id]
            result = RecommendationResult(
                item_id=item_id,
                content=item['content'],
                similarity=similarity,
                metadata=item.get('metadata', {}),
                reason=reason
            )
            results.append(result)
        
        recommendation_time = time.time() - start_time
        logging.info(f"✅ Generated {len(results)} recommendations in {recommendation_time:.3f}s")
        
        return results

    async def add_user_interaction(self, user_id: str, item_id: str):
        """添加用户交互记录"""
        if user_id in self.user_profiles and item_id in self.items:
            self.user_profiles[user_id].interaction_history.append(item_id)
            self.user_interactions[user_id].add(item_id)
            
            # 更新用户偏好向量
            await self.update_user_preference(user_id)
            
            logging.info(f"📝 Added interaction: user {user_id} -> item {item_id}")

    async def update_user_preference(self, user_id: str):
        """更新用户偏好向量"""
        if user_id not in self.user_profiles:
            return
        
        profile = self.user_profiles[user_id]
        all_texts = profile.interests + [
            self.items[item_id]['content'] 
            for item_id in profile.interaction_history 
            if item_id in self.items
        ]
        
        if all_texts:
            embeddings = await self.embeddings.aembed_documents(all_texts)
            profile.preference_vector = np.mean(embeddings, axis=0).tolist()
            self._save_cache()

    def print_recommendations(self, results: List[RecommendationResult], max_content_length: int = 150):
        """打印推荐结果"""
        if not results:
            print("❌ 没有找到推荐结果")
            return
        
        print(f"\n🎯 推荐 {len(results)} 个物品:")
        print("=" * 80)
        
        for i, result in enumerate(results, 1):
            content = result.content
            if len(content) > max_content_length:
                content = content[:max_content_length] + "..."
            
            print(f"\n{i}. 物品ID: {result.item_id}")
            print(f"   相似度: {result.similarity:.3f}")
            print(f"   内容: {content}")
            print(f"   推荐理由: {result.reason}")
            
            if result.metadata:
                print(f"   元数据: {result.metadata}")
        
        print("=" * 80)

    def get_stats(self) -> Dict[str, Any]:
        """获取推荐引擎统计信息"""
        embedding_stats = self.embeddings.get_stats()
        return {
            "total_items": len(self.items),
            "total_users": len(self.user_profiles),
            "cached_embeddings": len(self.embedding_cache),
            "model": self.model,
            "dimensions": self.dimensions,
            "embedding_stats": embedding_stats
        }

    def print_stats(self):
        """打印统计信息"""
        stats = self.get_stats()
        print(f"\n📊 语义推荐引擎统计:")
        print(f"   物品总数: {stats['total_items']}")
        print(f"   用户总数: {stats['total_users']}")
        print(f"   缓存embedding数: {stats['cached_embeddings']}")
        print(f"   模型: {stats['model']}")
        print(f"   向量维度: {stats['dimensions']}")


# 示例使用函数
async def demo_semantic_recommendation():
    """演示语义推荐功能"""
    # 注意：需要设置DASHSCOPE_API_KEY环境变量
    api_key = os.getenv("DASHSCOPE_API_KEY")
    if not api_key:
        print("❌ 请设置DASHSCOPE_API_KEY环境变量")
        return
    
    # 创建推荐引擎
    rec_engine = SemanticRecommendationEngine(
        api_key=api_key,
        model="text-embedding-v4",
        dimensions=1024
    )
    
    # 示例物品
    sample_items = [
        {
            "item_id": "book_1",
            "content": "Python编程从入门到精通，适合初学者学习编程基础",
            "metadata": {"category": "编程书籍", "price": 59.9}
        },
        {
            "item_id": "book_2", 
            "content": "机器学习实战，深入浅出讲解机器学习算法和应用",
            "metadata": {"category": "AI书籍", "price": 79.9}
        },
        {
            "item_id": "book_3",
            "content": "深度学习原理与实践，神经网络和深度学习详解",
            "metadata": {"category": "AI书籍", "price": 89.9}
        },
        {
            "item_id": "book_4",
            "content": "数据科学入门，统计学和数据分析基础知识",
            "metadata": {"category": "数据科学", "price": 69.9}
        },
        {
            "item_id": "book_5",
            "content": "云计算技术与应用，分布式系统和云服务详解",
            "metadata": {"category": "云计算", "price": 75.9}
        }
    ]
    
    # 添加物品
    await rec_engine.add_items(sample_items)
    
    # 创建用户画像
    await rec_engine.create_user_profile(
        user_id="user_1",
        interests=["Python编程", "机器学习", "数据分析"],
        interaction_history=["book_1"]  # 用户已经看过Python书籍
    )
    
    await rec_engine.create_user_profile(
        user_id="user_2", 
        interests=["深度学习", "神经网络", "AI算法"],
        interaction_history=[]
    )
    
    # 生成推荐
    users = ["user_1", "user_2"]
    for user_id in users:
        print(f"\n👤 为用户 {user_id} 生成推荐:")
        recommendations = await rec_engine.recommend_items(
            user_id=user_id,
            top_k=3,
            diversity_factor=0.2
        )
        rec_engine.print_recommendations(recommendations)
    
    # 模拟用户交互
    await rec_engine.add_user_interaction("user_1", "book_2")
    print(f"\n📝 用户user_1与book_2交互后的新推荐:")
    new_recommendations = await rec_engine.recommend_items("user_1", top_k=3)
    rec_engine.print_recommendations(new_recommendations)
    
    # 打印统计信息
    rec_engine.print_stats()


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(demo_semantic_recommendation())
