"""
阿里云text-embedding-v4语义搜索和推荐系统综合演示
Comprehensive Demo for Aliyun text-embedding-v4 Semantic Search and Recommendation
"""

import asyncio
import logging
import os
import pandas as pd
from typing import List, Dict, Any
from semantic_search import SemanticSearchEngine
from semantic_recommendation import SemanticRecommendationEngine


class AliyunSemanticSystem:
    """阿里云语义系统综合管理类"""
    
    def __init__(self, api_key: str, model: str = "text-embedding-v4", dimensions: int = 1024):
        """
        初始化语义系统
        
        Args:
            api_key: 阿里云API密钥
            model: embedding模型
            dimensions: 向量维度
        """
        self.api_key = api_key
        self.model = model
        self.dimensions = dimensions
        
        # 初始化搜索引擎
        self.search_engine = SemanticSearchEngine(
            api_key=api_key,
            model=model,
            dimensions=dimensions,
            cache_dir=".aliyun_search_cache",
            search_instruct="Given a web search query, retrieve relevant passages that answer the query"
        )
        
        # 初始化推荐引擎
        self.recommendation_engine = SemanticRecommendationEngine(
            api_key=api_key,
            model=model,
            dimensions=dimensions,
            cache_dir=".aliyun_recommendation_cache",
            recommendation_instruct="Given user preferences and item descriptions, find the most relevant items for recommendation"
        )
        
        logging.info(f"🚀 Aliyun Semantic System initialized with {model} ({dimensions}D)")

    async def load_sample_data(self):
        """加载示例数据"""
        print("📚 加载示例数据...")
        
        # 示例文档数据（用于搜索）
        search_documents = [
            {
                "content": "Python是一种解释型、面向对象、动态数据类型的高级程序设计语言。Python语法简洁清晰，具有丰富和强大的库。",
                "metadata": {"category": "编程语言", "difficulty": "初级", "type": "教程"}
            },
            {
                "content": "机器学习是人工智能的一个分支，它使计算机能够在没有明确编程的情况下学习。机器学习算法构建数学模型，基于训练数据进行预测或决策。",
                "metadata": {"category": "人工智能", "difficulty": "中级", "type": "概念"}
            },
            {
                "content": "深度学习是机器学习的一个子集，它模仿人脑神经网络的工作方式。深度学习在图像识别、自然语言处理等领域取得了突破性进展。",
                "metadata": {"category": "人工智能", "difficulty": "高级", "type": "技术"}
            },
            {
                "content": "数据科学是一个跨学科领域，使用科学方法、过程、算法和系统从结构化和非结构化数据中提取知识和见解。",
                "metadata": {"category": "数据科学", "difficulty": "中级", "type": "概念"}
            },
            {
                "content": "云计算是一种通过互联网提供计算服务的模式，包括服务器、存储、数据库、网络、软件等。云计算具有弹性、可扩展性和成本效益。",
                "metadata": {"category": "云计算", "difficulty": "初级", "type": "概念"}
            },
            {
                "content": "自然语言处理(NLP)是人工智能的一个分支，帮助计算机理解、解释和生成人类语言。NLP应用包括机器翻译、情感分析、聊天机器人等。",
                "metadata": {"category": "人工智能", "difficulty": "中级", "type": "技术"}
            },
            {
                "content": "区块链是一种分布式账本技术，通过密码学方法确保数据的安全性和不可篡改性。区块链技术在加密货币、供应链管理等领域有广泛应用。",
                "metadata": {"category": "区块链", "difficulty": "高级", "type": "技术"}
            },
            {
                "content": "大数据是指传统数据处理应用软件不足以处理的大或复杂的数据集。大数据技术包括数据收集、存储、分析和可视化。",
                "metadata": {"category": "大数据", "difficulty": "中级", "type": "概念"}
            }
        ]
        
        # 示例商品数据（用于推荐）
        recommendation_items = [
            {
                "item_id": "course_python_basic",
                "content": "Python编程基础课程：从零开始学习Python语法、数据类型、控制结构和函数编程",
                "metadata": {"category": "编程课程", "price": 199, "duration": "20小时", "level": "初级"}
            },
            {
                "item_id": "course_ml_intro",
                "content": "机器学习入门课程：学习监督学习、无监督学习算法，包括线性回归、决策树、聚类等",
                "metadata": {"category": "AI课程", "price": 299, "duration": "30小时", "level": "中级"}
            },
            {
                "item_id": "course_deep_learning",
                "content": "深度学习实战课程：神经网络原理、CNN、RNN、Transformer架构及实际项目应用",
                "metadata": {"category": "AI课程", "price": 399, "duration": "40小时", "level": "高级"}
            },
            {
                "item_id": "course_data_science",
                "content": "数据科学全栈课程：数据收集、清洗、分析、可视化，使用Python和R语言进行数据挖掘",
                "metadata": {"category": "数据科学课程", "price": 349, "duration": "35小时", "level": "中级"}
            },
            {
                "item_id": "course_cloud_computing",
                "content": "云计算技术课程：AWS、Azure、阿里云平台使用，容器化部署和微服务架构",
                "metadata": {"category": "云计算课程", "price": 279, "duration": "25小时", "level": "中级"}
            },
            {
                "item_id": "course_nlp",
                "content": "自然语言处理课程：文本预处理、词向量、情感分析、机器翻译和对话系统开发",
                "metadata": {"category": "AI课程", "price": 329, "duration": "28小时", "level": "高级"}
            },
            {
                "item_id": "course_blockchain",
                "content": "区块链开发课程：区块链原理、智能合约开发、DApp应用构建和加密货币技术",
                "metadata": {"category": "区块链课程", "price": 449, "duration": "32小时", "level": "高级"}
            },
            {
                "item_id": "course_big_data",
                "content": "大数据处理课程：Hadoop、Spark、Kafka等大数据技术栈，实时数据处理和分析",
                "metadata": {"category": "大数据课程", "price": 379, "duration": "38小时", "level": "高级"}
            }
        ]
        
        # 加载数据到系统
        await self.search_engine.add_documents(search_documents)
        await self.recommendation_engine.add_items(recommendation_items)
        
        print("✅ 示例数据加载完成")

    async def create_sample_users(self):
        """创建示例用户"""
        print("👥 创建示例用户...")
        
        # 用户1：编程初学者
        await self.recommendation_engine.create_user_profile(
            user_id="beginner_programmer",
            interests=["Python编程", "编程基础", "软件开发"],
            interaction_history=["course_python_basic"]
        )
        
        # 用户2：AI爱好者
        await self.recommendation_engine.create_user_profile(
            user_id="ai_enthusiast",
            interests=["机器学习", "深度学习", "人工智能", "神经网络"],
            interaction_history=["course_ml_intro"]
        )
        
        # 用户3：数据科学家
        await self.recommendation_engine.create_user_profile(
            user_id="data_scientist",
            interests=["数据分析", "数据挖掘", "统计学", "大数据"],
            interaction_history=["course_data_science", "course_big_data"]
        )
        
        print("✅ 示例用户创建完成")

    async def demo_semantic_search(self):
        """演示语义搜索功能"""
        print("\n" + "="*60)
        print("🔍 语义搜索演示")
        print("="*60)
        
        search_queries = [
            "如何学习编程语言",
            "人工智能和机器学习的区别",
            "什么是神经网络",
            "云服务的优势",
            "数据分析方法"
        ]
        
        for query in search_queries:
            print(f"\n🔍 搜索查询: '{query}'")
            results = await self.search_engine.search(query, top_k=3, min_similarity=0.1)
            
            if results:
                print(f"找到 {len(results)} 个相关结果:")
                for i, result in enumerate(results, 1):
                    content = result.content[:100] + "..." if len(result.content) > 100 else result.content
                    print(f"  {i}. 相似度: {result.similarity:.3f}")
                    print(f"     内容: {content}")
                    print(f"     类别: {result.metadata.get('category', 'N/A')}")
            else:
                print("  未找到相关结果")

    async def demo_semantic_recommendation(self):
        """演示语义推荐功能"""
        print("\n" + "="*60)
        print("🎯 语义推荐演示")
        print("="*60)
        
        users = ["beginner_programmer", "ai_enthusiast", "data_scientist"]
        
        for user_id in users:
            print(f"\n👤 为用户 '{user_id}' 生成推荐:")
            
            # 获取用户画像信息
            if user_id in self.recommendation_engine.user_profiles:
                profile = self.recommendation_engine.user_profiles[user_id]
                print(f"   兴趣: {', '.join(profile.interests)}")
                print(f"   历史交互: {', '.join(profile.interaction_history)}")
            
            # 生成推荐
            recommendations = await self.recommendation_engine.recommend_items(
                user_id=user_id,
                top_k=3,
                diversity_factor=0.2
            )
            
            if recommendations:
                print(f"   推荐课程:")
                for i, rec in enumerate(recommendations, 1):
                    print(f"     {i}. {rec.item_id} (相似度: {rec.similarity:.3f})")
                    print(f"        {rec.content[:80]}...")
                    print(f"        价格: ¥{rec.metadata.get('price', 'N/A')} | 时长: {rec.metadata.get('duration', 'N/A')}")
            else:
                print("   未找到推荐结果")

    async def demo_user_interaction(self):
        """演示用户交互和动态推荐"""
        print("\n" + "="*60)
        print("📝 用户交互演示")
        print("="*60)
        
        user_id = "beginner_programmer"
        new_item = "course_ml_intro"
        
        print(f"👤 用户 '{user_id}' 与课程 '{new_item}' 发生交互")
        
        # 交互前的推荐
        print("\n交互前的推荐:")
        before_recommendations = await self.recommendation_engine.recommend_items(user_id, top_k=3)
        for i, rec in enumerate(before_recommendations, 1):
            print(f"  {i}. {rec.item_id} (相似度: {rec.similarity:.3f})")
        
        # 添加交互
        await self.recommendation_engine.add_user_interaction(user_id, new_item)
        
        # 交互后的推荐
        print("\n交互后的推荐:")
        after_recommendations = await self.recommendation_engine.recommend_items(user_id, top_k=3)
        for i, rec in enumerate(after_recommendations, 1):
            print(f"  {i}. {rec.item_id} (相似度: {rec.similarity:.3f})")

    async def print_system_stats(self):
        """打印系统统计信息"""
        print("\n" + "="*60)
        print("📊 系统统计信息")
        print("="*60)
        
        # 搜索引擎统计
        search_stats = self.search_engine.get_stats()
        print(f"\n🔍 搜索引擎:")
        print(f"   文档数量: {search_stats['total_documents']}")
        print(f"   缓存embedding数: {search_stats['cached_embeddings']}")
        
        # 推荐引擎统计
        rec_stats = self.recommendation_engine.get_stats()
        print(f"\n🎯 推荐引擎:")
        print(f"   物品数量: {rec_stats['total_items']}")
        print(f"   用户数量: {rec_stats['total_users']}")
        print(f"   缓存embedding数: {rec_stats['cached_embeddings']}")
        
        # Embedding统计
        embedding_stats = search_stats['embedding_stats']
        print(f"\n🔥 Embedding统计:")
        print(f"   总请求数: {embedding_stats['total_requests']}")
        print(f"   总Token数: {embedding_stats['total_tokens']}")
        print(f"   总耗时: {embedding_stats['total_time']:.2f}s")
        print(f"   模型: {embedding_stats['model']}")
        print(f"   维度: {embedding_stats['dimensions']}")

    async def run_full_demo(self):
        """运行完整演示"""
        print("🚀 阿里云text-embedding-v4语义搜索和推荐系统演示")
        print("="*80)
        
        try:
            # 1. 加载数据
            await self.load_sample_data()
            
            # 2. 创建用户
            await self.create_sample_users()
            
            # 3. 演示搜索
            await self.demo_semantic_search()
            
            # 4. 演示推荐
            await self.demo_semantic_recommendation()
            
            # 5. 演示用户交互
            await self.demo_user_interaction()
            
            # 6. 显示统计信息
            await self.print_system_stats()
            
            print("\n✅ 演示完成！")
            
        except Exception as e:
            print(f"❌ 演示过程中出现错误: {e}")
            logging.error(f"Demo error: {e}", exc_info=True)


async def main():
    """主函数"""
    # 检查API密钥
    api_key = "sk-fcac337c29fe4d6f93bb9ff2ca2395d8"
    if not api_key:
        print("❌ 请设置DASHSCOPE_API_KEY环境变量")
        print("   export DASHSCOPE_API_KEY='your-api-key-here'")
        return
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # 创建并运行演示系统
    semantic_system = AliyunSemanticSystem(
        api_key=api_key,
        model="text-embedding-v4",
        dimensions=1024
    )
    
    await semantic_system.run_full_demo()


if __name__ == "__main__":
    asyncio.run(main())
