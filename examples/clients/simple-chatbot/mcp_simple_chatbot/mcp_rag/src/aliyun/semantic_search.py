"""
基于阿里云text-embedding-v4的语义搜索系统
Semantic Search System using Aliyun text-embedding-v4
"""

import asyncio
import json
import logging
import os
import pickle
import time
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import numpy as np
import pandas as pd
from dataclasses import dataclass
from aliyun_embeddings import create_aliyun_embeddings


@dataclass
class SearchResult:
    """搜索结果数据类"""
    content: str
    similarity: float
    metadata: Dict[str, Any]
    index: int


class SemanticSearchEngine:
    """语义搜索引擎"""
    
    def __init__(
        self,
        api_key: str,
        model: str = "text-embedding-v4",
        dimensions: int = 1024,
        cache_dir: str = ".semantic_search_cache",
        search_instruct: str = "Given a web search query, retrieve relevant passages that answer the query"
    ):
        """
        初始化语义搜索引擎
        
        Args:
            api_key: 阿里云API密钥
            model: embedding模型名称
            dimensions: 向量维度
            cache_dir: 缓存目录
            search_instruct: 搜索指令，用于优化搜索效果
        """
        self.api_key = api_key
        self.model = model
        self.dimensions = dimensions
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        
        # 初始化embedding模型
        self.embeddings = create_aliyun_embeddings(
            api_key=api_key,
            model=model,
            dimensions=dimensions,
            instruct=search_instruct
        )
        
        # 数据存储
        self.documents: List[Dict[str, Any]] = []
        self.document_embeddings: List[List[float]] = []
        self.embedding_cache: Dict[str, List[float]] = {}
        
        # 缓存文件路径
        self.cache_file = self.cache_dir / "embeddings_cache.pkl"
        self.documents_file = self.cache_dir / "documents.json"
        
        # 加载缓存
        self._load_cache()
        
        logging.info(f"🔍 Semantic Search Engine initialized")
        logging.info(f"📊 Model: {model}, Dimensions: {dimensions}")

    def _load_cache(self):
        """加载缓存数据"""
        try:
            # 加载embedding缓存
            if self.cache_file.exists():
                with open(self.cache_file, 'rb') as f:
                    self.embedding_cache = pickle.load(f)
                logging.info(f"📥 Loaded {len(self.embedding_cache)} cached embeddings")
            
            # 加载文档数据
            if self.documents_file.exists():
                with open(self.documents_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.documents = data.get('documents', [])
                    self.document_embeddings = data.get('embeddings', [])
                logging.info(f"📥 Loaded {len(self.documents)} documents")
                
        except Exception as e:
            logging.warning(f"⚠️ Failed to load cache: {e}")
            self.embedding_cache = {}
            self.documents = []
            self.document_embeddings = []

    def _save_cache(self):
        """保存缓存数据"""
        try:
            # 保存embedding缓存
            with open(self.cache_file, 'wb') as f:
                pickle.dump(self.embedding_cache, f)
            
            # 保存文档数据
            with open(self.documents_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'documents': self.documents,
                    'embeddings': self.document_embeddings
                }, f, ensure_ascii=False, indent=2)
                
            logging.info(f"💾 Cache saved successfully")
            
        except Exception as e:
            logging.error(f"❌ Failed to save cache: {e}")

    async def _get_embedding(self, text: str) -> List[float]:
        """获取文本的embedding向量（带缓存）"""
        # 检查缓存
        if text in self.embedding_cache:
            return self.embedding_cache[text]
        
        # 生成新的embedding
        try:
            embedding = await self.embeddings.aembed_query(text)
            self.embedding_cache[text] = embedding
            return embedding
        except Exception as e:
            logging.error(f"❌ Failed to generate embedding: {e}")
            raise

    def cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """计算余弦相似度"""
        vec1_np = np.array(vec1, dtype=np.float32)
        vec2_np = np.array(vec2, dtype=np.float32)
        
        dot_product = np.dot(vec1_np, vec2_np)
        norm1 = np.linalg.norm(vec1_np)
        norm2 = np.linalg.norm(vec2_np)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        return float(dot_product / (norm1 * norm2))

    async def add_documents(self, documents: List[Dict[str, Any]], batch_size: int = 10):
        """
        添加文档到搜索索引
        
        Args:
            documents: 文档列表，每个文档应包含'content'字段和可选的metadata
            batch_size: 批处理大小
        """
        logging.info(f"📚 Adding {len(documents)} documents to search index...")
        
        new_embeddings = []
        processed_count = 0
        
        for i in range(0, len(documents), batch_size):
            batch = documents[i:i + batch_size]
            batch_texts = [doc['content'] for doc in batch]
            
            try:
                # 批量生成embeddings
                batch_embeddings = await self.embeddings.aembed_documents(batch_texts)
                new_embeddings.extend(batch_embeddings)
                
                # 更新缓存
                for text, embedding in zip(batch_texts, batch_embeddings):
                    self.embedding_cache[text] = embedding
                
                processed_count += len(batch)
                logging.info(f"📊 Processed {processed_count}/{len(documents)} documents")
                
                # 避免请求过于频繁
                if i + batch_size < len(documents):
                    await asyncio.sleep(0.2)
                    
            except Exception as e:
                logging.error(f"❌ Failed to process batch {i//batch_size + 1}: {e}")
                continue
        
        # 添加到文档列表
        self.documents.extend(documents)
        self.document_embeddings.extend(new_embeddings)
        
        # 保存缓存
        self._save_cache()
        
        logging.info(f"✅ Successfully added {len(new_embeddings)} documents to search index")

    async def search(
        self, 
        query: str, 
        top_k: int = 5,
        min_similarity: float = 0.0
    ) -> List[SearchResult]:
        """
        执行语义搜索
        
        Args:
            query: 搜索查询
            top_k: 返回结果数量
            min_similarity: 最小相似度阈值
            
        Returns:
            搜索结果列表
        """
        if not self.documents:
            logging.warning("⚠️ No documents in search index")
            return []
        
        logging.info(f"🔍 Searching for: '{query}'")
        start_time = time.time()
        
        try:
            # 获取查询的embedding
            query_embedding = await self._get_embedding(query)
            
            # 计算相似度
            similarities = []
            for i, doc_embedding in enumerate(self.document_embeddings):
                similarity = self.cosine_similarity(query_embedding, doc_embedding)
                if similarity >= min_similarity:
                    similarities.append((i, similarity))
            
            # 按相似度排序
            similarities.sort(key=lambda x: x[1], reverse=True)
            
            # 构建结果
            results = []
            for i, (doc_idx, similarity) in enumerate(similarities[:top_k]):
                document = self.documents[doc_idx]
                result = SearchResult(
                    content=document['content'],
                    similarity=similarity,
                    metadata=document.get('metadata', {}),
                    index=doc_idx
                )
                results.append(result)
            
            search_time = time.time() - start_time
            logging.info(f"✅ Search completed in {search_time:.3f}s, found {len(results)} results")
            
            return results
            
        except Exception as e:
            logging.error(f"❌ Search failed: {e}")
            return []

    def print_search_results(self, results: List[SearchResult], max_content_length: int = 200):
        """打印搜索结果"""
        if not results:
            print("❌ 没有找到相关结果")
            return
        
        print(f"\n🎯 找到 {len(results)} 个相关结果:")
        print("=" * 80)
        
        for i, result in enumerate(results, 1):
            content = result.content
            if len(content) > max_content_length:
                content = content[:max_content_length] + "..."
            
            print(f"\n{i}. 相似度: {result.similarity:.3f}")
            print(f"   内容: {content}")
            
            if result.metadata:
                print(f"   元数据: {result.metadata}")
        
        print("=" * 80)

    async def load_from_csv(self, csv_file: str, content_column: str, metadata_columns: List[str] = None):
        """
        从CSV文件加载文档
        
        Args:
            csv_file: CSV文件路径
            content_column: 内容列名
            metadata_columns: 元数据列名列表
        """
        try:
            df = pd.read_csv(csv_file)
            logging.info(f"📄 Loading documents from {csv_file}")
            
            documents = []
            for _, row in df.iterrows():
                doc = {
                    'content': str(row[content_column]),
                    'metadata': {}
                }
                
                # 添加元数据
                if metadata_columns:
                    for col in metadata_columns:
                        if col in row:
                            doc['metadata'][col] = row[col]
                
                documents.append(doc)
            
            await self.add_documents(documents)
            logging.info(f"✅ Successfully loaded {len(documents)} documents from CSV")
            
        except Exception as e:
            logging.error(f"❌ Failed to load CSV: {e}")
            raise

    def get_stats(self) -> Dict[str, Any]:
        """获取搜索引擎统计信息"""
        embedding_stats = self.embeddings.get_stats()
        return {
            "total_documents": len(self.documents),
            "cached_embeddings": len(self.embedding_cache),
            "model": self.model,
            "dimensions": self.dimensions,
            "embedding_stats": embedding_stats
        }

    def print_stats(self):
        """打印统计信息"""
        stats = self.get_stats()
        print(f"\n📊 语义搜索引擎统计:")
        print(f"   文档总数: {stats['total_documents']}")
        print(f"   缓存embedding数: {stats['cached_embeddings']}")
        print(f"   模型: {stats['model']}")
        print(f"   向量维度: {stats['dimensions']}")
        
        embedding_stats = stats['embedding_stats']
        print(f"\n🔥 Embedding统计:")
        print(f"   总请求数: {embedding_stats['total_requests']}")
        print(f"   总Token数: {embedding_stats['total_tokens']}")
        print(f"   总耗时: {embedding_stats['total_time']:.2f}s")


# 示例使用函数
async def demo_semantic_search():
    """演示语义搜索功能"""
    # 注意：需要设置DASHSCOPE_API_KEY环境变量
    api_key = os.getenv("DASHSCOPE_API_KEY")
    if not api_key:
        print("❌ 请设置DASHSCOPE_API_KEY环境变量")
        return
    
    # 创建搜索引擎
    search_engine = SemanticSearchEngine(
        api_key=api_key,
        model="text-embedding-v4",
        dimensions=1024
    )
    
    # 示例文档
    sample_documents = [
        {
            "content": "Python是一种高级编程语言，具有简洁的语法和强大的功能",
            "metadata": {"category": "编程", "language": "中文"}
        },
        {
            "content": "机器学习是人工智能的一个分支，通过算法让计算机从数据中学习",
            "metadata": {"category": "AI", "language": "中文"}
        },
        {
            "content": "深度学习使用神经网络来模拟人脑的学习过程",
            "metadata": {"category": "AI", "language": "中文"}
        },
        {
            "content": "数据科学结合了统计学、计算机科学和领域专业知识",
            "metadata": {"category": "数据科学", "language": "中文"}
        },
        {
            "content": "云计算提供了可扩展的计算资源和服务",
            "metadata": {"category": "云计算", "language": "中文"}
        }
    ]
    
    # 添加文档
    await search_engine.add_documents(sample_documents)
    
    # 执行搜索
    queries = [
        "编程语言",
        "人工智能算法", 
        "神经网络",
        "数据分析"
    ]
    
    for query in queries:
        print(f"\n🔍 搜索查询: '{query}'")
        results = await search_engine.search(query, top_k=3)
        search_engine.print_search_results(results)
    
    # 打印统计信息
    search_engine.print_stats()


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(demo_semantic_search())
