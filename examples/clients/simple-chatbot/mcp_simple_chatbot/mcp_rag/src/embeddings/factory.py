"""Embeddings factory for creating embedding instances."""

import logging
from typing import Any, Dict

from langchain.embeddings import init_embeddings

# Import custom embedding providers
try:
    from ..aliyun.aliyun_embeddings import create_aliyun_embeddings
    ALIYUN_AVAILABLE = True
except ImportError:
    logging.warning("Aliyun embeddings not available")
    ALIYUN_AVAILABLE = False

try:
    from ..doubao.volcengine_embeddings import create_volcengine_embeddings
    VOLCENGINE_AVAILABLE = True
except ImportError:
    logging.warning("VolcEngine embeddings not available")
    VOLCENGINE_AVAILABLE = False


class EmbeddingsFactory:
    """Factory class for creating embeddings instances."""

    @staticmethod
    def create_embeddings(provider: str, config: Dict[str, Any], api_key: str):
        """Create embeddings instance for the specified provider.

        Args:
            provider: The embedding provider name (aliyun, volcengine, openai).
            config: Configuration dictionary for the provider.
            api_key: API key for the provider.

        Returns:
            Embeddings instance.

        Raises:
            ValueError: If provider is not supported or unavailable.
            Exception: If embeddings creation fails.
        """
        try:
            if provider == "aliyun":
                return EmbeddingsFactory._create_aliyun_embeddings(config, api_key)
            elif provider == "volcengine":
                return EmbeddingsFactory._create_volcengine_embeddings(config, api_key)
            elif provider == "openai":
                return EmbeddingsFactory._create_openai_embeddings(config, api_key)
            else:
                raise ValueError(f"Unsupported embedding provider: {provider}")
        except Exception as e:
            logging.error(f"Failed to create {provider} embeddings: {e}")
            raise

    @staticmethod
    def _create_aliyun_embeddings(config: Dict[str, Any], api_key: str):
        """Create Aliyun embeddings instance."""
        if not ALIYUN_AVAILABLE:
            raise ValueError("Aliyun embeddings not available")

        logging.info(f"🔥 Creating Aliyun embeddings with model {config.get('model')}")
        return create_aliyun_embeddings(
            api_key=api_key,
            model=config.get('model', 'text-embedding-v4'),
            dimensions=config.get('dimensions', 1024),
            instruct=config.get('instruct', 
                'Given a web search query, retrieve relevant passages that answer the query'),
            max_batch_size=config.get('max_batch_size', 10),
            timeout=config.get('timeout', 30)
        )

    @staticmethod
    def _create_volcengine_embeddings(config: Dict[str, Any], api_key: str):
        """Create VolcEngine embeddings instance."""
        if not VOLCENGINE_AVAILABLE:
            raise ValueError("VolcEngine embeddings not available")

        logging.info(f"🔄 Creating VolcEngine embeddings with model {config.get('model')}")
        return create_volcengine_embeddings(
            api_key=api_key,
            model=config.get('model', 'doubao-embedding-large-text-250515'),
            dimensions=config.get('dimensions', 2048),
            max_batch_size=config.get('max_batch_size', 50),
            timeout=config.get('timeout', 30)
        )

    @staticmethod
    def _create_openai_embeddings(config: Dict[str, Any], api_key: str):
        """Create OpenAI embeddings instance."""
        model = config.get('model', 'text-embedding-3-large')
        logging.info(f"🔄 Creating OpenAI embeddings with model {model}")
        
        # Use LangChain's init_embeddings for OpenAI
        return init_embeddings(f"openai:{model}")

    @staticmethod
    def get_supported_providers() -> list[str]:
        """Get list of supported embedding providers.

        Returns:
            List of supported provider names.
        """
        providers = ["openai"]  # OpenAI is always available via LangChain
        
        if ALIYUN_AVAILABLE:
            providers.append("aliyun")
        
        if VOLCENGINE_AVAILABLE:
            providers.append("volcengine")
            
        return providers

    @staticmethod
    def create_with_fallback(preferred_provider: str, config: Dict[str, Any], 
                           api_key: str, fallback_providers: list[str] = None):
        """Create embeddings with fallback support.

        Args:
            preferred_provider: The preferred embedding provider.
            config: Configuration dictionary.
            api_key: API key (will use appropriate key from config for fallbacks).
            fallback_providers: List of fallback providers to try.

        Returns:
            Embeddings instance and the provider that was actually used.

        Raises:
            Exception: If all providers fail.
        """
        if fallback_providers is None:
            fallback_providers = ["openai"]  # Default fallback

        providers_to_try = [preferred_provider] + fallback_providers
        
        for provider in providers_to_try:
            try:
                embeddings = EmbeddingsFactory.create_embeddings(provider, config, api_key)
                logging.info(f"✅ Successfully created {provider} embeddings")
                return embeddings, provider
            except Exception as e:
                logging.warning(f"Failed to create {provider} embeddings: {e}")
                continue
        
        raise Exception(f"Failed to create embeddings with any provider: {providers_to_try}")