#!/usr/bin/env python3
"""
测试火山引擎 embedding 模型可用性
Test VolcEngine embedding model availability
"""

import asyncio
import os
import logging
from volcengine_embeddings import VolcEngineEmbeddings

# 配置日志
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

async def test_models():
    """测试不同的模型"""
    # 从环境变量获取 API Key
    api_key = os.getenv("VOLCENGINE_API_KEY", "your-volcengine-api-key-here")
    
    if api_key == "your-volcengine-api-key-here":
        print("❌ 请设置 VOLCENGINE_API_KEY 环境变量")
        return
    
    # 要测试的模型列表
    models_to_test = [
        "doubao-embedding-large",
        "doubao-embedding",
        "text-embedding-3-large",
        "embedding-large",
        "doubao-embedding-text-240715",
        "doubao-embedding-text-240515",
        "doubao-embedding-large-text-240915",
        "doubao-embedding-large-text-250515",
    ]
    
    print("🔍 开始测试火山引擎 embedding 模型可用性...")
    print("=" * 60)
    
    available_models = []
    
    for model in models_to_test:
        print(f"\n🧪 测试模型: {model}")
        try:
            embeddings = VolcEngineEmbeddings(
                api_key=api_key,
                model=model,
                auto_fallback=False,  # 禁用自动回退，直接测试指定模型
                timeout=10
            )
            
            # 测试简单的文本嵌入
            test_text = "这是一个测试文本"
            vector = await embeddings.aembed_query(test_text)
            
            if vector and len(vector) > 0:
                print(f"✅ 模型 {model} 可用")
                print(f"   向量维度: {len(vector)}")
                print(f"   向量前3个值: {vector[:3]}")
                available_models.append({
                    "model": model,
                    "dimensions": len(vector),
                    "sample": vector[:3]
                })
            else:
                print(f"❌ 模型 {model} 返回空向量")
                
        except Exception as e:
            print(f"❌ 模型 {model} 不可用: {e}")
    
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    
    if available_models:
        print(f"✅ 找到 {len(available_models)} 个可用模型:")
        for model_info in available_models:
            print(f"   - {model_info['model']}: {model_info['dimensions']} 维")
        
        # 推荐最佳模型
        best_model = max(available_models, key=lambda x: x['dimensions'])
        print(f"\n🎯 推荐使用: {best_model['model']} ({best_model['dimensions']} 维)")
        
        # 生成配置建议
        print(f"\n📝 配置建议:")
        print(f"   model=\"{best_model['model']}\"")
        print(f"   dimensions={best_model['dimensions']}")
        
    else:
        print("❌ 没有找到可用的模型")
        print("💡 建议检查:")
        print("   1. API Key 是否正确")
        print("   2. 是否有访问 embedding 模型的权限")
        print("   3. 网络连接是否正常")

async def test_with_different_dimensions():
    """测试不同维度设置"""
    api_key = os.getenv("VOLCENGINE_API_KEY", "your-volcengine-api-key-here")
    
    if api_key == "your-volcengine-api-key-here":
        print("❌ 请设置 VOLCENGINE_API_KEY 环境变量")
        return
    
    print("\n🔢 测试不同维度设置...")
    print("=" * 40)
    
    # 测试维度
    dimensions_to_test = [4096, 2048, 1024, 512, 256]
    
    for dim in dimensions_to_test:
        print(f"\n📏 测试维度: {dim}")
        try:
            embeddings = VolcEngineEmbeddings(
                api_key=api_key,
                model="doubao-embedding-large",
                dimensions=dim,
                timeout=10
            )
            
            vector = await embeddings.aembed_query("测试文本")
            
            if vector and len(vector) == dim:
                print(f"✅ 维度 {dim} 可用，实际维度: {len(vector)}")
            else:
                print(f"❌ 维度 {dim} 不匹配，实际维度: {len(vector) if vector else 0}")
                
        except Exception as e:
            print(f"❌ 维度 {dim} 测试失败: {e}")

if __name__ == "__main__":
    print("🔥 火山引擎 Embedding 模型测试工具")
    print("请确保已设置 VOLCENGINE_API_KEY 环境变量")
    print()
    
    # 运行基本模型测试
    asyncio.run(test_models())
    
    # 运行维度测试
    asyncio.run(test_with_different_dimensions())
