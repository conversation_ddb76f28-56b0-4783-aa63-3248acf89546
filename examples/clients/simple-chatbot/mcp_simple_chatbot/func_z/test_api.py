#!/usr/bin/env python3
"""
测试API服务器的脚本
"""

import requests
import json
import time

def test_api_server():
    """测试API服务器的各个接口"""
    base_url = "http://127.0.0.1:8000"
    
    print("🧪 开始测试MCP工具检索API服务器")
    print("=" * 50)
    
    try:
        # 1. 测试根路径
        print("\n1️⃣ 测试根路径...")
        response = requests.get(f"{base_url}/", timeout=5)
        if response.status_code == 200:
            print("✅ 根路径访问成功")
            print(f"   响应: {response.json()}")
        else:
            print(f"❌ 根路径访问失败: {response.status_code}")
            return
        
        # 2. 测试状态接口
        print("\n2️⃣ 测试状态接口...")
        response = requests.get(f"{base_url}/status", timeout=5)
        if response.status_code == 200:
            status = response.json()
            print("✅ 状态接口访问成功")
            print(f"   初始化状态: {status.get('initialized', False)}")
            print(f"   工具数量: {status.get('tools_count', 'N/A')}")
            print(f"   运行时间: {status.get('uptime', 0):.2f}s")
            print(f"   Embedding类型: {status.get('embedding_type', 'N/A')}")
        else:
            print(f"❌ 状态接口访问失败: {response.status_code}")
            return
        
        # 3. 如果未初始化，进行初始化
        if not status.get("initialized", False):
            print("\n3️⃣ 系统未初始化，开始初始化...")
            init_data = {
                "embedding_type": "aliyun",
                "config_file": "servers_config.json"
            }
            
            print("   发送初始化请求...")
            start_time = time.time()
            response = requests.post(f"{base_url}/initialize", json=init_data, timeout=120)
            init_time = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 初始化成功 (耗时: {init_time:.2f}s)")
                print(f"   消息: {result.get('message', 'N/A')}")
                print(f"   处理时间: {result.get('processing_time', 0):.2f}s")
                if result.get('data'):
                    data = result['data']
                    print(f"   工具数量: {data.get('tools_count', 'N/A')}")
                    print(f"   Embedding类型: {data.get('embedding_type', 'N/A')}")
            else:
                print(f"❌ 初始化失败: {response.status_code}")
                if response.text:
                    print(f"   错误信息: {response.text}")
                return
        else:
            print("\n3️⃣ 系统已初始化，跳过初始化步骤")
        
        # 4. 再次检查状态
        print("\n4️⃣ 再次检查状态...")
        response = requests.get(f"{base_url}/status", timeout=5)
        if response.status_code == 200:
            status = response.json()
            print("✅ 状态检查成功")
            print(f"   初始化状态: {status.get('initialized', False)}")
            print(f"   工具数量: {status.get('tools_count', 'N/A')}")
            print(f"   Embedding类型: {status.get('embedding_type', 'N/A')}")
        
        # 5. 测试查询接口
        print("\n5️⃣ 测试查询接口...")
        test_queries = [
            "文件操作工具",
            "地图导航相关工具", 
            "菜谱推荐工具"
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n   查询 {i}: {query}")
            query_data = {
                "query": query,
                "max_results": 5
            }
            
            start_time = time.time()
            response = requests.post(f"{base_url}/query", json=query_data, timeout=30)
            query_time = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ 查询成功 (耗时: {query_time:.2f}s)")
                print(f"   📊 处理时间: {result.get('processing_time', 0):.2f}s")
                
                # 显示结果预览
                if result.get('data') and result['data'].get('result'):
                    result_text = result['data']['result']
                    preview = result_text[:100] + "..." if len(result_text) > 100 else result_text
                    print(f"   📋 结果预览: {preview}")
                else:
                    print("   📋 无结果数据")
            else:
                print(f"   ❌ 查询失败: {response.status_code}")
                if response.text:
                    print(f"   错误信息: {response.text}")
        
        # 6. 测试健康检查
        print("\n6️⃣ 测试健康检查...")
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            health = response.json()
            print("✅ 健康检查成功")
            print(f"   状态: {health.get('status', 'N/A')}")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
        
        print("\n" + "=" * 50)
        print("✅ API服务器测试完成")
        print("💡 所有接口工作正常！")
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务器")
        print("💡 请确保API服务器正在运行:")
        print("   python api_server.py --auto-init --embedding aliyun")
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")

if __name__ == "__main__":
    test_api_server()
