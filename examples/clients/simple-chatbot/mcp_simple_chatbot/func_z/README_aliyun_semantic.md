# 阿里云text-embedding-v4语义搜索和推荐系统

基于阿里云text-embedding-v4模型实现的高性能语义搜索和推荐系统。

## 🌟 特性

### 语义搜索系统
- ✅ 基于阿里云text-embedding-v4模型的高精度语义理解
- ✅ 支持多种向量维度选择（64-2048维）
- ✅ 智能缓存机制，避免重复计算
- ✅ 批量文档处理和索引构建
- ✅ 余弦相似度计算和结果排序
- ✅ 支持CSV文件批量导入
- ✅ 自定义搜索指令优化

### 语义推荐系统
- ✅ 基于用户画像的个性化推荐
- ✅ 多样性平衡算法，避免推荐结果过于相似
- ✅ 动态用户偏好学习和更新
- ✅ 交互历史记录和分析
- ✅ 支持排除已交互物品
- ✅ 推荐理由解释

## 📦 安装依赖

```bash
pip install aiohttp numpy pandas langchain-core dashscope
```

## 🔑 配置API密钥

1. 获取阿里云DashScope API密钥：
   - 访问 [阿里云百炼平台](https://bailian.console.aliyun.com/)
   - 创建应用并获取API Key

2. 设置环境变量：
```bash
export DASHSCOPE_API_KEY="your-api-key-here"
```

## 🚀 快速开始

### 1. 运行完整演示

```bash
python aliyun_semantic_demo.py
```

### 2. 单独使用语义搜索

```python
import asyncio
from semantic_search import SemanticSearchEngine

async def search_demo():
    # 初始化搜索引擎
    search_engine = SemanticSearchEngine(
        api_key="your-api-key",
        model="text-embedding-v4",
        dimensions=1024
    )
    
    # 添加文档
    documents = [
        {
            "content": "Python是一种高级编程语言",
            "metadata": {"category": "编程", "difficulty": "初级"}
        },
        {
            "content": "机器学习是人工智能的分支",
            "metadata": {"category": "AI", "difficulty": "中级"}
        }
    ]
    await search_engine.add_documents(documents)
    
    # 执行搜索
    results = await search_engine.search("编程语言学习", top_k=5)
    search_engine.print_search_results(results)

asyncio.run(search_demo())
```

### 3. 单独使用语义推荐

```python
import asyncio
from semantic_recommendation import SemanticRecommendationEngine

async def recommendation_demo():
    # 初始化推荐引擎
    rec_engine = SemanticRecommendationEngine(
        api_key="your-api-key",
        model="text-embedding-v4",
        dimensions=1024
    )
    
    # 添加物品
    items = [
        {
            "item_id": "course_1",
            "content": "Python编程基础课程",
            "metadata": {"price": 199, "category": "编程"}
        },
        {
            "item_id": "course_2",
            "content": "机器学习实战课程",
            "metadata": {"price": 299, "category": "AI"}
        }
    ]
    await rec_engine.add_items(items)
    
    # 创建用户画像
    await rec_engine.create_user_profile(
        user_id="user_1",
        interests=["Python", "编程", "数据分析"],
        interaction_history=[]
    )
    
    # 生成推荐
    recommendations = await rec_engine.recommend_items("user_1", top_k=3)
    rec_engine.print_recommendations(recommendations)

asyncio.run(recommendation_demo())
```

## 📊 模型配置选项

### 支持的模型
- `text-embedding-v4` (推荐) - 最新版本，支持100+语种和代码
- `text-embedding-v3` - 支持50+语种
- `text-embedding-v2` - 基础版本
- `text-embedding-v1` - 早期版本

### 向量维度选择

| 模型 | 支持的维度 | 推荐维度 | 说明 |
|------|------------|----------|------|
| text-embedding-v4 | 64, 128, 256, 512, 768, 1024, 1536, 2048 | 1024 | 平衡性能和精度 |
| text-embedding-v3 | 64, 128, 256, 512, 768, 1024 | 1024 | 标准选择 |
| text-embedding-v2 | 1536 | 1536 | 固定维度 |
| text-embedding-v1 | 1536 | 1536 | 固定维度 |

### 维度选择建议

- **64-256维**: 资源受限环境，快速原型
- **512-768维**: 一般应用，平衡性能
- **1024维**: 推荐选择，性能和精度平衡
- **1536-2048维**: 高精度要求，计算资源充足

## ⚙️ 高级配置

### 自定义指令优化

```python
# 搜索优化指令
search_instruct = "Given a web search query, retrieve relevant passages that answer the query"

# 推荐优化指令  
rec_instruct = "Given user preferences and item descriptions, find the most relevant items for recommendation"

search_engine = SemanticSearchEngine(
    api_key="your-api-key",
    search_instruct=search_instruct
)
```

### 缓存配置

```python
# 自定义缓存目录
search_engine = SemanticSearchEngine(
    api_key="your-api-key",
    cache_dir="./my_search_cache"
)

# 清除缓存
search_engine._load_cache()  # 重新加载
```

### 批处理配置

```python
# 调整批处理大小
embeddings = create_aliyun_embeddings(
    api_key="your-api-key",
    max_batch_size=5,  # 减少批处理大小
    timeout=60,        # 增加超时时间
    max_retries=5      # 增加重试次数
)
```

## 📈 性能优化建议

### 1. 维度选择
- 对于简单应用：使用512或768维
- 对于复杂应用：使用1024维
- 对于高精度需求：使用1536或2048维

### 2. 缓存策略
- 启用embedding缓存避免重复计算
- 定期清理过期缓存
- 使用SSD存储提高缓存读写速度

### 3. 批处理优化
- 根据网络条件调整batch_size
- 设置合适的超时时间
- 实现指数退避重试策略

### 4. 内存管理
- 大量文档时考虑分批处理
- 使用numpy float32减少内存占用
- 及时释放不需要的向量数据

## 🔧 故障排除

### 常见问题

1. **API密钥错误**
   ```
   ❌ 请设置DASHSCOPE_API_KEY环境变量
   ```
   解决：检查API密钥是否正确设置

2. **维度不支持**
   ```
   ValueError: 模型 text-embedding-v4 不支持维度 3000
   ```
   解决：使用支持的维度值

3. **请求超时**
   ```
   Request timeout after all retries
   ```
   解决：增加timeout参数或减少batch_size

4. **内存不足**
   ```
   MemoryError: Unable to allocate array
   ```
   解决：减少batch_size或使用更小的维度

### 调试模式

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# 启用详细日志
search_engine = SemanticSearchEngine(
    api_key="your-api-key",
    model="text-embedding-v4"
)
```

## 📝 API参考

### SemanticSearchEngine

#### 主要方法
- `add_documents(documents, batch_size=10)` - 添加文档
- `search(query, top_k=5, min_similarity=0.0)` - 执行搜索
- `load_from_csv(csv_file, content_column, metadata_columns)` - 从CSV加载
- `get_stats()` - 获取统计信息

### SemanticRecommendationEngine

#### 主要方法
- `add_items(items, batch_size=10)` - 添加物品
- `create_user_profile(user_id, interests, interaction_history)` - 创建用户画像
- `recommend_items(user_id, top_k=5, diversity_factor=0.1)` - 生成推荐
- `add_user_interaction(user_id, item_id)` - 添加交互记录

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📞 支持

如有问题，请联系：
- 创建GitHub Issue
- 查看阿里云文档：https://help.aliyun.com/zh/model-studio/embedding
