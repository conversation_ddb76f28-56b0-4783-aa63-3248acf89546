"""
阿里云text-embedding-v4模型集成
Aliyun Text Embedding v4 API Integration
"""

import asyncio
import json
import logging
import time
import os
from typing import List, Optional, Dict, Any, Union
import aiohttp
import numpy as np
from langchain_core.embeddings import Embeddings


class AliyunEmbeddings(Embeddings):
    """阿里云text-embedding-v4模型的LangChain兼容实现"""

    # 支持的模型列表
    SUPPORTED_MODELS = [
        "text-embedding-v4",
        "text-embedding-v3", 
        "text-embedding-v2",
        "text-embedding-v1"
    ]

    # 支持的维度选项
    SUPPORTED_DIMENSIONS = {
        "text-embedding-v4": [2048, 1536, 1024, 768, 512, 256, 128, 64],
        "text-embedding-v3": [1024, 768, 512, 256, 128, 64],
        "text-embedding-v2": [1536],
        "text-embedding-v1": [1536]
    }

    def __init__(
        self,
        api_key: str,
        model: str = "text-embedding-v4",
        base_url: str = "https://dashscope.aliyuncs.com/compatible-mode/v1/embeddings",
        dimensions: Optional[int] = 1024,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        timeout: int = 30,
        max_batch_size: int = 10,
        instruct: Optional[str] = None,
        **kwargs
    ):
        """
        初始化阿里云Embeddings
        
        Args:
            api_key: 阿里云DashScope API密钥
            model: 模型名称，默认为"text-embedding-v4"
            base_url: API基础URL
            dimensions: 向量维度，默认1024
            max_retries: 最大重试次数
            retry_delay: 重试延迟（秒）
            timeout: 请求超时时间（秒）
            max_batch_size: 批处理最大大小
            instruct: 自定义指令，用于提升特定任务效果
        """
        self.api_key = api_key
        self.model = model
        self.base_url = base_url
        self.dimensions = dimensions
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.timeout = timeout
        self.max_batch_size = max_batch_size
        self.instruct = instruct
        
        # 验证模型和维度
        self._validate_model_and_dimensions()
        
        # 设置请求头
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        
        # 统计信息
        self.total_requests = 0
        self.total_tokens = 0
        self.total_time = 0.0
        
        logging.info(f"🔥 Aliyun Embeddings initialized with model: {self.model}")
        logging.info(f"📏 Vector dimensions: {self.dimensions}")
        if self.instruct:
            logging.info(f"📝 Custom instruction: {self.instruct}")

    def _validate_model_and_dimensions(self):
        """验证模型和维度配置"""
        if self.model not in self.SUPPORTED_MODELS:
            raise ValueError(f"不支持的模型: {self.model}. 支持的模型: {self.SUPPORTED_MODELS}")
        
        if self.dimensions and self.dimensions not in self.SUPPORTED_DIMENSIONS[self.model]:
            raise ValueError(
                f"模型 {self.model} 不支持维度 {self.dimensions}. "
                f"支持的维度: {self.SUPPORTED_DIMENSIONS[self.model]}"
            )

    async def _make_request(self, texts: List[str]) -> List[List[float]]:
        """发送请求到阿里云API"""
        payload = {
            "model": self.model,
            "input": texts,
            "encoding_format": "float"
        }

        # 添加维度参数（仅v3和v4支持）
        if self.model in ["text-embedding-v3", "text-embedding-v4"] and self.dimensions:
            payload["dimensions"] = self.dimensions
        
        # 添加自定义指令（仅v4支持）
        if self.model == "text-embedding-v4" and self.instruct:
            payload["instruct"] = self.instruct
        
        start_time = time.time()
        
        for attempt in range(self.max_retries):
            try:
                async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                    async with session.post(
                        self.base_url,
                        headers=self.headers,
                        json=payload
                    ) as response:
                        if response.status == 200:
                            result = await response.json()
                            
                            # 提取向量
                            embeddings = []
                            for item in result.get("data", []):
                                embeddings.append(item.get("embedding", []))
                            
                            # 更新统计信息
                            self.total_requests += 1
                            self.total_tokens += result.get("usage", {}).get("total_tokens", 0)
                            self.total_time += time.time() - start_time
                            
                            logging.debug(f"✅ Aliyun API request successful, got {len(embeddings)} embeddings")
                            return embeddings
                        
                        elif response.status == 429:  # Rate limit
                            wait_time = self.retry_delay * (2 ** attempt)
                            logging.warning(f"⚠️ Rate limit hit, waiting {wait_time}s before retry {attempt + 1}/{self.max_retries}")
                            await asyncio.sleep(wait_time)
                            continue
                        
                        else:
                            error_text = await response.text()
                            logging.error(f"❌ Aliyun API error {response.status}: {error_text}")
                            if attempt == self.max_retries - 1:
                                raise Exception(f"Aliyun API error {response.status}: {error_text}")
                            
            except asyncio.TimeoutError:
                logging.warning(f"⏰ Request timeout, attempt {attempt + 1}/{self.max_retries}")
                if attempt == self.max_retries - 1:
                    raise Exception("Request timeout after all retries")
                await asyncio.sleep(self.retry_delay)
                
            except Exception as e:
                logging.error(f"❌ Request failed: {e}")
                if attempt == self.max_retries - 1:
                    raise
                await asyncio.sleep(self.retry_delay)
        
        raise Exception("All retry attempts failed")

    async def aembed_documents(self, texts: List[str]) -> List[List[float]]:
        """异步批量嵌入文档"""
        if not texts:
            return []
        
        # 分批处理
        all_embeddings = []
        for i in range(0, len(texts), self.max_batch_size):
            batch = texts[i:i + self.max_batch_size]
            batch_embeddings = await self._make_request(batch)
            all_embeddings.extend(batch_embeddings)
            
            # 避免请求过于频繁
            if i + self.max_batch_size < len(texts):
                await asyncio.sleep(0.1)
        
        return all_embeddings

    async def aembed_query(self, text: str) -> List[float]:
        """异步嵌入单个查询"""
        embeddings = await self._make_request([text])
        return embeddings[0] if embeddings else []

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """同步批量嵌入文档"""
        return asyncio.run(self.aembed_documents(texts))

    def embed_query(self, text: str) -> List[float]:
        """同步嵌入单个查询"""
        return asyncio.run(self.aembed_query(text))

    def get_stats(self) -> Dict[str, Any]:
        """获取使用统计信息"""
        avg_time = self.total_time / self.total_requests if self.total_requests > 0 else 0
        return {
            "total_requests": self.total_requests,
            "total_tokens": self.total_tokens,
            "total_time": self.total_time,
            "avg_time_per_request": avg_time,
            "model": self.model,
            "dimensions": self.dimensions
        }

    def print_stats(self):
        """打印使用统计信息"""
        stats = self.get_stats()
        print(f"\n🔥 Aliyun Embeddings 使用统计:")
        print(f"   总请求数: {stats['total_requests']}")
        print(f"   总Token数: {stats['total_tokens']}")
        print(f"   总耗时: {stats['total_time']:.2f}s")
        print(f"   平均每请求耗时: {stats['avg_time_per_request']:.2f}s")
        print(f"   使用模型: {stats['model']}")
        print(f"   向量维度: {stats['dimensions']}")


def create_aliyun_embeddings(
    api_key: str,
    model: str = "text-embedding-v4",
    dimensions: Optional[int] = 1024,
    instruct: Optional[str] = None,
    **kwargs
) -> AliyunEmbeddings:
    """
    创建阿里云Embeddings实例的便捷函数

    Args:
        api_key: 阿里云DashScope API密钥
        model: 模型名称
        dimensions: 向量维度
        instruct: 自定义指令
        **kwargs: 其他参数

    Returns:
        AliyunEmbeddings实例
    """
    return AliyunEmbeddings(
        api_key=api_key, 
        model=model, 
        dimensions=dimensions,
        instruct=instruct,
        **kwargs
    )


# 测试函数
async def test_aliyun_embeddings():
    """测试阿里云Embeddings"""
    # 注意：需要替换为实际的API密钥
    api_key = os.getenv("DASHSCOPE_API_KEY", "your-dashscope-api-key")
    
    if api_key == "your-dashscope-api-key":
        print("❌ 请设置DASHSCOPE_API_KEY环境变量")
        return
    
    embeddings = AliyunEmbeddings(
        api_key=api_key,
        model="text-embedding-v4",
        dimensions=1024,
        instruct="Given a web search query, retrieve relevant passages that answer the query"
    )
    
    # 测试单个查询
    test_text = "这是一个测试文本，用于验证阿里云embedding功能"
    try:
        vector = await embeddings.aembed_query(test_text)
        print(f"✅ 测试成功，向量维度: {len(vector)}")
        print(f"向量前5个值: {vector[:5]}")
        
        # 打印统计信息
        embeddings.print_stats()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")


if __name__ == "__main__":
    asyncio.run(test_aliyun_embeddings())
