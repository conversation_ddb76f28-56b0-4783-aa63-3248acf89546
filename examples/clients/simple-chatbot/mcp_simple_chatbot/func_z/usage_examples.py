#!/usr/bin/env python3
"""
MCP工具检索系统使用示例
展示不同的使用模式：命令行、API、编程接口
"""

import asyncio
import json
import time
import requests
from main import initialize_global_session, query_tools_fast, cleanup_global_session

async def example_1_programmatic_usage():
    """
    示例1: 编程接口使用
    先初始化，然后进行多次快速查询
    """
    print("=" * 60)
    print("📝 示例1: 编程接口使用")
    print("=" * 60)
    
    try:
        # 1. 初始化系统（只需要一次）
        print("🔧 正在初始化系统...")
        start_time = time.time()
        await initialize_global_session(embedding_type="aliyun")
        init_time = time.time() - start_time
        print(f"✅ 系统初始化完成，耗时: {init_time:.2f}s")
        
        # 2. 进行多次快速查询
        queries = [
            "文件操作相关的工具",
            "数据库查询工具", 
            "网络请求工具",
            "图像处理工具",
            "文本分析工具"
        ]
        
        print(f"\n🔍 开始进行 {len(queries)} 次查询:")
        total_query_time = 0
        
        for i, query in enumerate(queries, 1):
            print(f"\n--- 查询 {i}: {query} ---")
            start_time = time.time()
            
            result = await query_tools_fast(query)
            
            query_time = time.time() - start_time
            total_query_time += query_time
            
            print(f"⚡ 查询完成，耗时: {query_time:.2f}s")
            # 只显示结果的前100个字符
            result_preview = result[:100] + "..." if len(result) > 100 else result
            print(f"📋 结果预览: {result_preview}")
        
        avg_query_time = total_query_time / len(queries)
        print(f"\n📊 查询统计:")
        print(f"   总查询时间: {total_query_time:.2f}s")
        print(f"   平均查询时间: {avg_query_time:.2f}s")
        print(f"   初始化时间: {init_time:.2f}s")
        print(f"   性能提升: 避免了 {len(queries)-1} 次重复初始化")
        
    except Exception as e:
        print(f"❌ 示例1执行失败: {e}")
    finally:
        # 3. 清理资源
        await cleanup_global_session()
        print("🧹 资源清理完成")

def example_2_api_usage():
    """
    示例2: HTTP API使用
    展示如何通过HTTP接口使用服务
    """
    print("\n" + "=" * 60)
    print("🌐 示例2: HTTP API使用")
    print("=" * 60)
    
    base_url = "http://127.0.0.1:8000"
    
    try:
        # 1. 检查服务器状态
        print("🔍 检查服务器状态...")
        response = requests.get(f"{base_url}/status", timeout=5)
        if response.status_code == 200:
            status = response.json()
            print(f"✅ 服务器状态: {json.dumps(status, indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ 服务器状态检查失败: {response.status_code}")
            return
        
        # 2. 如果未初始化，进行初始化
        if not status.get("initialized", False):
            print("\n🔧 初始化系统...")
            init_data = {
                "embedding_type": "aliyun",
                "config_file": "servers_config.json"
            }
            response = requests.post(f"{base_url}/initialize", json=init_data, timeout=60)
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 初始化成功: {result['message']}")
                print(f"⏱️ 耗时: {result['processing_time']:.2f}s")
            else:
                print(f"❌ 初始化失败: {response.status_code}")
                return
        else:
            print("✅ 系统已初始化，跳过初始化步骤")
        
        # 3. 进行查询
        queries = [
            "文件读写工具",
            "网络请求工具",
            "数据处理工具"
        ]
        
        print(f"\n🔍 开始API查询:")
        for i, query in enumerate(queries, 1):
            print(f"\n--- API查询 {i}: {query} ---")
            
            query_data = {
                "query": query,
                "max_results": 5
            }
            
            response = requests.post(f"{base_url}/query", json=query_data, timeout=30)
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 查询成功")
                print(f"⏱️ 耗时: {result['processing_time']:.2f}s")
                # 显示结果预览
                if result.get("data") and result["data"].get("result"):
                    result_preview = result["data"]["result"][:100] + "..." if len(result["data"]["result"]) > 100 else result["data"]["result"]
                    print(f"📋 结果预览: {result_preview}")
            else:
                print(f"❌ 查询失败: {response.status_code}")
                if response.text:
                    print(f"错误信息: {response.text}")
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务器")
        print("💡 请先启动API服务器: python api_server.py --auto-init")
    except Exception as e:
        print(f"❌ 示例2执行失败: {e}")

async def example_3_command_line_usage():
    """
    示例3: 命令行使用
    展示不同的命令行使用方式
    """
    print("\n" + "=" * 60)
    print("💻 示例3: 命令行使用方式")
    print("=" * 60)
    
    print("以下是不同的命令行使用方式:")
    print()
    
    print("1️⃣ 服务器模式 (推荐):")
    print("   python main.py --server --embedding aliyun")
    print("   # 先初始化，然后等待用户输入查询")
    print()
    
    print("2️⃣ 单次查询模式:")
    print("   python main.py --embedding aliyun '文件操作工具'")
    print("   # 执行单次查询后退出")
    print()
    
    print("3️⃣ 传统交互模式:")
    print("   python main.py --embedding aliyun")
    print("   # 传统的交互式聊天模式")
    print()
    
    print("4️⃣ API服务器模式:")
    print("   python api_server.py --auto-init --embedding aliyun")
    print("   # 启动HTTP API服务器，自动初始化")
    print()
    
    print("5️⃣ API服务器手动初始化:")
    print("   python api_server.py")
    print("   # 启动HTTP API服务器，需要手动调用初始化接口")

def example_4_performance_comparison():
    """
    示例4: 性能对比
    对比传统方式和新方式的性能差异
    """
    print("\n" + "=" * 60)
    print("📊 示例4: 性能对比分析")
    print("=" * 60)
    
    print("🔄 传统方式 (每次查询都重新初始化):")
    print("   查询1: 初始化(10s) + 查询(0.5s) = 10.5s")
    print("   查询2: 初始化(10s) + 查询(0.5s) = 10.5s") 
    print("   查询3: 初始化(10s) + 查询(0.5s) = 10.5s")
    print("   总计: 31.5s")
    print()
    
    print("⚡ 新方式 (一次初始化，多次快速查询):")
    print("   初始化: 10s")
    print("   查询1: 0.5s")
    print("   查询2: 0.5s")
    print("   查询3: 0.5s")
    print("   总计: 11.5s")
    print()
    
    print("🚀 性能提升:")
    print("   时间节省: 20s (63.5%)")
    print("   资源节省: 避免重复加载模型和工具向量")
    print("   用户体验: 查询响应更快，支持连续查询")

async def main():
    """主函数，运行所有示例"""
    print("🚀 MCP工具检索系统使用示例")
    print("=" * 60)
    
    # 运行编程接口示例
    await example_1_programmatic_usage()
    
    # 运行API使用示例
    example_2_api_usage()
    
    # 展示命令行使用方式
    await example_3_command_line_usage()
    
    # 展示性能对比
    example_4_performance_comparison()
    
    print("\n" + "=" * 60)
    print("✅ 所有示例展示完成")
    print("💡 选择适合你的使用方式开始体验吧！")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())
