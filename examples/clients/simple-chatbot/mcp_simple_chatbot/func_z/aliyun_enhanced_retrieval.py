"""
阿里云增强检索系统
集成阿里云文本排序模型和意图理解模型，提升向量检索效果
"""

import os
import json
import time
import logging
import asyncio
import httpx
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import dashscope
from http import HTTPStatus

# 配置日志
logger = logging.getLogger(__name__)


@dataclass
class IntentResult:
    """意图理解结果"""
    intent: str
    confidence: float
    tool_calls: List[Dict[str, Any]]
    reasoning: str


@dataclass
class RerankResult:
    """重排序结果"""
    index: int
    relevance_score: float
    document: str
    original_similarity: float


@dataclass
class EnhancedRetrievalResult:
    """增强检索结果"""
    tool_id: str
    tool_name: str
    description: str
    original_similarity: float
    rerank_score: float
    final_score: float
    intent_match: bool
    reasoning: str


class AliyunIntentDetector:
    """阿里云意图理解模型"""
    
    def __init__(self, api_key: str = None):
        """
        初始化意图检测器
        
        Args:
            api_key: 阿里云API密钥，如果为None则从环境变量获取
        """
        self.api_key = api_key or os.getenv("DASHSCOPE_API_KEY")
        if not self.api_key:
            raise ValueError("需要设置DASHSCOPE_API_KEY环境变量或传入api_key参数")
        
        # 设置DashScope API密钥
        dashscope.api_key = self.api_key
        
        # 预定义的工具意图映射（基于实际可用的MCP工具）
        self.tool_intents = {
            # 文件操作相关
            "file_operations": ["文件", "文档", "压缩", "解压", "清理", "folder", "document", "file"],

            # 天气服务相关
            "weather_query": ["天气", "气象", "预报", "温度", "weather", "forecast", "climate"],

            # 新闻媒体相关
            "news_media": ["新闻", "资讯", "热点", "媒体", "news", "media"],

            # 位置服务相关
            "location_services": ["地图", "导航", "位置", "定位", "路线", "location", "map", "navigation"],

            # 系统工具相关
            "system_tools": ["系统", "硬件", "内存", "磁盘", "网络", "system", "hardware", "memory"],

            # B站相关
            "bilibili_tools": ["B站", "哔哩哔哩", "视频", "上传", "投稿", "bilibili", "video"],

            # 交通出行相关
            "transport_tools": ["交通", "火车", "航班", "高铁", "公交", "transport", "train", "flight"],

            # 翻译服务相关
            "translation_tools": ["翻译", "语言", "中文", "英文", "translate", "language"],

            # 音频处理相关
            "audio_tools": ["音频", "语音", "朗读", "播放", "音量", "audio", "voice", "speech"],

            # 办公文档相关
            "office_tools": ["办公", "文档", "表格", "Word", "Excel", "PowerPoint", "office"],

            # 图像处理相关
            "image_tools": ["图像", "图片", "截图", "格式转换", "压缩", "水印", "image", "screenshot"],

            # 实用工具相关
            "utilities": ["二维码", "通知", "提醒", "手机号", "utilities", "notification", "reminder"],

            # 娱乐信息相关
            "entertainment": ["电影", "菜谱", "娱乐", "推荐", "entertainment", "movie", "recipe"],

            # 商务信息相关
            "business": ["企业", "汇率", "商务", "business", "enterprise", "exchange"],

            # 数学计算相关
            "math_tools": ["数学", "计算", "表达式", "数值", "math", "calculate", "expression"],

            # 网络服务相关
            "network_tools": ["网络", "IP", "网速", "测试", "network", "speed", "test"],

            # 图书信息相关
            "books": ["图书", "书籍", "ISBN", "books", "book", "library"],

            # 通用查询
            "general_query": ["通用", "查询", "搜索", "信息", "general", "search", "query"]
        }
    
    async def detect_intent_with_tools(self, 
                                     query: str, 
                                     available_tools: List[Dict[str, str]]) -> IntentResult:
        """
        使用工具信息进行意图检测
        
        Args:
            query: 用户查询
            available_tools: 可用工具列表，格式为[{"name": "工具名", "description": "描述"}]
            
        Returns:
            IntentResult: 意图检测结果
        """
        try:
            # 构建工具信息
            tools_info = []
            for tool in available_tools[:10]:  # 限制工具数量以提高准确性
                tools_info.append({
                    "name": tool["name"],
                    "description": tool["description"],
                    "parameters": {"type": "object", "properties": {}}
                })
            
            tools_string = json.dumps(tools_info, ensure_ascii=False)
            
            # 构建系统提示
            system_prompt = f"""You are Qwen, created by Alibaba Cloud. You are a helpful assistant. You may call one or more tools to assist with the user query. The tools you can use are as follows:
{tools_string}
Response in INTENT_MODE."""
            
            messages = [
                {'role': 'system', 'content': system_prompt},
                {'role': 'user', 'content': query}
            ]
            
            # 调用意图理解模型
            response = dashscope.Generation.call(
                model="tongyi-intent-detect-v3",
                messages=messages,
                result_format="message",api_key="sk-fcac337c29fe4d6f93bb9ff2ca2395d8"
            )
            
            if response.status_code == HTTPStatus.OK:
                content = response.output.choices[0].message.content
                return self._parse_intent_response(content, query)
            else:
                logger.error(f"意图检测失败: {response}")
                return IntentResult(
                    intent="general_query",
                    confidence=0.5,
                    tool_calls=[],
                    reasoning="意图检测服务调用失败，使用默认意图"
                )
                
        except Exception as e:
            logger.error(f"意图检测异常: {e}")
            return IntentResult(
                intent="general_query",
                confidence=0.5,
                tool_calls=[],
                reasoning=f"意图检测异常: {str(e)}"
            )
    
    def _parse_intent_response(self, content: str, query: str) -> IntentResult:
        """解析意图检测响应"""
        import re
        
        try:
            # 解析标签
            tags_pattern = r'<tags>(.*?)</tags>'
            tool_call_pattern = r'<tool_call>(.*?)</tool_call>'
            content_pattern = r'<content>(.*?)</content>'
            
            tags_match = re.search(tags_pattern, content, re.DOTALL)
            tool_call_match = re.search(tool_call_pattern, content, re.DOTALL)
            content_match = re.search(content_pattern, content, re.DOTALL)
            
            tags = tags_match.group(1).strip() if tags_match else ""
            tool_call_str = tool_call_match.group(1).strip() if tool_call_match else ""
            response_content = content_match.group(1).strip() if content_match else ""
            
            # 解析工具调用
            tool_calls = []
            if tool_call_str and tool_call_str != "[]":
                try:
                    tool_calls = json.loads(tool_call_str)
                    if not isinstance(tool_calls, list):
                        tool_calls = [tool_calls]
                except json.JSONDecodeError:
                    logger.warning(f"无法解析工具调用: {tool_call_str}")
            
            # 确定意图
            intent = self._determine_intent(tags, tool_calls, query)
            
            # 计算置信度
            confidence = 0.9 if tool_calls else 0.7
            if "function call" in tags.lower():
                confidence = 0.95
            
            reasoning = f"基于标签'{tags}'和工具调用{len(tool_calls)}个确定意图"
            if response_content:
                reasoning += f"，响应内容: {response_content[:50]}"
            
            return IntentResult(
                intent=intent,
                confidence=confidence,
                tool_calls=tool_calls,
                reasoning=reasoning
            )
            
        except Exception as e:
            logger.error(f"解析意图响应失败: {e}")
            return IntentResult(
                intent="general_query",
                confidence=0.5,
                tool_calls=[],
                reasoning=f"解析失败: {str(e)}"
            )
    
    def _determine_intent(self, tags: str, tool_calls: List[Dict], query: str) -> str:
        """根据标签、工具调用和查询确定意图"""
        # 如果有工具调用，根据工具名称确定意图
        if tool_calls:
            for tool_call in tool_calls:
                tool_name = tool_call.get("name", "").lower()
                for intent, keywords in self.tool_intents.items():
                    if any(keyword in tool_name for keyword in keywords):
                        return intent
        
        # 根据查询内容确定意图
        query_lower = query.lower()
        for intent, keywords in self.tool_intents.items():
            if any(keyword in query_lower for keyword in keywords):
                return intent
        
        return "general_query"


class AliyunTextReranker:
    """阿里云文本排序模型"""
    
    def __init__(self, api_key: str = None):
        """
        初始化文本重排序器
        
        Args:
            api_key: 阿里云API密钥，如果为None则从环境变量获取
        """
        self.api_key = "sk-fcac337c29fe4d6f93bb9ff2ca2395d8"
        if not self.api_key:
            raise ValueError("需要设置DASHSCOPE_API_KEY环境变量或传入api_key参数")
        
        # 设置DashScope API密钥
        dashscope.api_key = self.api_key
    
    async def rerank_documents(self, 
                             query: str, 
                             documents: List[str], 
                             top_n: int = None) -> List[RerankResult]:
        """
        对文档进行重排序
        
        Args:
            query: 查询文本
            documents: 待排序的文档列表
            top_n: 返回的top文档数量
            
        Returns:
            List[RerankResult]: 重排序结果列表
        """
        try:
            if not documents:
                return []
            
            # 限制文档数量（API限制为500）
            max_docs = min(len(documents), 500)
            docs_to_rank = documents[:max_docs]
            
            # 调用文本排序模型
            logger.info(f"调用重排序模型: query='{query}', documents={len(docs_to_rank)}个")
            response = dashscope.TextReRank.call(
                model="gte-rerank-v2",
                query=query,
                documents=docs_to_rank,
                top_n=top_n or len(docs_to_rank),
                return_documents=True
            )

            # 添加响应调试信息
            logger.debug(f"重排序响应状态: {response.status_code}")
            if hasattr(response, 'output') and response.output:
                logger.debug(f"响应结果数量: {len(response.output.results) if response.output.results else 0}")
            else:
                logger.warning("响应中没有output字段")
            
            if response.status_code == HTTPStatus.OK:
                results = []
                for result in response.output.results:
                    try:
                        # 安全地获取文档文本
                        document_text = ""
                        if hasattr(result, 'document') and result.document is not None:
                            if hasattr(result.document, 'text'):
                                document_text = result.document.text
                            else:
                                # 如果没有text属性，尝试直接使用document
                                document_text = str(result.document)
                        else:
                            # 如果没有document，使用原始文档
                            if result.index < len(docs_to_rank):
                                document_text = docs_to_rank[result.index]

                        rerank_result = RerankResult(
                            index=result.index,
                            relevance_score=result.relevance_score,
                            document=document_text,
                            original_similarity=0.0  # 将在外部设置
                        )
                        results.append(rerank_result)
                    except Exception as e:
                        logger.warning(f"处理重排序结果时出错: {e}, 跳过索引 {result.index}")
                        continue

                logger.info(f"重排序完成: {len(results)} 个文档")
                return results
            else:
                logger.error(f"文本重排序失败: {response}")
                return []
                
        except Exception as e:
            logger.error(f"文本重排序异常: {e}")
            return []


class AliyunEnhancedRetrieval:
    """阿里云增强检索系统"""
    
    def __init__(self, 
                 cached_store,
                 api_key: str = None,
                 intent_weight: float = 0.3,
                 rerank_weight: float = 0.4,
                 original_weight: float = 0.3):
        """
        初始化增强检索系统
        
        Args:
            cached_store: 缓存向量存储
            api_key: 阿里云API密钥
            intent_weight: 意图匹配权重
            rerank_weight: 重排序权重
            original_weight: 原始相似度权重
        """
        self.cached_store = cached_store
        self.intent_detector = AliyunIntentDetector(api_key)
        self.text_reranker = AliyunTextReranker(api_key)
        
        # 权重配置
        self.intent_weight = intent_weight
        self.rerank_weight = rerank_weight
        self.original_weight = original_weight
        
        # 确保权重总和为1
        total_weight = intent_weight + rerank_weight + original_weight
        if abs(total_weight - 1.0) > 0.01:
            logger.warning(f"权重总和不为1.0: {total_weight}，将自动归一化")
            self.intent_weight /= total_weight
            self.rerank_weight /= total_weight
            self.original_weight /= total_weight
    
    async def enhanced_search(self, 
                            query: str, 
                            top_k: int = 10,
                            use_intent: bool = True,
                            use_rerank: bool = True) -> List[EnhancedRetrievalResult]:
        """
        执行增强检索
        
        Args:
            query: 查询文本
            top_k: 返回结果数量
            use_intent: 是否使用意图理解
            use_rerank: 是否使用文本重排序
            
        Returns:
            List[EnhancedRetrievalResult]: 增强检索结果
        """
        start_time = time.time()
        logger.info(f"开始增强检索: '{query}' (top_k={top_k})")
        
        try:
            # 1. 获取初始检索结果
            initial_results = await self.cached_store.search_similar_tools(
                query, top_k=min(top_k * 3, 50)  # 获取更多候选进行重排序
            )
            
            if not initial_results:
                logger.warning("初始检索无结果")
                return []
            
            logger.info(f"初始检索获得 {len(initial_results)} 个候选")
            
            # 2. 意图理解（如果启用）
            intent_result = None
            if use_intent:
                available_tools = [
                    {"name": result["tool_data"]["name"], 
                     "description": result["tool_data"]["description"]}
                    for result in initial_results
                ]
                intent_result = await self.intent_detector.detect_intent_with_tools(
                    query, available_tools
                )
                logger.info(f"意图检测: {intent_result.intent} (置信度: {intent_result.confidence:.3f})")
            
            # 3. 文本重排序（如果启用）
            rerank_results = {}
            if use_rerank:
                documents = [
                    f"{result['tool_data']['name']}: {result['tool_data']['description']}"
                    for result in initial_results
                ]
                
                rerank_list = await self.text_reranker.rerank_documents(
                    query, documents, top_n=len(documents)
                )
                
                # 建立重排序结果映射
                for rerank_result in rerank_list:
                    rerank_results[rerank_result.index] = rerank_result
                
                logger.info(f"重排序完成: {len(rerank_results)} 个结果")
            
            # 4. 融合结果
            enhanced_results = []
            for i, result in enumerate(initial_results):
                tool_name = result["tool_data"]["name"]
                description = result["tool_data"]["description"]
                original_similarity = result["similarity"]
                
                # 获取重排序分数
                rerank_score = 0.0
                if i in rerank_results:
                    rerank_score = rerank_results[i].relevance_score
                
                # 计算意图匹配
                intent_match = False
                intent_score = 0.0
                if intent_result:
                    intent_match = self._check_intent_match(
                        tool_name, description, intent_result
                    )
                    intent_score = intent_result.confidence if intent_match else 0.0
                
                # 计算最终分数
                if use_intent and intent_result:
                    # 使用意图检测时的完整权重
                    final_score = (
                        self.original_weight * original_similarity +
                        self.rerank_weight * rerank_score +
                        self.intent_weight * intent_score
                    )
                else:
                    # 不使用意图检测时，重新归一化权重
                    total_weight = self.original_weight + self.rerank_weight
                    if total_weight > 0:
                        normalized_original = self.original_weight / total_weight
                        normalized_rerank = self.rerank_weight / total_weight
                        final_score = (
                            normalized_original * original_similarity +
                            normalized_rerank * rerank_score
                        )
                    else:
                        # 如果权重都为0，使用原始相似度
                        final_score = original_similarity
                
                # 构建推理说明
                reasoning_parts = [
                    f"原始相似度: {original_similarity:.3f}"
                ]
                if use_rerank:
                    reasoning_parts.append(f"重排序分数: {rerank_score:.3f}")
                if use_intent:
                    reasoning_parts.append(f"意图匹配: {'是' if intent_match else '否'}")
                
                reasoning = " | ".join(reasoning_parts)
                
                enhanced_result = EnhancedRetrievalResult(
                    tool_id=result["tool_id"],
                    tool_name=tool_name,
                    description=description,
                    original_similarity=original_similarity,
                    rerank_score=rerank_score,
                    final_score=final_score,
                    intent_match=intent_match,
                    reasoning=reasoning
                )
                
                enhanced_results.append(enhanced_result)
            
            # 5. 按最终分数排序并返回top_k
            enhanced_results.sort(key=lambda x: x.final_score, reverse=True)
            final_results = enhanced_results[:top_k]
            
            processing_time = time.time() - start_time
            logger.info(f"增强检索完成: 耗时 {processing_time:.2f}s, 返回 {len(final_results)} 个结果")
            
            return final_results
            
        except Exception as e:
            logger.error(f"增强检索失败: {e}")
            # 回退到原始检索
            fallback_results = await self.cached_store.search_similar_tools(query, top_k)
            return [
                EnhancedRetrievalResult(
                    tool_id=result["tool_id"],
                    tool_name=result["tool_data"]["name"],
                    description=result["tool_data"]["description"],
                    original_similarity=result["similarity"],
                    rerank_score=0.0,
                    final_score=result["similarity"],
                    intent_match=False,
                    reasoning="增强检索失败，使用原始检索结果"
                )
                for result in fallback_results
            ]
    
    def _check_intent_match(self, 
                          tool_name: str, 
                          description: str, 
                          intent_result: IntentResult) -> bool:
        """检查工具是否匹配检测到的意图"""
        # 检查工具调用中是否包含该工具
        for tool_call in intent_result.tool_calls:
            if tool_call.get("name", "").lower() == tool_name.lower():
                return True
        
        # 检查意图关键词是否在工具名称或描述中
        intent_keywords = self.intent_detector.tool_intents.get(intent_result.intent, [])
        tool_text = f"{tool_name} {description}".lower()
        
        return any(keyword.lower() in tool_text for keyword in intent_keywords)
    
    def update_weights(self, 
                      intent_weight: float, 
                      rerank_weight: float, 
                      original_weight: float):
        """更新权重配置"""
        total = intent_weight + rerank_weight + original_weight
        if abs(total - 1.0) > 0.01:
            raise ValueError(f"权重总和必须为1.0，当前为: {total}")
        
        self.intent_weight = intent_weight
        self.rerank_weight = rerank_weight
        self.original_weight = original_weight
        
        logger.info(f"权重已更新: 意图={intent_weight:.2f}, 重排序={rerank_weight:.2f}, 原始={original_weight:.2f}")
