"""
Custom tool selector using cached vectors.

Provides tool selection based on cached vector similarity search.
"""

from typing import Any, Dict, List
try:
    from .cached_vector_store import CachedVectorStore
except ImportError:
    from cached_vector_store import CachedVectorStore


class CustomToolSelector:
    """Custom tool selector that uses cached vectors for tool selection."""

    def __init__(self, cached_store: CachedVectorStore, tool_registry: Dict[str, Any]):
        """Initialize with cached store and tool registry."""
        self.cached_store = cached_store
        self.tool_registry = tool_registry

    async def select_tools(self, query: str, max_tools: int = 5) -> List[Any]:
        """Select relevant tools based on query using cached vectors."""
        # Search for similar tools
        similar_tools = await self.cached_store.search_similar_tools(query, max_tools)

        # Return the actual tool objects
        selected_tools = []
        for tool_info in similar_tools:
            tool_id = tool_info["tool_id"]
            if tool_id in self.tool_registry:
                selected_tools.append(self.tool_registry[tool_id])

        return selected_tools

    async def get_tool_similarities(self, query: str, max_tools: int = 5) -> List[Dict[str, Any]]:
        """Get tool similarities with detailed information."""
        return await self.cached_store.search_similar_tools(query, max_tools)

    def get_tool_count(self) -> int:
        """Get the total number of tools available."""
        return self.cached_store.get_tool_count()

    def get_registry_info(self) -> Dict[str, Any]:
        """Get information about the tool registry."""
        return {
            "total_tools": len(self.tool_registry),
            "cached_tools": self.cached_store.get_tool_count(),
            "tool_ids": list(self.tool_registry.keys())
        }
