"""
Cached vector store for tool similarity search.

A vector store that uses pre-computed cached vectors for efficient tool search.
"""

import logging
from typing import Any, Dict, List
try:
    from .tool_vector_manager import ToolVectorManager
except ImportError:
    from tool_vector_manager import ToolVectorManager


class CachedVectorStore:
    """A vector store that uses pre-computed cached vectors."""

    def __init__(self, tool_vector_manager: ToolVectorManager):
        """Initialize with tool vector manager."""
        self.tool_vector_manager = tool_vector_manager
        self.tools_data: Dict[str, Dict[str, Any]] = {}
        self.tool_vectors: Dict[str, List[float]] = {}

    async def add_tool(self, tool_id: str, tool_name: str, tool_description: str) -> None:
        """Add a tool to the vector store."""
        # Get cached or generate vector
        vector = await self.tool_vector_manager.get_tool_vector(tool_name, tool_description)

        # Store tool data and vector
        self.tools_data[tool_id] = {
            "name": tool_name,
            "description": tool_description,
            "full_description": f"{tool_name}: {tool_description}"
        }
        self.tool_vectors[tool_id] = vector

    def cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """Calculate optimized cosine similarity between two vectors."""
        import numpy as np
        vec1_np = np.array(vec1, dtype=np.float32)
        vec2_np = np.array(vec2, dtype=np.float32)

        dot_product = np.dot(vec1_np, vec2_np)
        norm1 = np.linalg.norm(vec1_np)
        norm2 = np.linalg.norm(vec2_np)

        if norm1 == 0 or norm2 == 0:
            return 0.0

        return float(dot_product / (norm1 * norm2))

    def _calculate_keyword_similarity(self, query: str, tool_name: str, tool_description: str) -> float:
        """Calculate keyword overlap similarity using Jaccard index with stop word filtering."""
        import re
        
        # Import similarity config
        try:
            from .utils.similarity_config import SimilarityConfig
        except ImportError:
            try:
                from utils.similarity_config import SimilarityConfig
            except ImportError:
                # Fallback configuration
                class SimilarityConfig:
                    SEMANTIC_WEIGHT = 0.8
                    KEYWORD_WEIGHT = 0.1
                    NAME_MATCH_WEIGHT = 0.1
                    STOP_WORDS = set()
                    MIN_WORD_LENGTH = 2

        # Extract keywords (alphanumeric words, convert to lowercase)
        query_words = set(re.findall(r'\w+', query.lower()))
        tool_text = f"{tool_name} {tool_description}".lower()
        tool_words = set(re.findall(r'\w+', tool_text))

        # Filter out stop words and short words
        query_words = {w for w in query_words
                      if len(w) >= SimilarityConfig.MIN_WORD_LENGTH
                      and w not in SimilarityConfig.STOP_WORDS}
        tool_words = {w for w in tool_words
                     if len(w) >= SimilarityConfig.MIN_WORD_LENGTH
                     and w not in SimilarityConfig.STOP_WORDS}

        if not query_words or not tool_words:
            return 0.0

        # Calculate Jaccard similarity (intersection over union)
        intersection = len(query_words & tool_words)
        union = len(query_words | tool_words)

        return intersection / union if union > 0 else 0.0

    def _calculate_name_match(self, query: str, tool_name: str) -> float:
        """Calculate tool name matching score."""
        query_lower = query.lower()
        name_lower = tool_name.lower()

        # Exact substring match
        if name_lower in query_lower or query_lower in name_lower:
            return 1.0

        # Partial word match (handle underscore-separated tool names)
        name_words = set(name_lower.replace('_', ' ').split())
        query_words = set(query_lower.split())

        if name_words & query_words:
            return 0.5

        return 0.0

    def hybrid_similarity(self, query: str, tool_name: str, tool_description: str,
                         query_vector: List[float], tool_vector: List[float]) -> float:
        """Calculate hybrid similarity combining semantic and keyword matching."""

        # Import similarity config
        try:
            from .utils.similarity_config import SimilarityConfig
        except ImportError:
            try:
                from utils.similarity_config import SimilarityConfig
            except ImportError:
                # Fallback configuration
                class SimilarityConfig:
                    SEMANTIC_WEIGHT = 0.8
                    KEYWORD_WEIGHT = 0.1
                    NAME_MATCH_WEIGHT = 0.1

        # 1. Semantic similarity (vector-based)
        semantic_sim = self.cosine_similarity(query_vector, tool_vector)

        # 2. Keyword similarity (text-based)
        keyword_sim = self._calculate_keyword_similarity(query, tool_name, tool_description)

        # 3. Name matching bonus
        name_match = self._calculate_name_match(query, tool_name)

        # Weighted combination using configurable weights
        final_score = (
            SimilarityConfig.SEMANTIC_WEIGHT * semantic_sim +
            SimilarityConfig.KEYWORD_WEIGHT * keyword_sim +
            SimilarityConfig.NAME_MATCH_WEIGHT * name_match
        )

        return min(final_score, 1.0)  # Ensure score doesn't exceed 1.0

    async def search_similar_tools(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """Search for tools similar to the query using hybrid similarity."""
        # Generate vector for query
        query_vector = await self.tool_vector_manager.embeddings_model.aembed_query(query)

        # Calculate hybrid similarities
        similarities = []
        for tool_id, tool_vector in self.tool_vectors.items():
            tool_data = self.tools_data[tool_id]
            tool_name = tool_data["name"]
            tool_description = tool_data["description"]

            # Use hybrid similarity calculation
            similarity = self.hybrid_similarity(
                query, tool_name, tool_description, query_vector, tool_vector
            )

            similarities.append({
                "tool_id": tool_id,
                "similarity": similarity,
                "tool_data": tool_data,
                "match_details": {
                    "semantic_score": self.cosine_similarity(query_vector, tool_vector),
                    "keyword_score": self._calculate_keyword_similarity(query, tool_name, tool_description),
                    "name_match_score": self._calculate_name_match(query, tool_name)
                }
            })

        # Sort by similarity and return top_k
        similarities.sort(key=lambda x: x["similarity"], reverse=True)
        return similarities[:top_k]

    def get_tool_count(self) -> int:
        """Get the number of tools in the store."""
        return len(self.tools_data)

    def get_tool_info(self, tool_id: str) -> Dict[str, Any]:
        """Get information about a specific tool."""
        return self.tools_data.get(tool_id, {})
