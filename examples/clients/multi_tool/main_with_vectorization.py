"""
集成 JSON 向量化的 main.py 示例。

展示如何在 main.py 中集成 JSON 配置向量化功能。
"""

import asyncio
import logging
import json
from typing import Any, Dict, List
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

try:
    from json_vectorizer import JsonVectorizer
    from multi_tool_system import MultiToolSystem
except ImportError:
    from .json_vectorizer import JsonVectorizer
    from .multi_tool_system import MultiToolSystem


class MockEmbeddingsModel:
    """模拟嵌入模型。"""
    
    async def aembed_query(self, text: str) -> List[float]:
        import hashlib
        hash_obj = hashlib.sha256(text.encode('utf-8'))
        hash_bytes = hash_obj.digest()
        
        vector = []
        for i in range(0, len(hash_bytes), 4):
            chunk = hash_bytes[i:i+4]
            if len(chunk) == 4:
                val = int.from_bytes(chunk, 'big') / (2**32)
                vector.append(val)
        
        while len(vector) < 384:
            vector.append(0.0)
        
        return vector[:384]


class EnhancedChatSession:
    """增强的聊天会话，集成 JSON 向量化功能。"""
    
    def __init__(self, config_path: str = "servers_config.json"):
        """
        初始化增强聊天会话。
        
        Args:
            config_path: 服务器配置文件路径
        """
        self.config_path = config_path
        self.embeddings_model = MockEmbeddingsModel()
        
        # 初始化组件
        self.json_vectorizer: JsonVectorizer = None
        self.multi_tool_system: MultiToolSystem = None
        
        # 服务器相关
        self.servers: Dict[str, Any] = {}
        self.available_tools: List[Any] = []
        
        logging.info("EnhancedChatSession initialized")
    
    async def initialize(self):
        """初始化系统组件。"""
        
        print("🚀 初始化增强聊天会话...")
        
        # 1. 初始化 JSON 向量化器
        print("📊 初始化 JSON 向量化器...")
        self.json_vectorizer = JsonVectorizer(
            embeddings_model=self.embeddings_model,
            cache_dir=".servers_vector_cache"
        )
        
        # 2. 加载服务器配置
        print("📁 加载服务器配置...")
        await self._load_server_config()
        
        # 3. 向量化服务器配置
        print("🔄 向量化服务器配置...")
        await self.json_vectorizer.vectorize_servers()
        
        # 4. 初始化多工具系统
        print("🛠️ 初始化多工具系统...")
        self.multi_tool_system = MultiToolSystem(
            embeddings_model=self.embeddings_model,
            cache_dir=".multi_tool_cache",
            enable_enhanced_features=False  # 简化演示
        )
        
        # 5. 模拟添加工具（在实际应用中，这些工具来自 MCP 服务器）
        await self._simulate_tools()
        
        print("✅ 系统初始化完成!")
    
    async def _load_server_config(self):
        """加载服务器配置。"""
        
        config_file = Path(self.config_path)
        if config_file.exists():
            # 加载真实配置
            self.json_vectorizer.load_servers_config(self.config_path)
            servers = self.json_vectorizer.get_all_servers()
            print(f"✅ 加载了 {len(servers)} 个服务器配置")
        else:
            # 创建示例配置
            print("⚠️ 配置文件不存在，创建示例配置...")
            await self._create_example_servers()
    
    async def _create_example_servers(self):
        """创建示例服务器配置。"""
        
        from json_vectorizer import ServerInfo
        
        example_configs = {
            "文件处理器": {
                "name": "文件处理器",
                "type": "stdio",
                "command": "node",
                "args": ["/path/to/file-processor.js"],
                "disabledTools": []
            },
            "图像编辑器": {
                "name": "图像编辑器", 
                "type": "stdio",
                "command": "python",
                "args": ["/path/to/image-editor.py"],
                "disabledTools": []
            },
            "数据分析器": {
                "name": "数据分析器",
                "type": "stdio", 
                "command": "python",
                "args": ["/path/to/data-analyzer.py"],
                "disabledTools": []
            },
            "网络工具": {
                "name": "网络工具",
                "type": "stdio",
                "command": "node", 
                "args": ["/path/to/network-tools.js"],
                "disabledTools": []
            }
        }
        
        for server_id, config in example_configs.items():
            server_info = ServerInfo(server_id, config)
            self.json_vectorizer.servers[server_id] = server_info
        
        print(f"✅ 创建了 {len(example_configs)} 个示例服务器")
    
    async def _simulate_tools(self):
        """模拟工具（在实际应用中从 MCP 服务器获取）。"""
        
        class MockTool:
            def __init__(self, name: str, description: str):
                self.name = name
                self.description = description
        
        # 基于服务器配置创建模拟工具
        servers = self.json_vectorizer.get_all_servers()
        tools = []
        
        for server_id, server_info in servers.items():
            # 为每个服务器创建一些模拟工具
            tool = MockTool(
                name=f"{server_info.name}_tool",
                description=server_info.description
            )
            tools.append(tool)
        
        # 添加工具到多工具系统
        await self.multi_tool_system.add_tools_batch(tools)
        await self.multi_tool_system.initialize_selectors()
        
        print(f"✅ 添加了 {len(tools)} 个模拟工具")
    
    async def process_query(self, query: str) -> Dict[str, Any]:
        """
        处理用户查询。
        
        Args:
            query: 用户查询
            
        Returns:
            处理结果
        """
        
        print(f"\n🔍 处理查询: '{query}'")
        
        # 1. 搜索相关服务器
        print("📡 搜索相关服务器...")
        server_results = await self.json_vectorizer.search_servers(query, top_k=3)
        
        # 2. 选择相关工具
        print("🛠️ 选择相关工具...")
        tool_results = await self.multi_tool_system.select_tools(query, max_tools=3)
        
        # 3. 组合结果
        result = {
            "query": query,
            "relevant_servers": [
                {
                    "name": r["server_info"]["name"],
                    "similarity": r["similarity"],
                    "description": r["server_info"]["description"][:100] + "..."
                }
                for r in server_results
            ],
            "selected_tools": [
                {
                    "name": tool.name,
                    "description": tool.description[:100] + "..."
                }
                for tool in tool_results
            ],
            "processing_stats": {
                "servers_found": len(server_results),
                "tools_selected": len(tool_results)
            }
        }
        
        return result
    
    def display_result(self, result: Dict[str, Any]):
        """显示处理结果。"""
        
        print(f"\n📋 查询结果:")
        print(f"查询: {result['query']}")
        
        print(f"\n🏢 相关服务器 ({len(result['relevant_servers'])}):")
        for i, server in enumerate(result['relevant_servers'], 1):
            print(f"  {i}. {server['name']} (相似度: {server['similarity']:.3f})")
            print(f"     {server['description']}")
        
        print(f"\n🛠️ 选择的工具 ({len(result['selected_tools'])}):")
        for i, tool in enumerate(result['selected_tools'], 1):
            print(f"  {i}. {tool['name']}")
            print(f"     {tool['description']}")
        
        stats = result['processing_stats']
        print(f"\n📊 处理统计:")
        print(f"  找到服务器: {stats['servers_found']}")
        print(f"  选择工具: {stats['tools_selected']}")


async def main():
    """主函数。"""
    
    print("🎯 增强聊天会话演示")
    print("=" * 50)
    
    # 创建并初始化会话
    session = EnhancedChatSession("servers_config.json")
    await session.initialize()
    
    # 测试一些查询
    test_queries = [
        "我需要处理图片",
        "查询电影信息", 
        "获取新闻资讯",
        "文件压缩功能",
        "地图导航",
        "数学计算"
    ]
    
    print(f"\n🧪 测试查询:")
    for query in test_queries:
        result = await session.process_query(query)
        session.display_result(result)
        print("-" * 30)


if __name__ == "__main__":
    asyncio.run(main())
