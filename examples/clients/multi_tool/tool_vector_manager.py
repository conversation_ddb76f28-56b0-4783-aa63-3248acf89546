"""
Tool vector management for embeddings generation and caching.

Manages tool vector generation and caching using embeddings models.
"""

import logging
from typing import List
try:
    from .vector_cache import VectorCache
except ImportError:
    from vector_cache import VectorCache


class ToolVectorManager:
    """Manages tool vector generation and caching."""

    def __init__(self, embeddings_model, vector_cache: VectorCache):
        """Initialize with embeddings model and cache."""
        self.embeddings_model = embeddings_model
        self.vector_cache = vector_cache
        self._embedding_cache = {}

    async def get_tool_vector(self, tool_name: str, tool_description: str) -> List[float]:
        """Get vector for a tool, using cache if available."""
        # Check cache first
        cached_vector = self.vector_cache.get_vector(tool_name, tool_description)
        if cached_vector is not None:
            return cached_vector

        # Generate new vector
        logging.info(f"Generating vector for tool: {tool_name}")
        description_text = f"{tool_name}: {tool_description}"

        try:
            # Use the embeddings model to generate vector
            vector = await self.embeddings_model.aembed_query(description_text)

            # Cache the result
            self.vector_cache.set_vector(tool_name, tool_description, vector)

            return vector
        except Exception as e:
            logging.error(f"Failed to generate vector for tool {tool_name}: {e}")
            raise

    def save_cache(self) -> None:
        """Save the vector cache to disk."""
        self.vector_cache.save()

    def clear_cache(self) -> None:
        """Clear the vector cache."""
        self.vector_cache.clear()
        self._embedding_cache.clear()

    def get_cache_stats(self) -> dict:
        """Get cache statistics."""
        return {
            **self.vector_cache.get_cache_stats(),
            "embedding_cache_size": len(self._embedding_cache)
        }
