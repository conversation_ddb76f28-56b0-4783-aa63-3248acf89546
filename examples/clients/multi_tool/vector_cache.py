"""
Vector cache management for tool vectors.

Provides persistent storage of tool vectors to avoid recomputation.
"""

import asyncio
import json
import logging
import pickle
import hashlib
from pathlib import Path
from typing import Any, Dict, List, Optional


class VectorCache:
    """Manages persistent storage of tool vectors to avoid recomputation."""

    def __init__(self, cache_dir: str = ".vector_cache"):
        """Initialize vector cache with specified directory."""
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.cache_file = self.cache_dir / "tool_vectors.pkl"
        self.metadata_file = self.cache_dir / "cache_metadata.json"
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._load_cache()

    def _load_cache(self) -> None:
        """Load existing cache from disk."""
        try:
            if self.cache_file.exists():
                with open(self.cache_file, 'rb') as f:
                    self._cache = pickle.load(f)
                logging.info(f"Loaded {len(self._cache)} cached tool vectors")
            else:
                logging.info("No existing vector cache found, starting fresh")
        except Exception as e:
            logging.warning(f"Failed to load vector cache: {e}, starting fresh")
            self._cache = {}

    def _save_cache(self) -> None:
        """Save current cache to disk."""
        try:
            with open(self.cache_file, 'wb') as f:
                pickle.dump(self._cache, f)

            # Save metadata
            metadata = {
                "last_updated": asyncio.get_event_loop().time(),
                "tool_count": len(self._cache),
                "cache_version": "1.0"
            }
            with open(self.metadata_file, 'w') as f:
                json.dump(metadata, f, indent=2)

            logging.info(f"Saved {len(self._cache)} tool vectors to cache")
        except Exception as e:
            logging.error(f"Failed to save vector cache: {e}")

    def _get_tool_hash(self, tool_name: str, tool_description: str) -> str:
        """Generate a hash for tool identification."""
        content = f"{tool_name}:{tool_description}"
        return hashlib.md5(content.encode()).hexdigest()

    def get_vector(self, tool_name: str, tool_description: str) -> Optional[List[float]]:
        """Get cached vector for a tool."""
        tool_hash = self._get_tool_hash(tool_name, tool_description)
        if tool_hash in self._cache:
            logging.debug(f"Cache hit for tool: {tool_name}")
            return self._cache[tool_hash]["vector"]
        return None

    def set_vector(self, tool_name: str, tool_description: str, vector: List[float]) -> None:
        """Cache a vector for a tool."""
        tool_hash = self._get_tool_hash(tool_name, tool_description)
        self._cache[tool_hash] = {
            "tool_name": tool_name,
            "tool_description": tool_description,
            "vector": vector,
            "cached_at": asyncio.get_event_loop().time()
        }
        logging.debug(f"Cached vector for tool: {tool_name}")

    def save(self) -> None:
        """Public method to save cache."""
        self._save_cache()

    def clear(self) -> None:
        """Clear all cached vectors."""
        self._cache.clear()
        if self.cache_file.exists():
            self.cache_file.unlink()
        if self.metadata_file.exists():
            self.metadata_file.unlink()
        logging.info("Vector cache cleared")

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return {
            "total_cached_tools": len(self._cache),
            "cache_file_exists": self.cache_file.exists(),
            "metadata_file_exists": self.metadata_file.exists(),
            "cache_dir": str(self.cache_dir)
        }
