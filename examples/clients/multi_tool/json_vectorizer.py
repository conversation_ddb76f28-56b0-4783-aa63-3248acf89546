"""
JSON 配置文件向量化处理器。

将 JSON 配置文件中的服务器信息转换为向量，用于相似度搜索和工具选择。
"""

import json
import logging
import asyncio
from typing import Dict, List, Any, Optional
from pathlib import Path

try:
    from .vector_cache import VectorCache
    from .tool_vector_manager import ToolVectorManager
    from .cached_vector_store import CachedVectorStore
except ImportError:
    from vector_cache import VectorCache
    from tool_vector_manager import ToolVectorManager
    from cached_vector_store import CachedVectorStore


class ServerInfo:
    """服务器信息类，用于封装从 JSON 配置中提取的服务器信息。"""
    
    def __init__(self, server_id: str, config: Dict[str, Any]):
        self.server_id = server_id
        self.name = config.get("name", server_id)
        self.type = config.get("type", "stdio")
        self.command = config.get("command", "")
        self.args = config.get("args", [])
        self.env = config.get("env", {})
        self.disabled_tools = config.get("disabledTools", [])
        
        # 生成描述文本用于向量化
        self.description = self._generate_description()
    
    def _generate_description(self) -> str:
        """生成服务器的描述文本，用于向量化。"""
        description_parts = [
            f"服务器名称: {self.name}",
            f"服务器类型: {self.type}",
        ]
        
        # 根据服务器名称添加功能描述
        functionality_map = {
            "哔哩哔哩": "视频平台服务，提供视频搜索、播放、用户信息查询等功能",
            "图书信息查询": "图书信息检索服务，提供书籍搜索、详情查询、ISBN查询等功能",
            "计算机环境": "计算机系统环境管理，提供系统信息查询、环境变量管理等功能",
            "今日热点": "热点新闻资讯服务，提供实时热点、新闻推荐、话题追踪等功能",
            "对话框": "用户界面对话框服务，提供消息提示、确认对话、输入框等功能",
            "文档信息": "文档处理和信息提取服务，提供文档解析、内容提取、格式转换等功能",
            "企业信息": "企业信息查询服务，提供公司信息、工商数据、企业征信等功能",
            "汇率转换": "货币汇率转换服务，提供实时汇率、货币转换、汇率历史等功能",
            "文件压缩": "文件压缩和解压服务，提供文件打包、压缩、解压缩等功能",
            "文件选择": "文件选择和管理服务，提供文件浏览、选择、路径管理等功能",
            "获取位置": "地理位置服务，提供位置获取、地址解析、坐标转换等功能",
            "下班": "工作时间管理服务，提供下班提醒、时间计算、工作日历等功能",
            "图像处理": "图像处理和编辑服务，提供图片编辑、格式转换、滤镜效果等功能",
            "IP查询": "IP地址查询服务，提供IP定位、网络信息、地理位置等功能",
            "数学计算": "数学计算和公式求解服务，提供计算器、公式解析、数学运算等功能",
            "电影信息": "电影信息查询服务，提供电影搜索、影评、演员信息等功能",
            "新闻头条": "新闻资讯服务，提供头条新闻、分类资讯、新闻搜索等功能",
            "通知": "系统通知服务，提供消息推送、提醒通知、状态更新等功能",
            "程序启动": "应用程序启动服务，提供程序管理、启动控制、进程监控等功能",
            "网页打开": "网页浏览服务，提供网页打开、链接管理、浏览器控制等功能",
            "手机号码归属地": "手机号码查询服务，提供号码归属地、运营商信息、号段查询等功能",
            "二维码生成": "二维码生成和识别服务，提供二维码创建、扫描、内容编码等功能",
            "截图MCP服务器": "屏幕截图服务，提供屏幕捕获、图片保存、区域截图等功能",
            "网速测试": "网络速度测试服务，提供网速检测、延迟测试、网络诊断等功能",
            "分屏": "屏幕分屏管理服务，提供窗口分割、多屏显示、布局管理等功能",
            "系统清理": "系统清理和优化服务，提供垃圾清理、缓存清除、系统优化等功能",
            "交通信息查询": "交通信息服务，提供路况查询、公交信息、出行规划等功能",
            "12306车票查询": "火车票查询服务，提供车次查询、票价信息、余票查询等功能",
            "高德地图": "地图导航服务，提供地图显示、路线规划、位置搜索等功能",
            "怎么做菜": "烹饪指导服务，提供菜谱查询、烹饪教程、食材搭配等功能",
            "热点新闻": "热点新闻聚合服务，提供实时热点、新闻分类、话题追踪等功能"
        }
        
        if self.name in functionality_map:
            description_parts.append(f"功能描述: {functionality_map[self.name]}")
        
        # 添加环境变量信息（如果有的话）
        if self.env:
            env_keys = list(self.env.keys())
            description_parts.append(f"配置环境: {', '.join(env_keys)}")
        
        return " | ".join(description_parts)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式。"""
        return {
            "server_id": self.server_id,
            "name": self.name,
            "type": self.type,
            "command": self.command,
            "args": self.args,
            "env": self.env,
            "disabled_tools": self.disabled_tools,
            "description": self.description
        }


class JsonVectorizer:
    """JSON 配置文件向量化处理器。"""
    
    def __init__(self, 
                 embeddings_model,
                 cache_dir: str = ".json_vector_cache"):
        """
        初始化 JSON 向量化器。
        
        Args:
            embeddings_model: 嵌入模型
            cache_dir: 缓存目录
        """
        self.embeddings_model = embeddings_model
        self.cache_dir = cache_dir
        
        # 初始化向量组件
        self.vector_cache = VectorCache(cache_dir)
        self.tool_vector_manager = ToolVectorManager(embeddings_model, self.vector_cache)
        self.cached_vector_store = CachedVectorStore(self.tool_vector_manager)
        
        # 存储服务器信息
        self.servers: Dict[str, ServerInfo] = {}
        
        logging.info(f"JsonVectorizer initialized with cache dir: {cache_dir}")
    
    def load_servers_config(self, config_path: str) -> None:
        """
        加载服务器配置文件。
        
        Args:
            config_path: 配置文件路径
        """
        config_file = Path(config_path)
        if not config_file.exists():
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        # 解析服务器配置
        mcp_servers = config_data.get("mcpServers", {})
        for server_id, server_config in mcp_servers.items():
            server_info = ServerInfo(server_id, server_config)
            self.servers[server_id] = server_info
        
        logging.info(f"Loaded {len(self.servers)} servers from config")
    
    async def vectorize_servers(self) -> None:
        """
        对所有服务器进行向量化处理。
        """
        logging.info("开始向量化服务器配置...")
        
        for server_id, server_info in self.servers.items():
            try:
                # 将服务器信息添加到向量存储
                await self.cached_vector_store.add_tool(
                    tool_id=server_id,
                    tool_name=server_info.name,
                    tool_description=server_info.description
                )
                logging.info(f"向量化完成: {server_info.name}")
                
            except Exception as e:
                logging.error(f"向量化失败 {server_info.name}: {e}")
        
        # 保存缓存
        self.tool_vector_manager.save_cache()
        logging.info("服务器向量化完成，缓存已保存")
    
    async def search_servers(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """
        根据查询搜索相关服务器。
        
        Args:
            query: 查询字符串
            top_k: 返回结果数量
            
        Returns:
            相关服务器列表
        """
        # 搜索相似服务器
        similar_servers = await self.cached_vector_store.search_similar_tools(query, top_k)
        
        # 添加服务器详细信息
        results = []
        for server_result in similar_servers:
            server_id = server_result["tool_id"]
            if server_id in self.servers:
                server_info = self.servers[server_id]
                result = {
                    **server_result,
                    "server_info": server_info.to_dict()
                }
                results.append(result)
        
        return results
    
    def get_server_by_id(self, server_id: str) -> Optional[ServerInfo]:
        """根据 ID 获取服务器信息。"""
        return self.servers.get(server_id)
    
    def get_all_servers(self) -> Dict[str, ServerInfo]:
        """获取所有服务器信息。"""
        return self.servers.copy()
    
    def get_vectorization_stats(self) -> Dict[str, Any]:
        """获取向量化统计信息。"""
        cache_stats = self.tool_vector_manager.get_cache_stats()
        return {
            "total_servers": len(self.servers),
            "vectorized_servers": self.cached_vector_store.get_tool_count(),
            **cache_stats
        }
