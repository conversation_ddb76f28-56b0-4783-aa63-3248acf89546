"""
Multi-tool system integration.

Provides a complete multi-tool system with vector caching, tool selection,
and enhanced query processing capabilities.
"""

import asyncio
import logging
import uuid
from typing import Any, Dict, List, Optional

try:
    from .vector_cache import VectorCache
    from .tool_vector_manager import ToolVectorManager
    from .cached_vector_store import CachedVectorStore
    from .tool_selector import Custom<PERSON>oolSelector
except ImportError:
    # Fallback for direct execution
    from vector_cache import VectorCache
    from tool_vector_manager import Too<PERSON>VectorManager
    from cached_vector_store import CachedVectorStore
    from tool_selector import CustomToolSelector

# Try to import enhanced features
try:
    try:
        from .utils.enhanced_tool_selector import (
            EnhancedToolSelector,
            EnhancedToolResult,
            create_enhanced_tool_selector
        )
        from .utils.multi_tool_query_processor import (
            MultiToolRetriever,
            QueryComplexity
        )
        from .utils.optimized_tool_selection import (
            OptimizedToolSelector,
            SelectionStrategy
        )
    except ImportError:
        # Fallback for direct execution
        from utils.enhanced_tool_selector import (
            EnhancedToolSelector,
            EnhancedToolResult,
            create_enhanced_tool_selector
        )
        from utils.multi_tool_query_processor import (
            MultiToolRetriever,
            QueryComplexity
        )
        from utils.optimized_tool_selection import (
            OptimizedToolSelector,
            SelectionStrategy
        )
    ENHANCED_FEATURES_AVAILABLE = True
except ImportError as e:
    logging.warning(f"Enhanced features not available: {e}")
    ENHANCED_FEATURES_AVAILABLE = False


class MultiToolSystem:
    """Complete multi-tool system with caching and enhanced selection."""

    def __init__(self, 
                 embeddings_model,
                 cache_dir: str = ".vector_cache",
                 enable_enhanced_features: bool = True):
        """
        Initialize the multi-tool system.
        
        Args:
            embeddings_model: The embeddings model for vector generation
            cache_dir: Directory for vector cache storage
            enable_enhanced_features: Whether to enable enhanced tool selection
        """
        self.embeddings_model = embeddings_model
        self.cache_dir = cache_dir
        self.enable_enhanced_features = enable_enhanced_features and ENHANCED_FEATURES_AVAILABLE
        
        # Initialize core components
        self.vector_cache = VectorCache(cache_dir)
        self.tool_vector_manager = ToolVectorManager(embeddings_model, self.vector_cache)
        self.cached_vector_store = CachedVectorStore(self.tool_vector_manager)
        
        # Tool registry
        self.tool_registry: Dict[str, Any] = {}
        
        # Tool selectors
        self.custom_tool_selector: Optional[CustomToolSelector] = None
        self.enhanced_tool_selector: Optional[EnhancedToolSelector] = None
        self.optimized_tool_selector: Optional[OptimizedToolSelector] = None
        
        # Statistics
        self.stats = {
            "total_tools_added": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "queries_processed": 0
        }

    async def add_tool(self, tool, tool_name: str = None, tool_description: str = None) -> str:
        """
        Add a tool to the system.
        
        Args:
            tool: The tool object
            tool_name: Optional tool name override
            tool_description: Optional tool description override
            
        Returns:
            str: The tool ID assigned to this tool
        """
        tool_id = str(uuid.uuid4())
        
        # Extract tool information
        name = tool_name or getattr(tool, 'name', str(tool))
        description = tool_description or getattr(tool, 'description', 'No description available')
        
        # Add to registry
        self.tool_registry[tool_id] = tool
        
        # Add to vector store
        await self.cached_vector_store.add_tool(tool_id, name, description)
        
        self.stats["total_tools_added"] += 1
        logging.info(f"Added tool: {name} (ID: {tool_id})")
        
        return tool_id

    async def add_tools_batch(self, tools: List[Any]) -> List[str]:
        """
        Add multiple tools in batch.
        
        Args:
            tools: List of tool objects
            
        Returns:
            List[str]: List of tool IDs
        """
        tool_ids = []
        for tool in tools:
            tool_id = await self.add_tool(tool)
            tool_ids.append(tool_id)
        
        # Save cache after batch processing
        self.tool_vector_manager.save_cache()
        logging.info(f"Batch added {len(tools)} tools")
        
        return tool_ids

    async def initialize_selectors(self):
        """Initialize tool selectors after tools have been added."""
        # Initialize custom tool selector
        self.custom_tool_selector = CustomToolSelector(
            self.cached_vector_store, 
            self.tool_registry
        )
        
        # Initialize enhanced features if available
        if self.enable_enhanced_features:
            try:
                self.enhanced_tool_selector = await create_enhanced_tool_selector(
                    cached_store=self.cached_vector_store,
                    tool_registry=self.tool_registry,
                    multi_agent_system=None,
                    enable_multi_tool=True
                )
                logging.info("Enhanced tool selector initialized")
                
                # Initialize optimized tool selector
                try:
                    from .utils.optimization_config import optimization_config
                except ImportError:
                    from utils.optimization_config import optimization_config
                self.optimized_tool_selector = OptimizedToolSelector(
                    cached_store=self.cached_vector_store,
                    tool_registry=self.tool_registry,
                    config=optimization_config
                )
                logging.info("Optimized tool selector initialized")
                
            except Exception as e:
                logging.error(f"Failed to initialize enhanced features: {e}")
                self.enable_enhanced_features = False

    async def select_tools(self, 
                          query: str, 
                          max_tools: int = 5,
                          use_enhanced: bool = None,
                          use_multi_tool: bool = False) -> List[Any]:
        """
        Select tools based on query.
        
        Args:
            query: The query string
            max_tools: Maximum number of tools to return
            use_enhanced: Whether to use enhanced selector (None = auto)
            use_multi_tool: Whether to force multi-tool processing
            
        Returns:
            List of selected tools
        """
        self.stats["queries_processed"] += 1
        
        # Determine which selector to use
        if use_enhanced is None:
            use_enhanced = self.enable_enhanced_features
        
        if use_enhanced and self.enhanced_tool_selector:
            try:
                results = await self.enhanced_tool_selector.select_tools(
                    query=query,
                    max_tools=max_tools,
                    force_multi_tool=use_multi_tool
                )
                # Extract actual tools from enhanced results
                return [self.tool_registry.get(result.tool_id) for result in results 
                       if result.tool_id in self.tool_registry]
            except Exception as e:
                logging.error(f"Enhanced selector failed, falling back to custom: {e}")
        
        # Fall back to custom selector
        if self.custom_tool_selector:
            return await self.custom_tool_selector.select_tools(query, max_tools)
        
        logging.error("No tool selector available")
        return []

    async def get_tool_similarities(self, query: str, max_tools: int = 5) -> List[Dict[str, Any]]:
        """Get detailed tool similarity information."""
        return await self.cached_vector_store.search_similar_tools(query, max_tools)

    def get_system_stats(self) -> Dict[str, Any]:
        """Get system statistics."""
        cache_stats = self.tool_vector_manager.get_cache_stats()
        
        enhanced_stats = {}
        if self.enhanced_tool_selector:
            try:
                enhanced_stats = self.enhanced_tool_selector.get_statistics()
            except:
                pass
        
        return {
            **self.stats,
            **cache_stats,
            "enhanced_features_enabled": self.enable_enhanced_features,
            "tool_count": self.cached_vector_store.get_tool_count(),
            "registry_size": len(self.tool_registry),
            **enhanced_stats
        }

    def save_cache(self):
        """Save the vector cache to disk."""
        self.tool_vector_manager.save_cache()

    def clear_cache(self):
        """Clear the vector cache."""
        self.tool_vector_manager.clear_cache()
        self.stats["cache_hits"] = 0
        self.stats["cache_misses"] = 0

    async def cleanup(self):
        """Cleanup system resources."""
        self.save_cache()
        logging.info("Multi-tool system cleanup completed")
