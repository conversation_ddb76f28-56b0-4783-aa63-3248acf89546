"""
集成指南：如何在现有 main.py 中添加 JSON 向量化功能。

这个文件展示了如何修改现有的 main.py 来集成 JSON 配置向量化功能。
"""

# ============================================================================
# 第一步：在 main.py 开头添加导入
# ============================================================================

# 在现有导入后添加：
"""
# 添加 JSON 向量化支持
try:
    from examples.clients.multi_tool.json_vectorizer import JsonVectorizer
    from examples.clients.multi_tool.multi_tool_system import MultiToolSystem
    JSON_VECTORIZATION_AVAILABLE = True
except ImportError as e:
    logging.warning(f"JSON vectorization not available: {e}")
    JSON_VECTORIZATION_AVAILABLE = False
"""

# ============================================================================
# 第二步：在 ChatSession 类中添加 JSON 向量化支持
# ============================================================================

class ChatSessionWithJsonVectorization:
    """
    在现有 ChatSession 类中添加的代码示例。
    """
    
    def __init__(self, servers, llm_client, config_path="servers_config.json"):
        # 现有的初始化代码...
        
        # 添加 JSON 向量化支持
        self.config_path = config_path
        self.json_vectorizer = None
        self.json_vectorization_enabled = JSON_VECTORIZATION_AVAILABLE
        
        if self.json_vectorization_enabled:
            logging.info("🚀 JSON vectorization enabled")
        else:
            logging.info("⚠️ JSON vectorization disabled")
    
    async def initialize_json_vectorization(self, embeddings_model):
        """
        初始化 JSON 向量化功能。
        在现有的 start() 方法中调用此方法。
        """
        if not self.json_vectorization_enabled:
            return
        
        try:
            logging.info("🔄 Initializing JSON vectorization...")
            
            # 创建 JSON 向量化器
            self.json_vectorizer = JsonVectorizer(
                embeddings_model=embeddings_model,
                cache_dir=".servers_vector_cache"
            )
            
            # 加载服务器配置
            from pathlib import Path
            if Path(self.config_path).exists():
                self.json_vectorizer.load_servers_config(self.config_path)
                
                # 向量化服务器配置
                await self.json_vectorizer.vectorize_servers()
                
                servers_count = len(self.json_vectorizer.get_all_servers())
                logging.info(f"✅ JSON vectorization completed for {servers_count} servers")
            else:
                logging.warning(f"Config file not found: {self.config_path}")
                
        except Exception as e:
            logging.error(f"Failed to initialize JSON vectorization: {e}")
            self.json_vectorization_enabled = False
    
    async def search_servers_by_query(self, query: str, top_k: int = 5):
        """
        根据查询搜索相关服务器。
        """
        if not self.json_vectorization_enabled or not self.json_vectorizer:
            return []
        
        try:
            results = await self.json_vectorizer.search_servers(query, top_k)
            return results
        except Exception as e:
            logging.error(f"Server search failed: {e}")
            return []
    
    def _handle_server_search_command(self):
        """
        处理服务器搜索命令。
        在现有的命令处理中添加此方法。
        """
        if not self.json_vectorization_enabled:
            print("❌ JSON 向量化功能未启用")
            return
        
        print("\n🔍 服务器搜索")
        print("=" * 30)
        
        query = input("请输入搜索查询: ").strip()
        if not query:
            print("❌ 查询不能为空")
            return
        
        try:
            # 使用 asyncio 运行异步搜索
            import asyncio
            results = asyncio.run(self.search_servers_by_query(query, top_k=5))
            
            if results:
                print(f"\n📊 找到 {len(results)} 个相关服务器:")
                for i, result in enumerate(results, 1):
                    server_info = result["server_info"]
                    similarity = result["similarity"]
                    print(f"\n{i}. {server_info['name']} (相似度: {similarity:.3f})")
                    print(f"   描述: {server_info['description'][:100]}...")
                    print(f"   类型: {server_info['type']}")
                    if server_info['env']:
                        print(f"   环境变量: {list(server_info['env'].keys())}")
            else:
                print("❌ 未找到相关服务器")
                
        except Exception as e:
            print(f"❌ 搜索失败: {e}")


# ============================================================================
# 第三步：在 start() 方法中集成向量化
# ============================================================================

async def enhanced_start_method():
    """
    增强的 start() 方法示例，展示如何集成 JSON 向量化。
    """
    
    # 现有的服务器初始化代码...
    # for server in self.servers:
    #     await server.initialize()
    
    # 现有的工具加载代码...
    # all_tools = []
    # for server in self.servers:
    #     tools = await server.list_tools()
    #     all_tools.extend(tools)
    
    # 现有的嵌入模型初始化...
    # embeddings = init_embeddings("openai:text-embedding-3-large")
    
    # 添加 JSON 向量化初始化
    # await self.initialize_json_vectorization(embeddings)
    
    # 现有的其他初始化代码...
    pass


# ============================================================================
# 第四步：在命令处理中添加新命令
# ============================================================================

def enhanced_command_handling():
    """
    在现有的命令处理循环中添加新命令。
    """
    
    # 在现有的命令列表中添加：
    commands = [
        "'quit'/'exit'", 
        "'clear_cache'", 
        "'weights'", 
        "'performance'", 
        "'config'",
        "'search_servers'"  # 新增命令
    ]
    
    # 在命令处理的 if-elif 链中添加：
    """
    elif user_input.strip().lower() == 'search_servers':
        self._handle_server_search_command()
    """


# ============================================================================
# 第五步：完整的集成示例
# ============================================================================

class CompleteIntegrationExample:
    """
    完整的集成示例，展示如何在现有代码中添加所有功能。
    """
    
    def __init__(self):
        # 现有初始化...
        self.json_vectorization_enabled = JSON_VECTORIZATION_AVAILABLE
        self.json_vectorizer = None
    
    async def start(self):
        """完整的启动方法示例。"""
        
        print("🚀 启动增强聊天会话...")
        
        # 1. 现有的服务器初始化
        # ... 现有代码 ...
        
        # 2. 现有的工具加载
        # ... 现有代码 ...
        
        # 3. 现有的嵌入模型初始化
        # embeddings = init_embeddings("openai:text-embedding-3-large")
        
        # 4. 新增：JSON 向量化初始化
        # await self.initialize_json_vectorization(embeddings)
        
        # 5. 现有的其他初始化
        # ... 现有代码 ...
        
        # 6. 主循环
        await self.main_loop()
    
    async def main_loop(self):
        """主循环示例。"""
        
        while True:
            # 显示可用命令（包括新命令）
            commands = ["'quit'", "'search_servers'", "其他现有命令..."]
            print(f"Commands: {', '.join(commands)}")
            
            user_input = input("\n👤 You: ").strip()
            
            if user_input.lower() in ['quit', 'exit']:
                break
            elif user_input.lower() == 'search_servers':
                self._handle_server_search_command()
            # ... 其他现有命令处理 ...
            else:
                # 现有的查询处理，可以增强为同时搜索服务器和工具
                await self.enhanced_query_processing(user_input)
    
    async def enhanced_query_processing(self, query: str):
        """
        增强的查询处理，结合服务器搜索和工具选择。
        """
        
        # 1. 现有的工具选择逻辑
        # selected_tools = await self.select_tools(query)
        
        # 2. 新增：搜索相关服务器
        relevant_servers = await self.search_servers_by_query(query, top_k=3)
        
        # 3. 显示结果
        if relevant_servers:
            print(f"\n🏢 相关服务器:")
            for server in relevant_servers[:3]:
                server_info = server["server_info"]
                print(f"  • {server_info['name']} (相似度: {server['similarity']:.3f})")
        
        # 4. 继续现有的处理流程
        # ... 现有的 LLM 调用和工具执行 ...


# ============================================================================
# 使用说明
# ============================================================================

"""
集成步骤总结：

1. 安装依赖：
   - 确保 examples/clients/multi_tool 目录中的文件可用
   - 检查 numpy, langchain 等依赖

2. 修改现有 main.py：
   - 添加导入语句
   - 在 ChatSession.__init__ 中添加 JSON 向量化支持
   - 在 start() 方法中调用 initialize_json_vectorization()
   - 添加新的命令处理方法

3. 配置文件：
   - 确保 servers_config.json 文件存在
   - 或者使用自定义配置文件路径

4. 测试：
   - 运行修改后的 main.py
   - 使用 'search_servers' 命令测试功能
   - 观察启动时的向量化过程

5. 优化：
   - 根据需要调整缓存目录
   - 配置相似度权重
   - 添加更多自定义功能

注意事项：
- 首次运行会生成向量缓存，可能需要一些时间
- 后续运行会使用缓存，启动更快
- 可以通过 'clear_cache' 命令清除缓存重新生成
"""
