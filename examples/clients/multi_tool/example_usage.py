"""
Example usage of the multi-tool system.

Demonstrates how to use the multi-tool system for tool selection and caching.
"""

import asyncio
import logging
from typing import Any, List

# Configure logging
logging.basicConfig(level=logging.INFO)

# Example tool class for demonstration
class ExampleTool:
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
    
    def __str__(self):
        return f"Tool({self.name})"


async def example_usage():
    """Example of how to use the multi-tool system."""
    
    # Import the multi-tool system
    from multi_tool_system import MultiToolSystem
    
    # Mock embeddings model for demonstration
    class MockEmbeddingsModel:
        async def aembed_query(self, text: str) -> List[float]:
            # Simple mock embedding based on text hash
            import hashlib
            hash_obj = hashlib.md5(text.encode())
            # Convert hash to list of floats (384 dimensions for example)
            hash_bytes = hash_obj.digest()
            embedding = []
            for i in range(0, len(hash_bytes), 4):
                chunk = hash_bytes[i:i+4]
                if len(chunk) == 4:
                    # Convert 4 bytes to float
                    val = int.from_bytes(chunk, 'big') / (2**32)
                    embedding.append(val)
            
            # Pad to 384 dimensions
            while len(embedding) < 384:
                embedding.append(0.0)
            
            return embedding[:384]
    
    # Initialize the system
    embeddings_model = MockEmbeddingsModel()
    multi_tool_system = MultiToolSystem(
        embeddings_model=embeddings_model,
        cache_dir=".example_cache",
        enable_enhanced_features=False  # Disable for this example
    )
    
    # Create example tools
    tools = [
        ExampleTool("file_reader", "Read and process text files"),
        ExampleTool("web_scraper", "Scrape content from web pages"),
        ExampleTool("data_analyzer", "Analyze and visualize data"),
        ExampleTool("email_sender", "Send emails with attachments"),
        ExampleTool("image_processor", "Process and edit images"),
        ExampleTool("pdf_generator", "Generate PDF documents"),
        ExampleTool("database_query", "Query SQL databases"),
        ExampleTool("api_client", "Make HTTP API requests"),
    ]
    
    print("🚀 Adding tools to the system...")
    
    # Add tools to the system
    tool_ids = await multi_tool_system.add_tools_batch(tools)
    print(f"✅ Added {len(tool_ids)} tools")
    
    # Initialize selectors
    await multi_tool_system.initialize_selectors()
    print("✅ Selectors initialized")
    
    # Example queries
    queries = [
        "I need to read a text file",
        "Help me scrape a website",
        "Analyze some data and create charts",
        "Send an email with a PDF attachment",
        "Process an image and save it",
        "Query a database for user information"
    ]
    
    print("\n🔍 Testing tool selection...")
    
    for query in queries:
        print(f"\nQuery: '{query}'")
        
        # Select tools
        selected_tools = await multi_tool_system.select_tools(
            query=query,
            max_tools=3
        )
        
        print(f"Selected tools:")
        for i, tool in enumerate(selected_tools, 1):
            print(f"  {i}. {tool.name}: {tool.description}")
        
        # Get detailed similarities
        similarities = await multi_tool_system.get_tool_similarities(query, max_tools=3)
        print(f"Similarity scores:")
        for sim in similarities:
            tool_data = sim['tool_data']
            print(f"  {tool_data['name']}: {sim['similarity']:.3f}")
    
    # Show system statistics
    print("\n📊 System Statistics:")
    stats = multi_tool_system.get_system_stats()
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    # Save cache
    multi_tool_system.save_cache()
    print("\n💾 Cache saved")
    
    # Cleanup
    await multi_tool_system.cleanup()
    print("🧹 Cleanup completed")


async def test_caching():
    """Test caching functionality."""
    print("\n🧪 Testing caching functionality...")
    
    from multi_tool_system import MultiToolSystem
    
    # Mock embeddings model
    class MockEmbeddingsModel:
        def __init__(self):
            self.call_count = 0
        
        async def aembed_query(self, text: str) -> List[float]:
            self.call_count += 1
            print(f"  Embeddings call #{self.call_count} for: {text[:50]}...")
            # Simple deterministic embedding
            return [float(ord(c)) for c in text[:384].ljust(384, ' ')]
    
    embeddings_model = MockEmbeddingsModel()
    
    # First run - should generate embeddings
    print("\n1️⃣ First run (should generate embeddings):")
    system1 = MultiToolSystem(embeddings_model, ".test_cache", False)
    
    tool = ExampleTool("test_tool", "A test tool for caching")
    await system1.add_tool(tool)
    await system1.initialize_selectors()
    
    result1 = await system1.select_tools("test query", 1)
    print(f"Embeddings calls: {embeddings_model.call_count}")
    
    system1.save_cache()
    await system1.cleanup()
    
    # Second run - should use cache
    print("\n2️⃣ Second run (should use cache):")
    embeddings_model.call_count = 0  # Reset counter
    
    system2 = MultiToolSystem(embeddings_model, ".test_cache", False)
    await system2.add_tool(tool)
    await system2.initialize_selectors()
    
    result2 = await system2.select_tools("test query", 1)
    print(f"Embeddings calls: {embeddings_model.call_count}")
    
    await system2.cleanup()
    
    print("✅ Caching test completed")


if __name__ == "__main__":
    print("🎯 Multi-Tool System Example")
    print("=" * 40)
    
    # Run the main example
    asyncio.run(example_usage())
    
    # Test caching
    asyncio.run(test_caching())
