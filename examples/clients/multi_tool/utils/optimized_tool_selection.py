#!/usr/bin/env python3
"""
优化的工具选择系统
Optimized Tool Selection System

结合标准混合相似度和增强Agent系统的优势，提供更好的工具选择效果。
"""

import asyncio
import logging
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

# 导入相似度配置
try:
    from .similarity_config import SimilarityConfig
except ImportError:
    # Fallback configuration
    class SimilarityConfig:
        SEMANTIC_WEIGHT = 0.6
        KEYWORD_WEIGHT = 0.3
        NAME_MATCH_WEIGHT = 0.1
        STOP_WORDS = set()
        MIN_WORD_LENGTH = 2

# 导入优化配置
try:
    from .optimization_config import optimization_config
except ImportError:
    # Fallback if config not available
    class MockOptimizationConfig:
        def get(self, key, default=None):
            defaults = {
                "adaptation_threshold": 0.1,
                "min_calls_for_adaptation": 10,
                "success_rate_weight": 0.7,
                "speed_weight": 0.3
            }
            return defaults.get(key, default)
    optimization_config = MockOptimizationConfig()


class SelectionStrategy(Enum):
    """工具选择策略"""
    HYBRID_ONLY = "hybrid_only"  # 仅使用混合相似度
    AGENT_ONLY = "agent_only"    # 仅使用Agent系统
    ADAPTIVE = "adaptive"        # 自适应选择
    ENSEMBLE = "ensemble"        # 集成方法


@dataclass
class ToolSelectionResult:
    """工具选择结果"""
    tool_id: str
    tool_name: str
    description: str
    confidence: float
    selection_method: str
    reasoning: str
    match_details: Dict[str, Any]
    execution_time: float


class OptimizedToolSelector:
    """优化的工具选择器"""
    
    def __init__(self, 
                 cached_store,
                 multi_agent_system=None,
                 strategy: SelectionStrategy = SelectionStrategy.ADAPTIVE):
        """
        初始化优化的工具选择器
        
        Args:
            cached_store: 缓存存储系统
            multi_agent_system: 多Agent系统（可选）
            strategy: 选择策略
        """
        self.cached_store = cached_store
        self.multi_agent_system = multi_agent_system
        self.strategy = strategy
        
        # 性能统计
        self.performance_stats = {
            "hybrid_calls": 0,
            "agent_calls": 0,
            "hybrid_avg_time": 0.0,
            "agent_avg_time": 0.0,
            "hybrid_success_rate": 0.0,
            "agent_success_rate": 0.0
        }
        
        # 从配置获取自适应参数
        self.adaptation_threshold = optimization_config.get("adaptation_threshold", 0.1)
        self.min_calls_for_adaptation = optimization_config.get("min_calls_for_adaptation", 10)

        # 从配置获取性能权重
        self.success_rate_weight = optimization_config.get("success_rate_weight", 0.7)
        self.speed_weight = optimization_config.get("speed_weight", 0.3)
        
    async def select_tools(self, 
                          query: str, 
                          user_id: str = None,
                          top_k: int = 10,
                          context: Dict[str, Any] = None) -> List[ToolSelectionResult]:
        """
        选择相关工具
        
        Args:
            query: 用户查询
            user_id: 用户ID
            top_k: 返回工具数量
            context: 上下文信息
            
        Returns:
            工具选择结果列表
        """
        start_time = time.time()
        
        # 根据策略选择方法
        if self.strategy == SelectionStrategy.HYBRID_ONLY:
            results = await self._select_with_hybrid(query, top_k)
            
        elif self.strategy == SelectionStrategy.AGENT_ONLY:
            results = await self._select_with_agent(query, user_id, top_k)
            
        elif self.strategy == SelectionStrategy.ADAPTIVE:
            results = await self._select_adaptive(query, user_id, top_k)
            
        elif self.strategy == SelectionStrategy.ENSEMBLE:
            results = await self._select_ensemble(query, user_id, top_k)
            
        else:
            # 默认使用混合方法
            results = await self._select_with_hybrid(query, top_k)
        
        execution_time = time.time() - start_time
        
        # 更新执行时间
        for result in results:
            result.execution_time = execution_time / len(results) if results else execution_time
            
        return results
    
    async def _select_with_hybrid(self, query: str, top_k: int) -> List[ToolSelectionResult]:
        """使用混合相似度选择工具"""
        start_time = time.time()
        
        try:
            similar_tools = await self.cached_store.search_similar_tools(query, top_k)
            
            results = []
            for tool_info in similar_tools:
                result = ToolSelectionResult(
                    tool_id=tool_info["tool_id"],
                    tool_name=tool_info["tool_data"]["name"],
                    description=tool_info["tool_data"]["description"],
                    confidence=tool_info["similarity"],
                    selection_method="hybrid_similarity",
                    reasoning=f"Hybrid similarity score: {tool_info['similarity']:.3f}",
                    match_details=tool_info.get("match_details", {}),
                    execution_time=0.0  # 将在外部设置
                )
                results.append(result)
            
            # 更新性能统计
            self._update_performance_stats("hybrid", time.time() - start_time, len(results) > 0)
            
            return results
            
        except Exception as e:
            logging.error(f"Hybrid selection failed: {e}")
            return []
    
    async def _select_with_agent(self, query: str, user_id: str, top_k: int) -> List[ToolSelectionResult]:
        """使用Agent系统选择工具"""
        if not self.multi_agent_system:
            logging.warning("Multi-agent system not available, falling back to hybrid")
            return await self._select_with_hybrid(query, top_k)
        
        start_time = time.time()
        
        try:
            candidates = await self.multi_agent_system.retrieve_tools_with_agents(
                query, user_id or "default", top_k=top_k
            )
            
            results = []
            for candidate in candidates:
                result = ToolSelectionResult(
                    tool_id=candidate.tool_id,
                    tool_name=candidate.tool_name,
                    description=candidate.description,
                    confidence=candidate.confidence,
                    selection_method="multi_agent",
                    reasoning=candidate.reasoning,
                    match_details=candidate.match_details,
                    execution_time=0.0  # 将在外部设置
                )
                results.append(result)
            
            # 更新性能统计
            self._update_performance_stats("agent", time.time() - start_time, len(results) > 0)
            
            return results
            
        except Exception as e:
            logging.error(f"Agent selection failed: {e}")
            # 回退到混合方法
            return await self._select_with_hybrid(query, top_k)
    
    async def _select_adaptive(self, query: str, user_id: str, top_k: int) -> List[ToolSelectionResult]:
        """自适应选择最佳方法"""
        
        # 如果调用次数不足，使用混合方法
        total_calls = self.performance_stats["hybrid_calls"] + self.performance_stats["agent_calls"]
        if total_calls < self.min_calls_for_adaptation:
            return await self._select_with_hybrid(query, top_k)
        
        # 比较性能指标
        hybrid_score = self._calculate_method_score("hybrid")
        agent_score = self._calculate_method_score("agent")
        
        # 选择更好的方法
        if agent_score > hybrid_score + self.adaptation_threshold:
            logging.info(f"Adaptive selection: using agent (score: {agent_score:.3f} vs {hybrid_score:.3f})")
            return await self._select_with_agent(query, user_id, top_k)
        else:
            logging.info(f"Adaptive selection: using hybrid (score: {hybrid_score:.3f} vs {agent_score:.3f})")
            return await self._select_with_hybrid(query, top_k)
    
    async def _select_ensemble(self, query: str, user_id: str, top_k: int) -> List[ToolSelectionResult]:
        """集成多种方法的结果"""
        
        # 并行执行两种方法
        hybrid_task = self._select_with_hybrid(query, top_k)
        agent_task = self._select_with_agent(query, user_id, top_k) if self.multi_agent_system else None
        
        # 等待结果
        if agent_task:
            hybrid_results, agent_results = await asyncio.gather(hybrid_task, agent_task, return_exceptions=True)
        else:
            hybrid_results = await hybrid_task
            agent_results = []
        
        # 处理异常
        if isinstance(hybrid_results, Exception):
            hybrid_results = []
        if isinstance(agent_results, Exception):
            agent_results = []
        
        # 合并和去重结果
        combined_results = self._merge_results(hybrid_results, agent_results, top_k)
        
        return combined_results
    
    def _merge_results(self, 
                      hybrid_results: List[ToolSelectionResult], 
                      agent_results: List[ToolSelectionResult],
                      top_k: int) -> List[ToolSelectionResult]:
        """合并两种方法的结果"""
        
        # 创建工具ID到结果的映射
        tool_map = {}
        
        # 添加混合结果
        for result in hybrid_results:
            tool_map[result.tool_id] = result
        
        # 合并Agent结果
        for result in agent_results:
            if result.tool_id in tool_map:
                # 工具已存在，合并置信度
                existing = tool_map[result.tool_id]
                combined_confidence = (existing.confidence + result.confidence) / 2
                existing.confidence = combined_confidence
                existing.selection_method = "ensemble"
                existing.reasoning = f"Hybrid: {existing.reasoning}; Agent: {result.reasoning}"
            else:
                # 新工具，直接添加
                result.selection_method = "ensemble"
                tool_map[result.tool_id] = result
        
        # 按置信度排序并返回top_k
        sorted_results = sorted(tool_map.values(), key=lambda x: x.confidence, reverse=True)
        return sorted_results[:top_k]
    
    def _calculate_method_score(self, method: str) -> float:
        """计算方法的综合得分"""
        if method == "hybrid":
            success_rate = self.performance_stats["hybrid_success_rate"]
            avg_time = self.performance_stats["hybrid_avg_time"]
        else:  # agent
            success_rate = self.performance_stats["agent_success_rate"]
            avg_time = self.performance_stats["agent_avg_time"]
        
        # 使用配置的权重计算综合得分
        if avg_time > 0:
            time_score = 1.0 / (1.0 + avg_time)  # 时间越短得分越高
        else:
            time_score = 1.0

        return self.success_rate_weight * success_rate + self.speed_weight * time_score
    
    def _update_performance_stats(self, method: str, execution_time: float, success: bool):
        """更新性能统计"""
        if method == "hybrid":
            self.performance_stats["hybrid_calls"] += 1
            # 更新平均时间
            old_avg = self.performance_stats["hybrid_avg_time"]
            calls = self.performance_stats["hybrid_calls"]
            self.performance_stats["hybrid_avg_time"] = (old_avg * (calls - 1) + execution_time) / calls
            
            # 更新成功率
            old_success_rate = self.performance_stats["hybrid_success_rate"]
            self.performance_stats["hybrid_success_rate"] = (old_success_rate * (calls - 1) + (1.0 if success else 0.0)) / calls
            
        else:  # agent
            self.performance_stats["agent_calls"] += 1
            # 更新平均时间
            old_avg = self.performance_stats["agent_avg_time"]
            calls = self.performance_stats["agent_calls"]
            self.performance_stats["agent_avg_time"] = (old_avg * (calls - 1) + execution_time) / calls
            
            # 更新成功率
            old_success_rate = self.performance_stats["agent_success_rate"]
            self.performance_stats["agent_success_rate"] = (old_success_rate * (calls - 1) + (1.0 if success else 0.0)) / calls
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        return {
            "strategy": self.strategy.value,
            "performance_stats": self.performance_stats.copy(),
            "hybrid_score": self._calculate_method_score("hybrid"),
            "agent_score": self._calculate_method_score("agent"),
            "recommendation": self._get_strategy_recommendation()
        }
    
    def _get_strategy_recommendation(self) -> str:
        """获取策略推荐"""
        total_calls = self.performance_stats["hybrid_calls"] + self.performance_stats["agent_calls"]
        
        if total_calls < self.min_calls_for_adaptation:
            return "需要更多数据来评估性能"
        
        hybrid_score = self._calculate_method_score("hybrid")
        agent_score = self._calculate_method_score("agent")
        
        if agent_score > hybrid_score + self.adaptation_threshold:
            return "推荐使用Agent系统"
        elif hybrid_score > agent_score + self.adaptation_threshold:
            return "推荐使用混合相似度"
        else:
            return "两种方法性能相近，推荐使用集成方法"
