# Multi-Tool System

这是一个从 `main.py` 迁移出来的多工具系统，提供基于向量的工具选择和缓存功能。

## 功能特性

- **向量缓存**: 持久化存储工具向量，避免重复计算
- **智能工具选择**: 基于语义相似度、关键词匹配和名称匹配的混合算法
- **增强工具选择器**: 支持多工具查询处理（可选）
- **性能优化**: 缓存机制和优化的相似度计算
- **可配置权重**: 支持调整不同相似度算法的权重

## 目录结构

```
multi_tool/
├── __init__.py                 # 包初始化
├── vector_cache.py            # 向量缓存管理
├── tool_vector_manager.py     # 工具向量管理器
├── cached_vector_store.py     # 缓存向量存储
├── tool_selector.py           # 自定义工具选择器
├── multi_tool_system.py       # 主要系统集成
├── example_usage.py           # 使用示例
├── README.md                  # 说明文档
└── utils/                     # 工具模块
    ├── __init__.py
    ├── enhanced_tool_selector.py      # 增强工具选择器
    ├── multi_tool_query_processor.py  # 多工具查询处理器
    ├── similarity_config.py           # 相似度配置
    ├── optimization_config.py         # 优化配置
    └── optimized_tool_selection.py    # 优化工具选择
```

## 核心组件

### 1. VectorCache
管理工具向量的持久化存储，避免重复计算向量。

```python
from multi_tool import VectorCache

cache = VectorCache(".vector_cache")
vector = cache.get_vector("tool_name", "tool_description")
if vector is None:
    # 生成新向量并缓存
    cache.set_vector("tool_name", "tool_description", new_vector)
cache.save()
```

### 2. ToolVectorManager
管理工具向量的生成和缓存。

```python
from multi_tool import ToolVectorManager, VectorCache

cache = VectorCache()
manager = ToolVectorManager(embeddings_model, cache)
vector = await manager.get_tool_vector("tool_name", "description")
```

### 3. CachedVectorStore
提供基于缓存向量的工具相似度搜索。

```python
from multi_tool import CachedVectorStore

store = CachedVectorStore(tool_vector_manager)
await store.add_tool("tool_id", "tool_name", "description")
results = await store.search_similar_tools("query", top_k=5)
```

### 4. MultiToolSystem
完整的多工具系统集成。

```python
from multi_tool import MultiToolSystem

system = MultiToolSystem(embeddings_model)
await system.add_tools_batch(tools)
await system.initialize_selectors()
selected_tools = await system.select_tools("query", max_tools=5)
```

## 使用示例

### 基本使用

```python
import asyncio
from multi_tool import MultiToolSystem

async def main():
    # 初始化系统
    system = MultiToolSystem(embeddings_model)
    
    # 添加工具
    tools = [...]  # 你的工具列表
    await system.add_tools_batch(tools)
    
    # 初始化选择器
    await system.initialize_selectors()
    
    # 选择工具
    selected = await system.select_tools("我需要处理文件", max_tools=3)
    
    # 获取详细相似度信息
    similarities = await system.get_tool_similarities("处理图片", max_tools=5)
    
    # 保存缓存
    system.save_cache()

asyncio.run(main())
```

### 配置相似度权重

```python
from multi_tool.utils.similarity_config import SimilarityConfig

# 查看当前权重
weights = SimilarityConfig.get_weights()
print(weights)

# 设置自定义权重
SimilarityConfig.set_weights(
    semantic=0.8,    # 语义相似度权重
    keyword=0.15,    # 关键词匹配权重
    name_match=0.05  # 名称匹配权重
)

# 应用预设
SimilarityConfig.apply_preset('semantic_focused')
```

### 使用增强功能

```python
# 启用增强功能
system = MultiToolSystem(
    embeddings_model,
    enable_enhanced_features=True
)

# 使用多工具查询
selected = await system.select_tools(
    "分析数据并生成报告",
    max_tools=10,
    use_multi_tool=True
)
```

## 迁移说明

这个系统从原来的 `main.py` 中迁移了以下组件：

### 已迁移的核心功能
- ✅ `VectorCache` - 向量缓存管理
- ✅ `ToolVectorManager` - 工具向量管理器
- ✅ `CachedVectorStore` - 缓存向量存储
- ✅ `CustomToolSelector` - 自定义工具选择器
- ✅ 混合相似度计算（语义+关键词+名称匹配）
- ✅ 向量缓存持久化
- ✅ 工具选择逻辑

### 已迁移的增强功能
- ✅ `EnhancedToolSelector` - 增强工具选择器
- ✅ `MultiToolRetriever` - 多工具检索器
- ✅ `OptimizedToolSelector` - 优化工具选择器
- ✅ 相似度配置管理
- ✅ 优化配置管理

### 未迁移的功能（按要求排除）
- ❌ 阿里云相关功能 (`AliyunEnhancedRetrieval`, `AliyunTextReranker` 等)
- ❌ 阿里云配置 (`aliyun_config`)
- ❌ 阿里云 API 调用相关代码

## 依赖要求

```python
# 核心依赖
numpy
langchain
langchain-core

# 可选依赖（用于增强功能）
langgraph
```

## 性能特性

- **缓存机制**: 向量计算结果持久化存储
- **批量处理**: 支持批量添加工具
- **混合相似度**: 结合多种算法提高匹配准确性
- **可配置权重**: 根据使用场景调整算法权重
- **统计信息**: 提供详细的性能统计

## 注意事项

1. 首次使用时会生成向量并缓存，后续使用会直接从缓存读取
2. 缓存文件默认存储在 `.vector_cache` 目录
3. 增强功能需要额外的依赖，如果不可用会自动降级到基础功能
4. 建议定期保存缓存以避免数据丢失

## 运行示例

```bash
cd examples/clients/multi_tool
python example_usage.py
```

这将运行一个完整的示例，展示如何使用多工具系统进行工具选择和缓存。
