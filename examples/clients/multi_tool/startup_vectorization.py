"""
启动时 JSON 配置向量化示例。

展示如何在程序启动时对 JSON 配置文件进行向量化处理。
"""

import asyncio
import logging
import time
from typing import List

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

try:
    from json_vectorizer import JsonVectorizer, ServerInfo
except ImportError:
    from .json_vectorizer import JsonVectorizer, ServerInfo


class MockEmbeddingsModel:
    """模拟的嵌入模型，用于演示。"""
    
    def __init__(self):
        self.call_count = 0
    
    async def aembed_query(self, text: str) -> List[float]:
        """生成模拟的嵌入向量。"""
        self.call_count += 1
        
        # 基于文本内容生成确定性的向量
        import hashlib
        hash_obj = hashlib.sha256(text.encode('utf-8'))
        hash_bytes = hash_obj.digest()
        
        # 转换为 384 维向量
        vector = []
        for i in range(0, len(hash_bytes), 4):
            chunk = hash_bytes[i:i+4]
            if len(chunk) == 4:
                val = int.from_bytes(chunk, 'big') / (2**32)
                vector.append(val)
        
        # 填充到 384 维
        while len(vector) < 384:
            vector.append(0.0)
        
        return vector[:384]


async def startup_vectorization_demo():
    """启动时向量化演示。"""
    
    print("🚀 启动 JSON 配置向量化演示")
    print("=" * 50)
    
    # 1. 初始化嵌入模型
    print("\n📊 初始化嵌入模型...")
    embeddings_model = MockEmbeddingsModel()
    
    # 2. 创建 JSON 向量化器
    print("🔧 创建 JSON 向量化器...")
    vectorizer = JsonVectorizer(
        embeddings_model=embeddings_model,
        cache_dir=".servers_vector_cache"
    )
    
    # 3. 加载服务器配置
    print("📁 加载服务器配置...")

    try:
        vectorizer.load_servers_config("servers_config.json")
        servers = vectorizer.get_all_servers()
        print(f"✅ 成功加载 {len(servers)} 个服务器配置")
        
        # 显示前几个服务器
        print("\n📋 服务器列表预览:")
        for i, (server_id, server_info) in enumerate(list(servers.items())[:5]):
            print(f"  {i+1}. {server_info.name} ({server_id})")
        if len(servers) > 5:
            print(f"  ... 还有 {len(servers) - 5} 个服务器")
            
    except FileNotFoundError:
        print("❌ 配置文件不存在，创建示例配置...")
        # 创建示例配置用于演示
        await create_example_config(vectorizer)
    
    # 4. 执行向量化
    print(f"\n🔄 开始向量化处理...")
    start_time = time.time()
    
    await vectorizer.vectorize_servers()
    
    end_time = time.time()
    processing_time = end_time - start_time
    
    print(f"✅ 向量化完成，耗时: {processing_time:.2f}s")
    print(f"📊 嵌入模型调用次数: {embeddings_model.call_count}")
    
    # 5. 显示统计信息
    stats = vectorizer.get_vectorization_stats()
    print(f"\n📈 向量化统计:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    # 6. 测试搜索功能
    print(f"\n🔍 测试服务器搜索功能...")

    print(f"\n🎉 启动向量化演示完成!")


async def create_example_config(vectorizer: JsonVectorizer):
    """创建示例配置用于演示。"""
    example_servers = {
        "文件处理": {
            "name": "文件处理",
            "type": "stdio", 
            "command": "node",
            "args": ["/path/to/file-processor.js"],
            "disabledTools": []
        },
        "图像编辑": {
            "name": "图像编辑",
            "type": "stdio",
            "command": "python",
            "args": ["/path/to/image-editor.py"],
            "disabledTools": []
        },
        "数据分析": {
            "name": "数据分析", 
            "type": "stdio",
            "command": "python",
            "args": ["/path/to/data-analyzer.py"],
            "disabledTools": []
        }
    }
    
    for server_id, config in example_servers.items():
        server_info = ServerInfo(server_id, config)
        vectorizer.servers[server_id] = server_info
    
    print(f"✅ 创建了 {len(example_servers)} 个示例服务器配置")




if __name__ == "__main__":
    print("🎯 JSON 配置向量化启动演示")
    print("=" * 50)
    
    # 运行主演示
    asyncio.run(startup_vectorization_demo())
    

